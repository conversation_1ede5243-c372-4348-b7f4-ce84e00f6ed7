project: monorepo
env:
  packageManager: pnpm # 指定包管理，可不配置，默认 npm@6.14.18
  node: 18 # 指定 node 版本，可不配置，默认 14
# 对应包名，就是package.json里的name名称，如果是带 @ 前缀的包名，请写成 package~@xx，因为yml文件开头不能以 @ 开始，格式会错误

package~@blm/bi-lego-sdk:
  stage:
    dev,sit,daily,daily2:
      build:
        # - npm install # 看具体情况，默认会在项目根目录使用包管理install依赖，如果子包里要单独install，则自己安装
        - npm run build # 以下都是自己的打包 & 发布命令
        - npm publish --registry=http://npm.bailongma-inc.com/ --tag=beta
    pre2,pre:
      build: # 脚本同上
        # - npm install
        - npm run build
        - npm publish --registry=http://npm.bailongma-inc.com/ --tag=rc
    publish:
      build:
        # - npm install
        - npm run build
        - npm publish --registry=http://npm.bailongma-inc.com/
