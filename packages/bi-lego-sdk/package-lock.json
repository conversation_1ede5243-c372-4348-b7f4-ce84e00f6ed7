{"name": "@blm/bi-lego-sdk", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@blm/bi-lego-sdk", "version": "1.0.0", "dependencies": {"@alilc/lowcode-plugin-inject": "^1.2.3", "@alilc/lowcode-react-renderer": "^1.2.5", "@alilc/lowcode-types": "^1.2.2", "@alilc/lowcode-utils": "^1.2.3", "@ant-design/icons": "^5.2.6", "@blmcp/peento-businessComponents": "0.0.63", "@blmcp/ui": "1.1.19", "@blmlc/lego-init": "^1.0.8-beta.30", "@codemirror/autocomplete": "^6.16.0", "@codemirror/commands": "^6.6.0", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.3", "@ice/stark-app": "^1.5.0", "ahooks": "^3.7.8", "antd": "^5.8.1", "axios": "^1.6.2", "blm-utils": "1.2.88-rc.4", "classnames": "^2.5.0", "dayjs": "1.11.9", "echarts": "^5.6.0", "js-cookie": "^2.2.1", "lodash-es": "4.17.21", "numeral": "^2.0.6", "prop-types": "^15.6.0", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-joyride": "2.7.2", "resize-observer-polyfill": "^1.5.1", "vuera": "^0.2.7"}, "devDependencies": {"@babel/parser": "^7.23.6", "@babel/runtime": "^7.0.0", "@modern-js/module-tools": "^2.58.1", "@modern-js/storybook": "2.53.0", "@storybook/addon-essentials": "7", "@svgr/webpack": "^5.5.0", "@types/react": "^18.0.25", "click-to-react-component": "1.1.0", "cross-env": "^7.0.3", "http-proxy-middleware": "^3.0.5", "stylelint": "^15.10.3", "typescript": "^5.5.4"}}, "../../node_modules/.pnpm/@alilc+lowcode-plugin-inject@1.2.3_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_xwbn5w6atednvx3fyqotlbdxuy/node_modules/@alilc/lowcode-plugin-inject": {"version": "1.2.3", "license": "MIT", "dependencies": {"@alifd/next": "^1.25.13", "@alilc/lowcode-engine": "^1.0.0", "@alilc/lowcode-types": "^1.1.2", "@alilc/lowcode-utils": "^1.0.0", "case": "^1.6.3", "fetch-jsonp": "^1.2.1", "query-string": "^7.1.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.32", "@alifd/theme-lowcode-light": "^0.2.1", "@alilc/build-plugin-alt": "^1.0.0", "@recore/config": "^2.0.6", "@types/react": "^16.0.3", "build-plugin-fusion": "^0.1.19"}, "peerDependencies": {"react": "^16.0.0"}}, "../../node_modules/.pnpm/@alilc+lowcode-react-renderer@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30._q4dzvorlrdhgvmmiajkd2gdexu/node_modules/@alilc/lowcode-react-renderer": {"version": "1.3.4", "dependencies": {"@alifd/next": "^1.21.16", "@alilc/lowcode-renderer-core": "1.3.4"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alifd/next": "^1.19.17", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "react": "^16.4.1", "react-dom": "^16.4.1", "react-test-renderer": "^16"}}, "../../node_modules/.pnpm/@alilc+lowcode-types@1.3.4/node_modules/@alilc/lowcode-types": {"version": "1.3.4", "dependencies": {"@alilc/lowcode-datasource-types": "^1.0.0", "react": "^16.9", "strict-event-emitter-types": "^2.0.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@types/node": "^13.7.1", "@types/react": "^16"}}, "../../node_modules/.pnpm/@alilc+lowcode-utils@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_react-dom@18.3.1/node_modules/@alilc/lowcode-utils": {"version": "1.3.4", "dependencies": {"@alifd/next": "^1.19.16", "@alilc/lowcode-types": "1.3.4", "lodash": "^4.17.21", "mobx": "^6.3.0", "prop-types": "^15.8.1", "react": "^16"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^11.2.7", "@types/node": "^13.7.1", "@types/react": "^16", "react-dom": "^16.14.0"}}, "../../node_modules/.pnpm/@ant-design+icons@5.6.1_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/icons": {"version": "5.6.1", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-svg": "^4.4.0", "@babel/runtime": "^7.24.8", "classnames": "^2.2.6", "rc-util": "^5.31.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@swc/core": "^1.3.53", "@testing-library/react": "^12", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.3", "@types/jest": "^24.9.1", "@types/lodash": "^4.14.136", "@types/node": "^13.9.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@umijs/fabric": "^3.0.0", "antd": "^4.8.2", "cross-env": "^5.2.0", "dumi": "^1.1.4", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.3.5", "eslint": "^8.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^47.0.0", "father": "^4.0.0", "glob": "^7.1.6", "history": "^4.9.0", "lodash": "^4.17.21", "pkg-dir": "4.0.0", "prettier": "^2.2.1", "rc-test": "^7.0.15", "react": "^16.4.2", "react-dom": "^16.4.2", "rimraf": "^3.0.0", "styled-components": "^3.3.3", "ts-node": "^8.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=8"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "../../node_modules/.pnpm/@babel+parser@7.27.7/node_modules/@babel/parser": {"version": "7.27.7", "extraneous": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.7"}, "bin": {"parser": "bin/babel-parser.js"}, "devDependencies": {"@babel/code-frame": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}}, "../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime": {"version": "7.27.6", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "../../node_modules/.pnpm/@blmcp+peento-businessComponents@0.0.63_@blmcp+peento-publicApiHub@1.0.12_@blmcp+ui@1.1.19_@f_lh7k7vqkvycomgcxoyfuppi3nm/node_modules/@blmcp/peento-businessComponents": {"version": "0.0.63", "dependencies": {"@ant-design/icons": "5.3.7", "@blmcp/peento-publicApiHub": "1.0.12", "@blmcp/peento-request": "0.0.17", "@blmcp/peento-share-url-link": "^0.1.3", "@blmcp/ui": "1.1.19-beta.1", "@blmcp/ui-mobile": "1.1.0-rc.1", "@formily/antd-v5": "^1.1.1", "@formily/core": "^2.2.29", "@formily/react": "^2.2.29", "@types/lodash-es": "4.17.9", "@types/react": "18.0.0", "ahooks": "3.8.1", "antd": "5.16.0", "blm-monitor": "1.0.12", "blm-utils": "1.2.87-beta.3", "classnames": "^2.5.1", "dayjs": "1.11.10", "lodash-es": "4.17.21", "pinyin-match": "1.2.4", "process": "0.11.10", "rc-virtual-list": "3.14.2", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-dom": "18.2.0", "react-tiny-virtual-list": "2.2.0", "spark-md5": "3.0.2"}, "devDependencies": {"@modern-js-app/eslint-config": "2.53.0", "@modern-js/eslint-config": "2.53.0", "@modern-js/module-tools": "2.53.0", "@modern-js/plugin-rspress": "1.25.0", "@modern-js/plugin-testing": "2.53.0", "@modern-js/storybook": "2.52.0", "@modern-js/tsconfig": "2.53.0", "@storybook/addon-essentials": "7", "@types/jest": "29.2.4", "@types/node": "16.11.7", "@types/react-beautiful-dnd": "13.1.4", "@types/spark-md5": "3.0.2", "cross-env": "^7.0.3", "eslint": "8.46.0", "husky": "8.0.1", "lint-staged": "13.1.0", "prettier": "2.8.1", "rimraf": "3.0.2", "router-scan-bigbang": "1.3.42", "typescript": "5.0.4"}, "peerDependencies": {"@blmcp/peento-publicApiHub": "1.0.12", "@blmcp/ui": "1.1.18-beta.3", "@formily/antd-v5": "^1.1.1", "@formily/core": "^2.2.29", "@formily/react": "^2.2.29", "@types/react": "18.2.18", "cross-env": "^7.0.3", "dayjs": "1.11.10", "lodash-es": "4.17.21", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-dom": "18.2.0", "spark-md5": "3.0.2"}}, "../../node_modules/.pnpm/@blmcp+ui@1.1.19_@types+react@18.3.23_antd@5.26.3_dayjs@1.11.9_react-dom@18.3.1_react@18.3.1/node_modules/@blmcp/ui": {"version": "1.1.19", "dependencies": {"@ant-design/icons": "^5.2.5", "antd": "5.16.0", "antd-style": "^3.6.2", "classnames": "^2.3.2", "dayjs": "1.11.10", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@modern-js-app/eslint-config": "2.53.0", "@modern-js/eslint-config": "2.53.0", "@modern-js/module-tools": "2.53.0", "@modern-js/storybook": "2.53.0", "@modern-js/tsconfig": "2.53.0", "@storybook/addon-essentials": "^7", "@types/jest": "~29.2.4", "@types/node": "~16.11.7", "@types/react": "~18.0.26", "cross-env": "^7.0.3", "fs": "0.0.1-security", "husky": "~8.0.1", "lint-staged": "~13.1.0", "path": "^0.12.7", "prettier": "~2.8.1", "rimraf": "~3.0.2", "typescript": "~5.0.4"}, "peerDependencies": {"antd": "5.16.0", "dayjs": "1.11.10", "react": "^18", "react-dom": "^18"}}, "../../node_modules/.pnpm/@blmlc+lego-init@1.0.8-beta.30_@alifd+meet-react@2.9.9_@alifd+next@1.27.32_@types+react-dom@1_jhtdjrf35c6qz3anosm5kblpem/node_modules/@blmlc/lego-init": {"version": "1.0.8-beta.30", "license": "ISC", "dependencies": {"@alilc/lowcode-datasource-fetch-handler": "1.1.4", "@alilc/lowcode-plugin-inject": "1.2.3", "@alilc/lowcode-plugin-set-ref-prop": "1.0.1", "@alilc/lowcode-setter-behavior": "1.0.0", "@alilc/lowcode-setter-title": "1.0.2", "@alilc/lowcode-types": "1.2.3", "@blmcp/ui": "1.0.56", "less": "4.2.0", "less-loader": "12.2.0"}, "devDependencies": {"@alib/build-scripts": "0.1.32", "@babel/core": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-typescript": "7.23.3", "babel-plugin-add-module-exports": "1.0.4", "babel-plugin-dynamic-import-node": "2.3.3", "build-plugin-component": "1.12.2", "cross-env": "7.0.3", "rimraf": "5.0.5"}}, "../../node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete": {"version": "6.18.6", "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}}, "../../node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands": {"version": "6.8.1", "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}}, "../../node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state": {"version": "6.5.2", "license": "MIT", "dependencies": {"@marijn/find-cluster-break": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}}, "../../node_modules/.pnpm/@codemirror+view@6.38.0/node_modules/@codemirror/view": {"version": "6.38.0", "license": "MIT", "dependencies": {"@codemirror/state": "^6.5.0", "crelt": "^1.0.6", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}}, "../../node_modules/.pnpm/@ice+stark-app@1.5.0/node_modules/@ice/stark-app": {"version": "1.5.0", "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.5.2", "@commitlint/config-conventional": "^7.5.0", "@ice/spec": "^0.1.4", "@testing-library/jest-dom": "^4.2.3", "@types/jest": "^24.0.12", "@types/node": "^12.0.0", "codecov": "^3.4.0", "husky": "^2.2.0", "jest": "^24.7.1", "stylelint": "^10.1.0", "ts-jest": "^24.0.2", "typescript": "^3.4.4"}}, "../../node_modules/.pnpm/@modern-js+module-tools@2.67.11_typescript@5.8.3/node_modules/@modern-js/module-tools": {"version": "2.67.11", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.3.0", "@ast-grep/napi": "0.35.0", "@babel/core": "^7.26.0", "@babel/types": "^7.26.0", "@modern-js/core": "2.67.11", "@modern-js/plugin": "2.67.11", "@modern-js/plugin-changeset": "2.67.11", "@modern-js/plugin-i18n": "2.67.11", "@modern-js/swc-plugins": "0.6.11", "@modern-js/types": "2.67.11", "@modern-js/utils": "2.67.11", "@rollup/pluginutils": "4.2.1", "@swc/helpers": "^0.5.17", "convert-source-map": "1.9.0", "enhanced-resolve": "5.17.1", "esbuild": "0.19.2", "magic-string": "0.30.17", "postcss": "^8.4.35", "postcss-modules": "4.3.1", "safe-identifier": "0.4.2", "source-map": "0.7.4", "style-inject": "0.3.0", "sucrase": "3.29.0", "tapable": "2.2.1", "terser": "^5.31.1", "tsconfig-paths-webpack-plugin": "4.1.0"}, "bin": {"modern": "bin/modern.js", "modern-module": "bin/modern.js"}, "devDependencies": {"@modern-js/self": "npm:@modern-js/module-tools@2.67.11", "@rsbuild/core": "1.3.22", "@scripts/build": "2.66.0", "@scripts/vitest-config": "2.66.0", "@types/convert-source-map": "1.5.2", "@types/node": "^14", "typescript": "^5"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"typescript": "^4 || ^5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "../../node_modules/.pnpm/@modern-js+storybook@2.53.0_@types+react-dom@19.1.6_@types+react@18.3.23_esbuild@0.18.20_reac_6fqujrfeai3npg6joquw2atbd4/node_modules/@modern-js/storybook": {"version": "2.53.0", "dev": true, "license": "MIT", "dependencies": {"@modern-js/storybook-builder": "2.53.0", "@modern-js/utils": "2.53.0", "@storybook/react": "~7.6.1", "storybook": "~7.6.1"}, "bin": {"sb": "bin.js", "storybook": "bin.js"}, "devDependencies": {"@storybook/types": "~7.6.12", "@types/node": "^20.5.6", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}}, "../../node_modules/.pnpm/@storybook+addon-essentials@7.6.20_@types+react-dom@19.1.6_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@storybook/addon-essentials": {"version": "7.6.20", "dev": true, "license": "MIT", "dependencies": {"@storybook/addon-actions": "7.6.20", "@storybook/addon-backgrounds": "7.6.20", "@storybook/addon-controls": "7.6.20", "@storybook/addon-docs": "7.6.20", "@storybook/addon-highlight": "7.6.20", "@storybook/addon-measure": "7.6.20", "@storybook/addon-outline": "7.6.20", "@storybook/addon-toolbars": "7.6.20", "@storybook/addon-viewport": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview-api": "7.6.20", "ts-dedent": "^2.0.0"}, "devDependencies": {"@storybook/vue": "7.6.20", "typescript": "^4.9.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "../../node_modules/.pnpm/@svgr+webpack@5.5.0/node_modules/@svgr/webpack": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.12.3", "@babel/plugin-transform-react-constant-elements": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.5", "@svgr/core": "^5.5.0", "@svgr/plugin-jsx": "^5.5.0", "@svgr/plugin-svgo": "^5.5.0", "loader-utils": "^2.0.0"}, "devDependencies": {"babel-loader": "^8.2.1", "memory-fs": "^0.5.0", "url-loader": "^4.1.1", "webpack": "^5.4.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "../../node_modules/.pnpm/@types+react@18.3.23/node_modules/@types/react": {"version": "18.3.23", "dev": true, "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "../../node_modules/.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1/node_modules/ahooks": {"version": "3.9.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "dayjs": "^1.9.1", "intersection-observer": "^0.12.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "react-fast-compare": "^3.2.2", "resize-observer-polyfill": "^1.5.1", "screenfull": "^5.0.0", "tslib": "^2.4.1"}, "devDependencies": {"@alifd/next": "^1.20.6", "@ant-design/icons": "^5.0.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "antd": "^5.2.1", "jest-websocket-mock": "^2.1.0", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-drag-listview": "^0.1.6", "react-json-view": "^1.21.3"}, "engines": {"node": ">=8.0.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "../../node_modules/.pnpm/antd@5.26.3_moment@2.30.1_react-dom@18.3.1_react@18.3.1/node_modules/antd": {"version": "5.26.3", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.2.1", "@ant-design/cssinjs": "^1.23.0", "@ant-design/cssinjs-utils": "^1.1.3", "@ant-design/fast-color": "^2.0.6", "@ant-design/icons": "^5.6.1", "@ant-design/react-slick": "~1.1.2", "@babel/runtime": "^7.26.0", "@rc-component/color-picker": "~2.0.1", "@rc-component/mutate-observer": "^1.1.0", "@rc-component/qrcode": "~1.0.0", "@rc-component/tour": "~1.15.1", "@rc-component/trigger": "^2.2.7", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "rc-cascader": "~3.34.0", "rc-checkbox": "~3.5.0", "rc-collapse": "~3.9.0", "rc-dialog": "~9.6.0", "rc-drawer": "~7.3.0", "rc-dropdown": "~4.2.1", "rc-field-form": "~2.7.0", "rc-image": "~7.12.0", "rc-input": "~1.8.0", "rc-input-number": "~9.5.0", "rc-mentions": "~2.20.0", "rc-menu": "~9.16.1", "rc-motion": "^2.9.5", "rc-notification": "~5.6.4", "rc-pagination": "~5.1.0", "rc-picker": "~4.11.3", "rc-progress": "~4.0.0", "rc-rate": "~2.13.1", "rc-resize-observer": "^1.4.3", "rc-segmented": "~2.7.0", "rc-select": "~14.16.8", "rc-slider": "~11.1.8", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-table": "~7.51.1", "rc-tabs": "~15.6.1", "rc-textarea": "~1.10.0", "rc-tooltip": "~6.4.0", "rc-tree": "~5.13.1", "rc-tree-select": "~5.27.0", "rc-upload": "~4.9.2", "rc-util": "^5.44.4", "scroll-into-view-if-needed": "^3.1.0", "throttle-debounce": "^5.0.2"}, "devDependencies": {"@ant-design/compatible": "^5.1.3", "@ant-design/happy-work-theme": "^1.0.0", "@ant-design/tools": "^18.0.3", "@ant-design/v5-patch-for-react-19": "^1.0.2", "@antfu/eslint-config": "^4.15.0", "@antv/g6": "^4.8.24", "@biomejs/biome": "^2.0.4", "@codecov/webpack-plugin": "^1.4.0", "@codesandbox/sandpack-react": "^2.19.10", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/css": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/server": "^11.11.0", "@eslint-react/eslint-plugin": "^1.17.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@inquirer/prompts": "^7.1.0", "@madccc/duplicate-package-checker-webpack-plugin": "^1.0.0", "@microflash/rehype-figure": "^2.1.1", "@npmcli/run-script": "^9.0.1", "@octokit/rest": "^22.0.0", "@prettier/sync": "^0.6.1", "@qixian.cs/github-contributors-list": "^2.0.2", "@size-limit/file": "^11.1.6", "@stackblitz/sdk": "^1.11.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/adm-zip": "^0.5.6", "@types/ali-oss": "^6.16.11", "@types/cli-progress": "^3.11.6", "@types/css-tree": "^2.3.10", "@types/fs-extra": "^11.0.4", "@types/gtag.js": "^0.0.20", "@types/http-server": "^0.12.4", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/jest-environment-puppeteer": "^5.0.6", "@types/jest-image-snapshot": "^6.4.0", "@types/jquery": "^3.5.31", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.12", "@types/minimist": "^1.2.5", "@types/node": "^24.0.0", "@types/nprogress": "^0.2.3", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@types/prismjs": "^1.26.4", "@types/progress": "^2.0.7", "@types/react": "^19.0.1", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.0.2", "@types/react-highlight-words": "^0.20.0", "@types/react-resizable": "^3.0.8", "@types/semver": "^7.5.8", "@types/spinnies": "^0.5.3", "@types/tar": "^6.1.13", "@types/throttle-debounce": "^5.0.2", "@types/warning": "^3.0.3", "adm-zip": "^0.5.16", "ali-oss": "^6.21.0", "antd-img-crop": "^4.23.0", "antd-style": "^3.7.1", "antd-token-previewer": "^2.0.8", "axios": "^1.7.7", "chalk": "^5.0.0", "cheerio": "^1.0.0", "circular-dependency-plugin": "^5.2.2", "cli-progress": "^3.12.0", "cross-env": "^7.0.3", "cross-fetch": "^4.0.0", "css-tree": "^3.1.0", "csstree-validator": "^4.0.1", "cypress-image-diff-html-report": "2.2.0", "dekko": "^0.2.1", "dotenv": "^17.0.0", "dumi": "~2.4.20", "dumi-plugin-color-chunk": "^2.1.0", "env-paths": "^3.0.0", "eslint": "^9.23.0", "eslint-plugin-compat": "^6.0.1", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.14", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "gh-pages": "^6.2.0", "github-slugger": "^2.0.0", "glob": "^11.0.0", "hast-util-to-string": "^3.0.1", "html2sketch": "^1.0.2", "http-server": "^14.1.1", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "immer": "^10.1.1", "is-ci": "^4.0.0", "isomorphic-fetch": "^3.0.0", "jest": "^30.0.0", "jest-axe": "^10.0.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^30.0.0", "jest-image-snapshot": "^6.4.0", "jest-puppeteer": "^11.0.0", "jquery": "^3.7.1", "jsdom": "^26.0.0", "jsonml-to-react-element": "^1.1.11", "jsonml.js": "^0.1.0", "lint-staged": "^16.0.0", "lodash": "^4.17.21", "lunar-typescript": "^1.7.5", "lz-string": "^1.5.0", "minimist": "^1.2.8", "mockdate": "^3.0.5", "node-fetch": "^3.3.2", "node-notifier": "^10.0.1", "open": "^10.1.0", "ora": "^8.1.0", "p-all": "^5.0.0", "package-manager-detector": "^1.0.0", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "portfinder": "^1.0.37", "prettier": "^3.4.1", "pretty-format": "^30.0.0", "prismjs": "^1.29.0", "puppeteer": "^24.7.1", "rc-footer": "^0.6.8", "rc-tween-one": "^3.0.6", "rc-virtual-list": "^3.19.1", "react": "^19.1.0", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-fast-marquee": "^1.6.5", "react-highlight-words": "^0.21.0", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.1", "react-resizable": "^3.0.5", "react-router-dom": "^7.0.1", "react-scan": "^0.4.2", "react-sticky-box": "^2.0.5", "regenerator-runtime": "^0.14.1", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-cli": "^12.0.1", "remark-gfm": "^4.0.0", "remark-lint": "^10.0.0", "remark-lint-no-undefined-references": "^5.0.0", "remark-preset-lint-recommended": "^7.0.0", "remark-rehype": "^11.1.1", "rimraf": "^6.0.1", "runes2": "^1.1.4", "semver": "^7.6.3", "sharp": "^0.34.0", "simple-git": "^3.27.0", "size-limit": "^11.1.6", "spinnies": "^0.5.1", "tar": "^7.4.3", "tsx": "^4.20.3", "typedoc": "^0.28.0", "typescript": "~5.8.2", "vanilla-jsoneditor": "^3.0.0", "vanilla-tilt": "^1.8.1", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "xhr-mock": "^2.5.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "../../node_modules/.pnpm/axios@1.10.0/node_modules/axios": {"version": "1.10.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "abortcontroller-polyfill": "^1.7.5", "auto-changelog": "^2.4.0", "body-parser": "^1.20.2", "chalk": "^5.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "dev-null": "^0.1.1", "dtslint": "^4.2.1", "es6-promise": "^4.2.8", "eslint": "^8.56.0", "express": "^4.18.2", "formdata-node": "^5.0.1", "formidable": "^2.1.2", "fs-extra": "^10.1.0", "get-stream": "^3.0.0", "gulp": "^4.0.2", "gzip-size": "^7.0.0", "handlebars": "^4.7.8", "husky": "^8.0.3", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^2.99.1", "karma": "^6.3.17", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^1.1.2", "karma-jasmine-ajax": "^0.1.13", "karma-rollup-preprocessor": "^7.0.8", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "memoizee": "^0.4.15", "minimist": "^1.2.8", "mocha": "^10.3.0", "multer": "^1.4.4", "pretty-bytes": "^6.1.1", "release-it": "^15.11.0", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-bundle-size": "^1.0.3", "rollup-plugin-terser": "^7.0.2", "sinon": "^4.5.0", "stream-throttle": "^0.1.3", "string-replace-async": "^3.0.2", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.9.5"}}, "../../node_modules/.pnpm/blm-utils@1.2.88-rc.4/node_modules/blm-utils": {"version": "1.2.88-rc.4", "license": "ISC", "dependencies": {"axios": "0.26.0", "blueimp-md5": "2.15.0", "element-ui": "3.2.16", "i18next": "23.6.0", "js-cookie": "2.2.1", "lodash": "4.17.21", "moment": "2.29.1", "path-to-regexp": "6.2.1", "qs": "6.10.3", "vue": "2.6.14"}, "devDependencies": {"@babel/core": "7.7.7", "@babel/plugin-transform-runtime": "7.4.4", "@babel/preset-env": "7.7.7", "@rollup/plugin-alias": "2.2.0", "@rollup/plugin-commonjs": "11.1.0", "@rollup/plugin-node-resolve": "10.0.0", "@rollup/plugin-replace": "2.4.2", "axios-mock-adapter": "^2.0.0", "babel-jest": "^29.7.0", "babel-plugin-component": "1.1.1", "cross-env": "6.0.3", "jest": "^29.7.0", "jsdom": "^24.1.1", "minimist": "1.2.0", "rollup": "1.27.13", "rollup-plugin-babel": "4.3.3", "rollup-plugin-terser": "5.1.3", "router-scan-bigbang": "1.3.31"}, "engines": {"node": ">=8"}}, "../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames": {"version": "2.5.1", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "classnames-local": "file:.", "classnames-npm": "npm:classnames@*", "http-server": "^14.1.1", "markdown-table": "^3.0.3", "rollup": "^4.9.1", "tinybench": "^2.5.1", "tsd": "^0.30.1"}}, "../../node_modules/.pnpm/click-to-react-component@1.1.0_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/click-to-react-component": {"version": "1.1.0", "dev": true, "license": "ISC", "dependencies": {"@floating-ui/react-dom-interactions": "^0.3.1", "htm": "^3.1.0", "react-merge-refs": "^1.1.0"}, "devDependencies": {"@types/react": "^18.0.6", "@types/react-reconciler": "^0.26.6", "eslint": "^8.0.0", "eslint-config-react-app": "^7.0.1"}, "peerDependencies": {"react": ">=16.8.0"}}, "../../node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.1"}, "bin": {"cross-env": "src/bin/cross-env.js", "cross-env-shell": "src/bin/cross-env-shell.js"}, "devDependencies": {"kcd-scripts": "^5.5.0"}, "engines": {"node": ">=10.14", "npm": ">=6", "yarn": ">=1"}}, "../../node_modules/.pnpm/dayjs@1.11.9/node_modules/dayjs": {"version": "1.11.9", "license": "MIT", "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}}, "../../node_modules/.pnpm/echarts@5.6.0/node_modules/echarts": {"version": "5.6.0", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}}, "../../node_modules/.pnpm/http-proxy-middleware@3.0.5/node_modules/http-proxy-middleware": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.15", "debug": "^4.3.6", "http-proxy": "^1.18.1", "is-glob": "^4.0.3", "is-plain-object": "^5.0.0", "micromatch": "^4.0.8"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/js": "9.23.0", "@types/debug": "4.1.12", "@types/eslint": "9.6.1", "@types/express": "4.17.21", "@types/is-glob": "4.0.4", "@types/jest": "29.5.14", "@types/micromatch": "4.0.9", "@types/node": "22.10.2", "@types/supertest": "6.0.2", "@types/ws": "8.18.0", "body-parser": "1.20.3", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.3", "express": "4.21.2", "get-port": "5.1.1", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.0", "mockttp": "3.17.0", "open": "8.4.2", "patch-package": "8.0.0", "pkg-pr-new": "0.0.41", "prettier": "3.5.3", "supertest": "7.1.0", "ts-jest": "29.2.6", "typescript": "5.8.2", "typescript-eslint": "8.27.0", "ws": "8.18.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "../../node_modules/.pnpm/js-cookie@2.2.1/node_modules/js-cookie": {"version": "2.2.1", "license": "MIT", "devDependencies": {"grunt": "1.0.3", "grunt-compare-size": "0.4.2", "grunt-contrib-connect": "2.0.0", "grunt-contrib-nodeunit": "2.0.0", "grunt-contrib-qunit": "2.0.0", "grunt-contrib-uglify": "2.3.0", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "21.0.0", "grunt-saucelabs": "9.0.0", "gzip-js": "0.3.2", "qunitjs": "1.23.1", "requirejs": "2.3.5"}}, "../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "../../node_modules/.pnpm/numeral@2.0.6/node_modules/numeral": {"version": "2.0.6", "license": "MIT", "devDependencies": {"chai": "^3.5.0", "grunt": "latest", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-jshint": "latest", "grunt-contrib-nodeunit": "1.0.0", "grunt-contrib-uglify": "latest", "grunt-karma": "^2.0.0", "grunt-mocha-test": "^0.13.2", "grunt-saucelabs": "*", "grunt-string-replace": "^1.3.1", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-firefox-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^1.1.0", "load-grunt-tasks": "^3.5.2", "mocha": "^3.1.2", "uglify-js": "latest"}, "engines": {"node": "*"}}, "../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.5.0", "bundle-collapser": "^1.4.0", "eslint": "^8.6.0", "in-publish": "^2.0.1", "jest": "^19.0.2", "react": "^15.7.0", "uglifyify": "^5.0.2", "uglifyjs": "^2.4.11"}}, "../../node_modules/.pnpm/react-dnd-html5-backend@16.0.1/node_modules/react-dnd-html5-backend": {"version": "16.0.1", "license": "MIT", "dependencies": {"dnd-core": "^16.0.1"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@types/jest": "^27.4.1", "npm-run-all": "^4.1.5", "shx": "^0.3.4", "typescript": "^4.6.3"}}, "../../node_modules/.pnpm/react-dnd@16.0.1_@types+react@18.3.23_react@18.3.1/node_modules/react-dnd": {"version": "16.0.1", "license": "MIT", "dependencies": {"@react-dnd/invariant": "^4.0.1", "@react-dnd/shallowequal": "^4.0.1", "dnd-core": "^16.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@testing-library/react": "^13.1.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@types/react": "^18.0.5", "@types/react-dom": "^18.0.1", "jest": "^27.5.1", "jest-mock": "^27.5.1", "npm-run-all": "^4.1.5", "react": "^18.0.0", "react-dnd-test-backend": "portal:../backend-test", "react-dnd-test-utils": "portal:../test-utils", "react-dom": "^18.0.0", "shx": "^0.3.4", "typescript": "^4.6.3"}, "peerDependencies": {"@types/hoist-non-react-statics": ">= 3.3.1", "@types/node": ">= 12", "@types/react": ">= 16", "react": ">= 16.14"}, "peerDependenciesMeta": {"@types/hoist-non-react-statics": {"optional": true}, "@types/node": {"optional": true}, "@types/react": {"optional": true}}}, "../../node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "../../node_modules/.pnpm/react-joyride@2.7.2_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/react-joyride": {"version": "2.7.2", "license": "MIT", "dependencies": {"@gilbarbara/deep-equal": "^0.3.1", "@gilbarbara/helpers": "^0.9.0", "deep-diff": "^1.0.2", "deepmerge": "^4.3.1", "is-lite": "^1.2.0", "react-floater": "^0.7.9", "react-innertext": "^1.1.5", "react-is": "^16.13.1", "scroll": "^3.0.1", "scrollparent": "^2.1.0", "tree-changes": "^0.11.2", "type-fest": "^4.8.3"}, "devDependencies": {"@arethetypeswrong/cli": "^0.13.5", "@gilbarbara/eslint-config": "^0.7.2", "@gilbarbara/node-helpers": "^0.1.0", "@gilbarbara/prettier-config": "^1.0.0", "@gilbarbara/tsconfig": "^0.2.3", "@playwright/experimental-ct-react": "^1.40.1", "@size-limit/preset-big-lib": "^11.0.1", "@swc/core": "^1.3.100", "@swc/jest": "^0.2.29", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@total-typescript/shoehorn": "^0.1.1", "@types/exenv": "^1.2.2", "@types/jest": "^29.5.11", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@types/scroll": "^3.0.3", "@types/scrollparent": "^2.0.3", "caniuse-lite": "^1.0.30001570", "cross-env": "^7.0.3", "csstype": "^3.1.3", "del-cli": "^5.1.0", "husky": "^8.0.3", "is-ci-cli": "^2.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.2", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "repo-tools": "^0.3.1", "size-limit": "^11.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "peerDependencies": {"react": "15 - 18", "react-dom": "15 - 18"}}, "../../node_modules/.pnpm/react@18.3.1/node_modules/react": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "../../node_modules/.pnpm/resize-observer-polyfill@1.5.1/node_modules/resize-observer-polyfill": {"version": "1.5.1", "license": "MIT", "devDependencies": {"babel-eslint": "10.0.1", "cpy-cli": "2.0.0", "eslint": "5.10.0", "jasmine": "2.8.0", "jasmine-core": "2.8.0", "karma": "3.1.3", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-jasmine": "1.1.2", "karma-jasmine-html-reporter": "0.2.2", "karma-rollup-preprocessor": "6.1.1", "karma-sauce-launcher": "1.2.0", "karma-sourcemap-loader": "0.3.7", "karma-spec-reporter": "0.0.32", "promise-polyfill": "8.1.0", "rollup": "0.67.4", "rollup-plugin-typescript": "1.0.0", "typescript": "3.2.2"}}, "../../node_modules/.pnpm/stylelint@15.11.0_typescript@5.8.3/node_modules/stylelint": {"version": "15.11.0", "dev": true, "license": "MIT", "dependencies": {"@csstools/css-parser-algorithms": "^2.3.1", "@csstools/css-tokenizer": "^2.2.0", "@csstools/media-query-list-parser": "^2.1.4", "@csstools/selector-specificity": "^3.0.0", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^8.2.0", "css-functions-list": "^3.2.1", "css-tree": "^2.3.1", "debug": "^4.3.4", "fast-glob": "^3.3.1", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^7.0.0", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.3.1", "ignore": "^5.2.4", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.29.0", "mathml-tag-names": "^2.1.3", "meow": "^10.1.5", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.28", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.13", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "supports-hyperlinks": "^3.0.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "write-file-atomic": "^5.0.1"}, "bin": {"stylelint": "bin/stylelint.mjs"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@changesets/get-github-info": "^0.5.2", "@jest/globals": "^29.6.4", "@stylelint/prettier-config": "^3.0.0", "@stylelint/remark-preset": "^4.0.0", "@types/balanced-match": "^1.0.3", "@types/css-tree": "^2.3.1", "@types/debug": "^4.1.8", "@types/file-entry-cache": "^5.0.2", "@types/global-modules": "^2.0.0", "@types/globjoin": "^0.1.0", "@types/imurmurhash": "^0.1.1", "@types/micromatch": "^4.0.2", "@types/normalize-path": "^3.0.0", "@types/postcss-less": "^4.0.2", "@types/postcss-resolve-nested-selector": "^0.1.0", "@types/postcss-safe-parser": "^5.0.1", "@types/style-search": "^0.1.3", "@types/svg-tags": "^1.0.0", "@types/write-file-atomic": "^4.0.0", "benchmark": "^2.1.4", "common-tags": "^1.8.2", "deepmerge": "^4.3.1", "eslint": "^8.48.0", "eslint-config-stylelint": "^20.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.2.3", "husky": "^8.0.3", "jest": "^29.6.4", "jest-preset-stylelint": "^6.2.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^14.0.1", "np": "^8.0.4", "npm-run-all": "^4.1.5", "patch-package": "^8.0.0", "postcss-html": "^1.5.0", "postcss-import": "^15.1.0", "postcss-less": "^6.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.7", "remark-cli": "^11.0.0", "sugarss": "^4.0.1", "typescript": "^5.2.2"}, "engines": {"node": "^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stylelint"}}, "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "node_modules/@alilc/lowcode-plugin-inject": {"resolved": "../../node_modules/.pnpm/@alilc+lowcode-plugin-inject@1.2.3_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_xwbn5w6atednvx3fyqotlbdxuy/node_modules/@alilc/lowcode-plugin-inject", "link": true}, "node_modules/@alilc/lowcode-react-renderer": {"resolved": "../../node_modules/.pnpm/@alilc+lowcode-react-renderer@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30._q4dzvorlrdhgvmmiajkd2gdexu/node_modules/@alilc/lowcode-react-renderer", "link": true}, "node_modules/@alilc/lowcode-types": {"resolved": "../../node_modules/.pnpm/@alilc+lowcode-types@1.3.4/node_modules/@alilc/lowcode-types", "link": true}, "node_modules/@alilc/lowcode-utils": {"resolved": "../../node_modules/.pnpm/@alilc+lowcode-utils@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_react-dom@18.3.1/node_modules/@alilc/lowcode-utils", "link": true}, "node_modules/@ant-design/icons": {"resolved": "../../node_modules/.pnpm/@ant-design+icons@5.6.1_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/icons", "link": true}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "http://npm.bailongma-inc.com/@babel%2fhelper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://npm.bailongma-inc.com/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "http://npm.bailongma-inc.com/@babel%2fparser/-/parser-7.28.0.tgz", "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"resolved": "../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime", "link": true}, "node_modules/@babel/types": {"version": "7.28.2", "resolved": "http://npm.bailongma-inc.com/@babel%2ftypes/-/types-7.28.2.tgz", "integrity": "sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@blmcp/peento-businessComponents": {"resolved": "../../node_modules/.pnpm/@blmcp+peento-businessComponents@0.0.63_@blmcp+peento-publicApiHub@1.0.12_@blmcp+ui@1.1.19_@f_lh7k7vqkvycomgcxoyfuppi3nm/node_modules/@blmcp/peento-businessComponents", "link": true}, "node_modules/@blmcp/ui": {"resolved": "../../node_modules/.pnpm/@blmcp+ui@1.1.19_@types+react@18.3.23_antd@5.26.3_dayjs@1.11.9_react-dom@18.3.1_react@18.3.1/node_modules/@blmcp/ui", "link": true}, "node_modules/@blmlc/lego-init": {"resolved": "../../node_modules/.pnpm/@blmlc+lego-init@1.0.8-beta.30_@alifd+meet-react@2.9.9_@alifd+next@1.27.32_@types+react-dom@1_jhtdjrf35c6qz3anosm5kblpem/node_modules/@blmlc/lego-init", "link": true}, "node_modules/@codemirror/autocomplete": {"resolved": "../../node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete", "link": true}, "node_modules/@codemirror/commands": {"resolved": "../../node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands", "link": true}, "node_modules/@codemirror/state": {"resolved": "../../node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state", "link": true}, "node_modules/@codemirror/view": {"resolved": "../../node_modules/.pnpm/@codemirror+view@6.38.0/node_modules/@codemirror/view", "link": true}, "node_modules/@ice/stark-app": {"resolved": "../../node_modules/.pnpm/@ice+stark-app@1.5.0/node_modules/@ice/stark-app", "link": true}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "http://npm.bailongma-inc.com/@jridgewell%2fsourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "license": "MIT", "peer": true}, "node_modules/@modern-js/module-tools": {"resolved": "../../node_modules/.pnpm/@modern-js+module-tools@2.67.11_typescript@5.8.3/node_modules/@modern-js/module-tools", "link": true}, "node_modules/@modern-js/storybook": {"resolved": "../../node_modules/.pnpm/@modern-js+storybook@2.53.0_@types+react-dom@19.1.6_@types+react@18.3.23_esbuild@0.18.20_reac_6fqujrfeai3npg6joquw2atbd4/node_modules/@modern-js/storybook", "link": true}, "node_modules/@storybook/addon-essentials": {"resolved": "../../node_modules/.pnpm/@storybook+addon-essentials@7.6.20_@types+react-dom@19.1.6_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@storybook/addon-essentials", "link": true}, "node_modules/@svgr/webpack": {"resolved": "../../node_modules/.pnpm/@svgr+webpack@5.5.0/node_modules/@svgr/webpack", "link": true}, "node_modules/@types/react": {"resolved": "../../node_modules/.pnpm/@types+react@18.3.23/node_modules/@types/react", "link": true}, "node_modules/@vue/compiler-core": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-core/-/compiler-core-3.5.18.tgz", "integrity": "sha512-3slwjQrrV1TO8MoXgy3aynDQ7lslj5UqDxuHnrzHtpON5CBinhWjJETciPngpin/T3OuW3tXUf86tEurusnztw==", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.28.0", "@vue/shared": "3.5.18", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-dom/-/compiler-dom-3.5.18.tgz", "integrity": "sha512-RMbU6NTU70++B1JyVJbNbeFkK+A+Q7y9XKE2EM4NLGm2WFR8x9MbAtWxPPLdm0wUkuZv9trpwfSlL6tjdIa1+A==", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-core": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-sfc/-/compiler-sfc-3.5.18.tgz", "integrity": "sha512-5aBjvGqsWs+MoxswZPoTB9nSDb3dhd1x30xrrltKujlCxo48j8HGDNj3QPhF4VIS0VQDUrA1xUfp2hEa+FNyXA==", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.28.0", "@vue/compiler-core": "3.5.18", "@vue/compiler-dom": "3.5.18", "@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-ssr/-/compiler-ssr-3.5.18.tgz", "integrity": "sha512-xM16Ak7rSWHkM3m22NlmcdIM+K4BMyFARAfV9hYFl+SFuRzrZ3uGMNW05kA5pmeMa0X9X963Kgou7ufdbpOP9g==", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/reactivity": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2freactivity/-/reactivity-3.5.18.tgz", "integrity": "sha512-x0vPO5Imw+3sChLM5Y+B6G1zPjwdOri9e8V21NnTnlEvkxatHEH5B5KEAJcjuzQ7BsjGrKtfzuQ5eQwXh8HXBg==", "license": "MIT", "peer": true, "dependencies": {"@vue/shared": "3.5.18"}}, "node_modules/@vue/runtime-core": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fruntime-core/-/runtime-core-3.5.18.tgz", "integrity": "sha512-DUpHa1HpeOQEt6+3nheUfqVXRog2kivkXHUhoqJiKR33SO4x+a5uNOMkV487WPerQkL0vUuRvq/7JhRgLW3S+w==", "license": "MIT", "peer": true, "dependencies": {"@vue/reactivity": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fruntime-dom/-/runtime-dom-3.5.18.tgz", "integrity": "sha512-YwDj71iV05j4RnzZnZtGaXwPoUWeRsqinblgVJwR8XTXYZ9D5PbahHQgsbmzUvCWNF6x7siQ89HgnX5eWkr3mw==", "license": "MIT", "peer": true, "dependencies": {"@vue/reactivity": "3.5.18", "@vue/runtime-core": "3.5.18", "@vue/shared": "3.5.18", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fserver-renderer/-/server-renderer-3.5.18.tgz", "integrity": "sha512-PvIHLUoWgSbDG7zLHqSqaCoZvHi6NNmfVFOqO+OnwvqMz/tqQr3FuGWS8ufluNddk7ZLBJYMrjcw1c6XzR12mA==", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18"}, "peerDependencies": {"vue": "3.5.18"}}, "node_modules/@vue/shared": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fshared/-/shared-3.5.18.tgz", "integrity": "sha512-cZy8Dq+uuIXbxCZpuLd2GJdeSO/lIzIspC2WtkqIpje5QyFbvLaI5wZtdUjLHjGZrlVX6GilejatWwVYYRc8tA==", "license": "MIT", "peer": true}, "node_modules/ahooks": {"resolved": "../../node_modules/.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1/node_modules/ahooks", "link": true}, "node_modules/antd": {"resolved": "../../node_modules/.pnpm/antd@5.26.3_moment@2.30.1_react-dom@18.3.1_react@18.3.1/node_modules/antd", "link": true}, "node_modules/axios": {"resolved": "../../node_modules/.pnpm/axios@1.10.0/node_modules/axios", "link": true}, "node_modules/blm-utils": {"resolved": "../../node_modules/.pnpm/blm-utils@1.2.88-rc.4/node_modules/blm-utils", "link": true}, "node_modules/classnames": {"resolved": "../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames", "link": true}, "node_modules/click-to-react-component": {"resolved": "../../node_modules/.pnpm/click-to-react-component@1.1.0_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/click-to-react-component", "link": true}, "node_modules/cross-env": {"resolved": "../../node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env", "link": true}, "node_modules/csstype": {"version": "3.1.3", "resolved": "http://npm.bailongma-inc.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "license": "MIT", "peer": true}, "node_modules/dayjs": {"resolved": "../../node_modules/.pnpm/dayjs@1.11.9/node_modules/dayjs", "link": true}, "node_modules/echarts": {"resolved": "../../node_modules/.pnpm/echarts@5.6.0/node_modules/echarts", "link": true}, "node_modules/entities": {"version": "4.5.0", "resolved": "http://npm.bailongma-inc.com/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "http://npm.bailongma-inc.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "license": "MIT", "peer": true}, "node_modules/http-proxy-middleware": {"resolved": "../../node_modules/.pnpm/http-proxy-middleware@3.0.5/node_modules/http-proxy-middleware", "link": true}, "node_modules/js-cookie": {"resolved": "../../node_modules/.pnpm/js-cookie@2.2.1/node_modules/js-cookie", "link": true}, "node_modules/lodash-es": {"resolved": "../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es", "link": true}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "http://npm.bailongma-inc.com/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "license": "MIT", "peer": true, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "http://npm.bailongma-inc.com/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "peer": true, "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/numeral": {"resolved": "../../node_modules/.pnpm/numeral@2.0.6/node_modules/numeral", "link": true}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://npm.bailongma-inc.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "license": "ISC", "peer": true}, "node_modules/postcss": {"version": "8.5.6", "resolved": "http://npm.bailongma-inc.com/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "peer": true, "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prop-types": {"resolved": "../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types", "link": true}, "node_modules/react": {"resolved": "../../node_modules/.pnpm/react@18.3.1/node_modules/react", "link": true}, "node_modules/react-dnd": {"resolved": "../../node_modules/.pnpm/react-dnd@16.0.1_@types+react@18.3.23_react@18.3.1/node_modules/react-dnd", "link": true}, "node_modules/react-dnd-html5-backend": {"resolved": "../../node_modules/.pnpm/react-dnd-html5-backend@16.0.1/node_modules/react-dnd-html5-backend", "link": true}, "node_modules/react-dom": {"resolved": "../../node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom", "link": true}, "node_modules/react-joyride": {"resolved": "../../node_modules/.pnpm/react-joyride@2.7.2_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/react-joyride", "link": true}, "node_modules/resize-observer-polyfill": {"resolved": "../../node_modules/.pnpm/resize-observer-polyfill@1.5.1/node_modules/resize-observer-polyfill", "link": true}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "http://npm.bailongma-inc.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/stylelint": {"resolved": "../../node_modules/.pnpm/stylelint@15.11.0_typescript@5.8.3/node_modules/stylelint", "link": true}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/vue": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/vue/-/vue-3.5.18.tgz", "integrity": "sha512-7W4Y4ZbMiQ3SEo+m9lnoNpV9xG7QVMLa+/0RFwwiAVkeYoyGXqWE85jabU4pllJNUzqfLShJ5YLptewhCWUgNA==", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/compiler-sfc": "3.5.18", "@vue/runtime-dom": "3.5.18", "@vue/server-renderer": "3.5.18", "@vue/shared": "3.5.18"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vuera": {"version": "0.2.7", "resolved": "http://npm.bailongma-inc.com/vuera/-/vuera-0.2.7.tgz", "integrity": "sha512-MZErNEK+xXjxBzIDFL9cdEtX3TDZk4pMJGB+WLrlFOVro9okS/FeOSpdPFkpdyZUImZN/or4CrmztA932ChGEQ==", "license": "MIT", "peerDependencies": {"react": ">= 15.2.0", "react-dom": ">= 15.2.0", "vue": ">= 2.2"}}}, "dependencies": {"@alilc/lowcode-plugin-inject": {"version": "file:../../node_modules/.pnpm/@alilc+lowcode-plugin-inject@1.2.3_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_xwbn5w6atednvx3fyqotlbdxuy/node_modules/@alilc/lowcode-plugin-inject", "requires": {"@alib/build-scripts": "^0.1.32", "@alifd/next": "^1.25.13", "@alifd/theme-lowcode-light": "^0.2.1", "@alilc/build-plugin-alt": "^1.0.0", "@alilc/lowcode-engine": "^1.0.0", "@alilc/lowcode-types": "^1.1.2", "@alilc/lowcode-utils": "^1.0.0", "@recore/config": "^2.0.6", "@types/react": "^16.0.3", "build-plugin-fusion": "^0.1.19", "case": "^1.6.3", "fetch-jsonp": "^1.2.1", "query-string": "^7.1.0"}}, "@alilc/lowcode-react-renderer": {"version": "file:../../node_modules/.pnpm/@alilc+lowcode-react-renderer@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30._q4dzvorlrdhgvmmiajkd2gdexu/node_modules/@alilc/lowcode-react-renderer", "requires": {"@alib/build-scripts": "^0.1.18", "@alifd/next": "^1.19.17", "@alilc/lowcode-renderer-core": "1.3.4", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "react": "^16.4.1", "react-dom": "^16.4.1", "react-test-renderer": "^16"}}, "@alilc/lowcode-types": {"version": "file:../../node_modules/.pnpm/@alilc+lowcode-types@1.3.4/node_modules/@alilc/lowcode-types", "requires": {"@alib/build-scripts": "^0.1.18", "@alilc/lowcode-datasource-types": "^1.0.0", "@types/node": "^13.7.1", "@types/react": "^16", "react": "^16.9", "strict-event-emitter-types": "^2.0.0"}}, "@alilc/lowcode-utils": {"version": "file:../../node_modules/.pnpm/@alilc+lowcode-utils@1.3.4_@alifd+meet-react@2.9.9_@types+react@18.3.23_moment@2.30.1_react-dom@18.3.1/node_modules/@alilc/lowcode-utils", "requires": {"@alib/build-scripts": "^0.1.18", "@alifd/next": "^1.19.16", "@alilc/lowcode-types": "1.3.4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^11.2.7", "@types/node": "^13.7.1", "@types/react": "^16", "lodash": "^4.17.21", "mobx": "^6.3.0", "prop-types": "^15.8.1", "react": "^16", "react-dom": "^16.14.0"}}, "@ant-design/icons": {"version": "file:../../node_modules/.pnpm/@ant-design+icons@5.6.1_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/icons", "requires": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-svg": "^4.4.0", "@babel/runtime": "^7.24.8", "@rc-component/father-plugin": "^1.0.2", "@swc/core": "^1.3.53", "@testing-library/react": "^12", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.3", "@types/jest": "^24.9.1", "@types/lodash": "^4.14.136", "@types/node": "^13.9.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@umijs/fabric": "^3.0.0", "antd": "^4.8.2", "classnames": "^2.2.6", "cross-env": "^5.2.0", "dumi": "^1.1.4", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.3.5", "eslint": "^8.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^47.0.0", "father": "^4.0.0", "glob": "^7.1.6", "history": "^4.9.0", "lodash": "^4.17.21", "pkg-dir": "4.0.0", "prettier": "^2.2.1", "rc-test": "^7.0.15", "rc-util": "^5.31.1", "react": "^16.4.2", "react-dom": "^16.4.2", "rimraf": "^3.0.0", "styled-components": "^3.3.3", "ts-node": "^8.0.0", "typescript": "^5.0.0"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "http://npm.bailongma-inc.com/@babel%2fhelper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://npm.bailongma-inc.com/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.28.0", "resolved": "http://npm.bailongma-inc.com/@babel%2fparser/-/parser-7.28.0.tgz", "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "requires": {"@babel/types": "^7.28.0"}}, "@babel/runtime": {"version": "file:../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime"}, "@babel/types": {"version": "7.28.2", "resolved": "http://npm.bailongma-inc.com/@babel%2ftypes/-/types-7.28.2.tgz", "integrity": "sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "@blmcp/peento-businessComponents": {"version": "file:../../node_modules/.pnpm/@blmcp+peento-businessComponents@0.0.63_@blmcp+peento-publicApiHub@1.0.12_@blmcp+ui@1.1.19_@f_lh7k7vqkvycomgcxoyfuppi3nm/node_modules/@blmcp/peento-businessComponents", "requires": {"@ant-design/icons": "5.3.7", "@blmcp/peento-publicApiHub": "1.0.12", "@blmcp/peento-request": "0.0.17", "@blmcp/peento-share-url-link": "^0.1.3", "@blmcp/ui": "1.1.19-beta.1", "@blmcp/ui-mobile": "1.1.0-rc.1", "@formily/antd-v5": "^1.1.1", "@formily/core": "^2.2.29", "@formily/react": "^2.2.29", "@modern-js-app/eslint-config": "2.53.0", "@modern-js/eslint-config": "2.53.0", "@modern-js/module-tools": "2.53.0", "@modern-js/plugin-rspress": "1.25.0", "@modern-js/plugin-testing": "2.53.0", "@modern-js/storybook": "2.52.0", "@modern-js/tsconfig": "2.53.0", "@storybook/addon-essentials": "7", "@types/jest": "29.2.4", "@types/lodash-es": "4.17.9", "@types/node": "16.11.7", "@types/react": "18.0.0", "@types/react-beautiful-dnd": "13.1.4", "@types/spark-md5": "3.0.2", "ahooks": "3.8.1", "antd": "5.16.0", "blm-monitor": "1.0.12", "blm-utils": "1.2.87-beta.3", "classnames": "^2.5.1", "cross-env": "^7.0.3", "dayjs": "1.11.10", "eslint": "8.46.0", "husky": "8.0.1", "lint-staged": "13.1.0", "lodash-es": "4.17.21", "pinyin-match": "1.2.4", "prettier": "2.8.1", "process": "0.11.10", "rc-virtual-list": "3.14.2", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-dom": "18.2.0", "react-tiny-virtual-list": "2.2.0", "rimraf": "3.0.2", "router-scan-bigbang": "1.3.42", "spark-md5": "3.0.2", "typescript": "5.0.4"}}, "@blmcp/ui": {"version": "file:../../node_modules/.pnpm/@blmcp+ui@1.1.19_@types+react@18.3.23_antd@5.26.3_dayjs@1.11.9_react-dom@18.3.1_react@18.3.1/node_modules/@blmcp/ui", "requires": {"@ant-design/icons": "^5.2.5", "@modern-js-app/eslint-config": "2.53.0", "@modern-js/eslint-config": "2.53.0", "@modern-js/module-tools": "2.53.0", "@modern-js/storybook": "2.53.0", "@modern-js/tsconfig": "2.53.0", "@storybook/addon-essentials": "^7", "@types/jest": "~29.2.4", "@types/node": "~16.11.7", "@types/react": "~18.0.26", "antd": "5.16.0", "antd-style": "^3.6.2", "classnames": "^2.3.2", "cross-env": "^7.0.3", "dayjs": "1.11.10", "fs": "0.0.1-security", "husky": "~8.0.1", "lint-staged": "~13.1.0", "path": "^0.12.7", "prettier": "~2.8.1", "react": "^18", "react-dom": "^18", "rimraf": "~3.0.2", "typescript": "~5.0.4"}}, "@blmlc/lego-init": {"version": "file:../../node_modules/.pnpm/@blmlc+lego-init@1.0.8-beta.30_@alifd+meet-react@2.9.9_@alifd+next@1.27.32_@types+react-dom@1_jhtdjrf35c6qz3anosm5kblpem/node_modules/@blmlc/lego-init", "requires": {"@alib/build-scripts": "0.1.32", "@alilc/lowcode-datasource-fetch-handler": "1.1.4", "@alilc/lowcode-plugin-inject": "1.2.3", "@alilc/lowcode-plugin-set-ref-prop": "1.0.1", "@alilc/lowcode-setter-behavior": "1.0.0", "@alilc/lowcode-setter-title": "1.0.2", "@alilc/lowcode-types": "1.2.3", "@babel/core": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-typescript": "7.23.3", "@blmcp/ui": "1.0.56", "babel-plugin-add-module-exports": "1.0.4", "babel-plugin-dynamic-import-node": "2.3.3", "build-plugin-component": "1.12.2", "cross-env": "7.0.3", "less": "4.2.0", "less-loader": "12.2.0", "rimraf": "5.0.5"}}, "@codemirror/autocomplete": {"version": "file:../../node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete", "requires": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}}, "@codemirror/commands": {"version": "file:../../node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands", "requires": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}}, "@codemirror/state": {"version": "file:../../node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state", "requires": {"@codemirror/buildhelper": "^1.0.0", "@marijn/find-cluster-break": "^1.0.0"}}, "@codemirror/view": {"version": "file:../../node_modules/.pnpm/@codemirror+view@6.38.0/node_modules/@codemirror/view", "requires": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/state": "^6.5.0", "crelt": "^1.0.6", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}}, "@ice/stark-app": {"version": "file:../../node_modules/.pnpm/@ice+stark-app@1.5.0/node_modules/@ice/stark-app", "requires": {"@commitlint/cli": "^7.5.2", "@commitlint/config-conventional": "^7.5.0", "@ice/spec": "^0.1.4", "@testing-library/jest-dom": "^4.2.3", "@types/jest": "^24.0.12", "@types/node": "^12.0.0", "codecov": "^3.4.0", "husky": "^2.2.0", "jest": "^24.7.1", "stylelint": "^10.1.0", "ts-jest": "^24.0.2", "typescript": "^3.4.4"}}, "@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "http://npm.bailongma-inc.com/@jridgewell%2fsourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "peer": true}, "@modern-js/module-tools": {"version": "file:../../node_modules/.pnpm/@modern-js+module-tools@2.67.11_typescript@5.8.3/node_modules/@modern-js/module-tools", "requires": {"@ampproject/remapping": "^2.3.0", "@ast-grep/napi": "0.35.0", "@babel/core": "^7.26.0", "@babel/types": "^7.26.0", "@modern-js/core": "2.67.11", "@modern-js/plugin": "2.67.11", "@modern-js/plugin-changeset": "2.67.11", "@modern-js/plugin-i18n": "2.67.11", "@modern-js/self": "npm:@modern-js/module-tools@2.67.11", "@modern-js/swc-plugins": "0.6.11", "@modern-js/types": "2.67.11", "@modern-js/utils": "2.67.11", "@rollup/pluginutils": "4.2.1", "@rsbuild/core": "1.3.22", "@scripts/build": "2.66.0", "@scripts/vitest-config": "2.66.0", "@swc/helpers": "^0.5.17", "@types/convert-source-map": "1.5.2", "@types/node": "^14", "convert-source-map": "1.9.0", "enhanced-resolve": "5.17.1", "esbuild": "0.19.2", "magic-string": "0.30.17", "postcss": "^8.4.35", "postcss-modules": "4.3.1", "safe-identifier": "0.4.2", "source-map": "0.7.4", "style-inject": "0.3.0", "sucrase": "3.29.0", "tapable": "2.2.1", "terser": "^5.31.1", "tsconfig-paths-webpack-plugin": "4.1.0", "typescript": "^5"}}, "@modern-js/storybook": {"version": "file:../../node_modules/.pnpm/@modern-js+storybook@2.53.0_@types+react-dom@19.1.6_@types+react@18.3.23_esbuild@0.18.20_reac_6fqujrfeai3npg6joquw2atbd4/node_modules/@modern-js/storybook", "requires": {"@modern-js/storybook-builder": "2.53.0", "@modern-js/utils": "2.53.0", "@storybook/react": "~7.6.1", "@storybook/types": "~7.6.12", "@types/node": "^20.5.6", "storybook": "~7.6.1", "typescript": "^5.2.2"}}, "@storybook/addon-essentials": {"version": "file:../../node_modules/.pnpm/@storybook+addon-essentials@7.6.20_@types+react-dom@19.1.6_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@storybook/addon-essentials", "requires": {"@storybook/addon-actions": "7.6.20", "@storybook/addon-backgrounds": "7.6.20", "@storybook/addon-controls": "7.6.20", "@storybook/addon-docs": "7.6.20", "@storybook/addon-highlight": "7.6.20", "@storybook/addon-measure": "7.6.20", "@storybook/addon-outline": "7.6.20", "@storybook/addon-toolbars": "7.6.20", "@storybook/addon-viewport": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/vue": "7.6.20", "ts-dedent": "^2.0.0", "typescript": "^4.9.3"}}, "@svgr/webpack": {"version": "file:../../node_modules/.pnpm/@svgr+webpack@5.5.0/node_modules/@svgr/webpack", "requires": {"@babel/core": "^7.12.3", "@babel/plugin-transform-react-constant-elements": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.5", "@svgr/core": "^5.5.0", "@svgr/plugin-jsx": "^5.5.0", "@svgr/plugin-svgo": "^5.5.0", "babel-loader": "^8.2.1", "loader-utils": "^2.0.0", "memory-fs": "^0.5.0", "url-loader": "^4.1.1", "webpack": "^5.4.0"}}, "@types/react": {"version": "file:../../node_modules/.pnpm/@types+react@18.3.23/node_modules/@types/react", "requires": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "@vue/compiler-core": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-core/-/compiler-core-3.5.18.tgz", "integrity": "sha512-3slwjQrrV1TO8MoXgy3aynDQ7lslj5UqDxuHnrzHtpON5CBinhWjJETciPngpin/T3OuW3tXUf86tEurusnztw==", "peer": true, "requires": {"@babel/parser": "^7.28.0", "@vue/shared": "3.5.18", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "@vue/compiler-dom": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-dom/-/compiler-dom-3.5.18.tgz", "integrity": "sha512-RMbU6NTU70++B1JyVJbNbeFkK+A+Q7y9XKE2EM4NLGm2WFR8x9MbAtWxPPLdm0wUkuZv9trpwfSlL6tjdIa1+A==", "peer": true, "requires": {"@vue/compiler-core": "3.5.18", "@vue/shared": "3.5.18"}}, "@vue/compiler-sfc": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-sfc/-/compiler-sfc-3.5.18.tgz", "integrity": "sha512-5aBjvGqsWs+MoxswZPoTB9nSDb3dhd1x30xrrltKujlCxo48j8HGDNj3QPhF4VIS0VQDUrA1xUfp2hEa+FNyXA==", "peer": true, "requires": {"@babel/parser": "^7.28.0", "@vue/compiler-core": "3.5.18", "@vue/compiler-dom": "3.5.18", "@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}}, "@vue/compiler-ssr": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fcompiler-ssr/-/compiler-ssr-3.5.18.tgz", "integrity": "sha512-xM16Ak7rSWHkM3m22NlmcdIM+K4BMyFARAfV9hYFl+SFuRzrZ3uGMNW05kA5pmeMa0X9X963Kgou7ufdbpOP9g==", "peer": true, "requires": {"@vue/compiler-dom": "3.5.18", "@vue/shared": "3.5.18"}}, "@vue/reactivity": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2freactivity/-/reactivity-3.5.18.tgz", "integrity": "sha512-x0vPO5Imw+3sChLM5Y+B6G1zPjwdOri9e8V21NnTnlEvkxatHEH5B5KEAJcjuzQ7BsjGrKtfzuQ5eQwXh8HXBg==", "peer": true, "requires": {"@vue/shared": "3.5.18"}}, "@vue/runtime-core": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fruntime-core/-/runtime-core-3.5.18.tgz", "integrity": "sha512-DUpHa1HpeOQEt6+3nheUfqVXRog2kivkXHUhoqJiKR33SO4x+a5uNOMkV487WPerQkL0vUuRvq/7JhRgLW3S+w==", "peer": true, "requires": {"@vue/reactivity": "3.5.18", "@vue/shared": "3.5.18"}}, "@vue/runtime-dom": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fruntime-dom/-/runtime-dom-3.5.18.tgz", "integrity": "sha512-YwDj71iV05j4RnzZnZtGaXwPoUWeRsqinblgVJwR8XTXYZ9D5PbahHQgsbmzUvCWNF6x7siQ89HgnX5eWkr3mw==", "peer": true, "requires": {"@vue/reactivity": "3.5.18", "@vue/runtime-core": "3.5.18", "@vue/shared": "3.5.18", "csstype": "^3.1.3"}}, "@vue/server-renderer": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fserver-renderer/-/server-renderer-3.5.18.tgz", "integrity": "sha512-PvIHLUoWgSbDG7zLHqSqaCoZvHi6NNmfVFOqO+OnwvqMz/tqQr3FuGWS8ufluNddk7ZLBJYMrjcw1c6XzR12mA==", "peer": true, "requires": {"@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18"}}, "@vue/shared": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/@vue%2fshared/-/shared-3.5.18.tgz", "integrity": "sha512-cZy8Dq+uuIXbxCZpuLd2GJdeSO/lIzIspC2WtkqIpje5QyFbvLaI5wZtdUjLHjGZrlVX6GilejatWwVYYRc8tA==", "peer": true}, "ahooks": {"version": "file:../../node_modules/.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1/node_modules/ahooks", "requires": {"@alifd/next": "^1.20.6", "@ant-design/icons": "^5.0.1", "@babel/runtime": "^7.21.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "antd": "^5.2.1", "dayjs": "^1.9.1", "intersection-observer": "^0.12.0", "jest-websocket-mock": "^2.1.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-drag-listview": "^0.1.6", "react-fast-compare": "^3.2.2", "react-json-view": "^1.21.3", "resize-observer-polyfill": "^1.5.1", "screenfull": "^5.0.0", "tslib": "^2.4.1"}}, "antd": {"version": "file:../../node_modules/.pnpm/antd@5.26.3_moment@2.30.1_react-dom@18.3.1_react@18.3.1/node_modules/antd", "requires": {"@ant-design/colors": "^7.2.1", "@ant-design/compatible": "^5.1.3", "@ant-design/cssinjs": "^1.23.0", "@ant-design/cssinjs-utils": "^1.1.3", "@ant-design/fast-color": "^2.0.6", "@ant-design/happy-work-theme": "^1.0.0", "@ant-design/icons": "^5.6.1", "@ant-design/react-slick": "~1.1.2", "@ant-design/tools": "^18.0.3", "@ant-design/v5-patch-for-react-19": "^1.0.2", "@antfu/eslint-config": "^4.15.0", "@antv/g6": "^4.8.24", "@babel/runtime": "^7.26.0", "@biomejs/biome": "^2.0.4", "@codecov/webpack-plugin": "^1.4.0", "@codesandbox/sandpack-react": "^2.19.10", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/css": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/server": "^11.11.0", "@eslint-react/eslint-plugin": "^1.17.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@inquirer/prompts": "^7.1.0", "@madccc/duplicate-package-checker-webpack-plugin": "^1.0.0", "@microflash/rehype-figure": "^2.1.1", "@npmcli/run-script": "^9.0.1", "@octokit/rest": "^22.0.0", "@prettier/sync": "^0.6.1", "@qixian.cs/github-contributors-list": "^2.0.2", "@rc-component/color-picker": "~2.0.1", "@rc-component/mutate-observer": "^1.1.0", "@rc-component/qrcode": "~1.0.0", "@rc-component/tour": "~1.15.1", "@rc-component/trigger": "^2.2.7", "@size-limit/file": "^11.1.6", "@stackblitz/sdk": "^1.11.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/adm-zip": "^0.5.6", "@types/ali-oss": "^6.16.11", "@types/cli-progress": "^3.11.6", "@types/css-tree": "^2.3.10", "@types/fs-extra": "^11.0.4", "@types/gtag.js": "^0.0.20", "@types/http-server": "^0.12.4", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/jest-environment-puppeteer": "^5.0.6", "@types/jest-image-snapshot": "^6.4.0", "@types/jquery": "^3.5.31", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.12", "@types/minimist": "^1.2.5", "@types/node": "^24.0.0", "@types/nprogress": "^0.2.3", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@types/prismjs": "^1.26.4", "@types/progress": "^2.0.7", "@types/react": "^19.0.1", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.0.2", "@types/react-highlight-words": "^0.20.0", "@types/react-resizable": "^3.0.8", "@types/semver": "^7.5.8", "@types/spinnies": "^0.5.3", "@types/tar": "^6.1.13", "@types/throttle-debounce": "^5.0.2", "@types/warning": "^3.0.3", "adm-zip": "^0.5.16", "ali-oss": "^6.21.0", "antd-img-crop": "^4.23.0", "antd-style": "^3.7.1", "antd-token-previewer": "^2.0.8", "axios": "^1.7.7", "chalk": "^5.0.0", "cheerio": "^1.0.0", "circular-dependency-plugin": "^5.2.2", "classnames": "^2.5.1", "cli-progress": "^3.12.0", "copy-to-clipboard": "^3.3.3", "cross-env": "^7.0.3", "cross-fetch": "^4.0.0", "css-tree": "^3.1.0", "csstree-validator": "^4.0.1", "cypress-image-diff-html-report": "2.2.0", "dayjs": "^1.11.11", "dekko": "^0.2.1", "dotenv": "^17.0.0", "dumi": "~2.4.20", "dumi-plugin-color-chunk": "^2.1.0", "env-paths": "^3.0.0", "eslint": "^9.23.0", "eslint-plugin-compat": "^6.0.1", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.14", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "gh-pages": "^6.2.0", "github-slugger": "^2.0.0", "glob": "^11.0.0", "hast-util-to-string": "^3.0.1", "html2sketch": "^1.0.2", "http-server": "^14.1.1", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "immer": "^10.1.1", "is-ci": "^4.0.0", "isomorphic-fetch": "^3.0.0", "jest": "^30.0.0", "jest-axe": "^10.0.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^30.0.0", "jest-image-snapshot": "^6.4.0", "jest-puppeteer": "^11.0.0", "jquery": "^3.7.1", "jsdom": "^26.0.0", "jsonml-to-react-element": "^1.1.11", "jsonml.js": "^0.1.0", "lint-staged": "^16.0.0", "lodash": "^4.17.21", "lunar-typescript": "^1.7.5", "lz-string": "^1.5.0", "minimist": "^1.2.8", "mockdate": "^3.0.5", "node-fetch": "^3.3.2", "node-notifier": "^10.0.1", "open": "^10.1.0", "ora": "^8.1.0", "p-all": "^5.0.0", "package-manager-detector": "^1.0.0", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "portfinder": "^1.0.37", "prettier": "^3.4.1", "pretty-format": "^30.0.0", "prismjs": "^1.29.0", "puppeteer": "^24.7.1", "rc-cascader": "~3.34.0", "rc-checkbox": "~3.5.0", "rc-collapse": "~3.9.0", "rc-dialog": "~9.6.0", "rc-drawer": "~7.3.0", "rc-dropdown": "~4.2.1", "rc-field-form": "~2.7.0", "rc-footer": "^0.6.8", "rc-image": "~7.12.0", "rc-input": "~1.8.0", "rc-input-number": "~9.5.0", "rc-mentions": "~2.20.0", "rc-menu": "~9.16.1", "rc-motion": "^2.9.5", "rc-notification": "~5.6.4", "rc-pagination": "~5.1.0", "rc-picker": "~4.11.3", "rc-progress": "~4.0.0", "rc-rate": "~2.13.1", "rc-resize-observer": "^1.4.3", "rc-segmented": "~2.7.0", "rc-select": "~14.16.8", "rc-slider": "~11.1.8", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-table": "~7.51.1", "rc-tabs": "~15.6.1", "rc-textarea": "~1.10.0", "rc-tooltip": "~6.4.0", "rc-tree": "~5.13.1", "rc-tree-select": "~5.27.0", "rc-tween-one": "^3.0.6", "rc-upload": "~4.9.2", "rc-util": "^5.44.4", "rc-virtual-list": "^3.19.1", "react": "^19.1.0", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-fast-marquee": "^1.6.5", "react-highlight-words": "^0.21.0", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.1", "react-resizable": "^3.0.5", "react-router-dom": "^7.0.1", "react-scan": "^0.4.2", "react-sticky-box": "^2.0.5", "regenerator-runtime": "^0.14.1", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-cli": "^12.0.1", "remark-gfm": "^4.0.0", "remark-lint": "^10.0.0", "remark-lint-no-undefined-references": "^5.0.0", "remark-preset-lint-recommended": "^7.0.0", "remark-rehype": "^11.1.1", "rimraf": "^6.0.1", "runes2": "^1.1.4", "scroll-into-view-if-needed": "^3.1.0", "semver": "^7.6.3", "sharp": "^0.34.0", "simple-git": "^3.27.0", "size-limit": "^11.1.6", "spinnies": "^0.5.1", "tar": "^7.4.3", "throttle-debounce": "^5.0.2", "tsx": "^4.20.3", "typedoc": "^0.28.0", "typescript": "~5.8.2", "vanilla-jsoneditor": "^3.0.0", "vanilla-tilt": "^1.8.1", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "xhr-mock": "^2.5.1"}}, "axios": {"version": "file:../../node_modules/.pnpm/axios@1.10.0/node_modules/axios", "requires": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "abortcontroller-polyfill": "^1.7.5", "auto-changelog": "^2.4.0", "body-parser": "^1.20.2", "chalk": "^5.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "dev-null": "^0.1.1", "dtslint": "^4.2.1", "es6-promise": "^4.2.8", "eslint": "^8.56.0", "express": "^4.18.2", "follow-redirects": "^1.15.6", "form-data": "^4.0.0", "formdata-node": "^5.0.1", "formidable": "^2.1.2", "fs-extra": "^10.1.0", "get-stream": "^3.0.0", "gulp": "^4.0.2", "gzip-size": "^7.0.0", "handlebars": "^4.7.8", "husky": "^8.0.3", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^2.99.1", "karma": "^6.3.17", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^1.1.2", "karma-jasmine-ajax": "^0.1.13", "karma-rollup-preprocessor": "^7.0.8", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "memoizee": "^0.4.15", "minimist": "^1.2.8", "mocha": "^10.3.0", "multer": "^1.4.4", "pretty-bytes": "^6.1.1", "proxy-from-env": "^1.1.0", "release-it": "^15.11.0", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-bundle-size": "^1.0.3", "rollup-plugin-terser": "^7.0.2", "sinon": "^4.5.0", "stream-throttle": "^0.1.3", "string-replace-async": "^3.0.2", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.9.5"}}, "blm-utils": {"version": "file:../../node_modules/.pnpm/blm-utils@1.2.88-rc.4/node_modules/blm-utils", "requires": {"@babel/core": "7.7.7", "@babel/plugin-transform-runtime": "7.4.4", "@babel/preset-env": "7.7.7", "@rollup/plugin-alias": "2.2.0", "@rollup/plugin-commonjs": "11.1.0", "@rollup/plugin-node-resolve": "10.0.0", "@rollup/plugin-replace": "2.4.2", "axios": "0.26.0", "axios-mock-adapter": "^2.0.0", "babel-jest": "^29.7.0", "babel-plugin-component": "1.1.1", "blueimp-md5": "2.15.0", "cross-env": "6.0.3", "element-ui": "3.2.16", "i18next": "23.6.0", "jest": "^29.7.0", "js-cookie": "2.2.1", "jsdom": "^24.1.1", "lodash": "4.17.21", "minimist": "1.2.0", "moment": "2.29.1", "path-to-regexp": "6.2.1", "qs": "6.10.3", "rollup": "1.27.13", "rollup-plugin-babel": "4.3.3", "rollup-plugin-terser": "5.1.3", "router-scan-bigbang": "1.3.31", "vue": "2.6.14"}}, "classnames": {"version": "file:../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames", "requires": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "classnames-local": "file:", "classnames-npm": "npm:classnames@*", "http-server": "^14.1.1", "markdown-table": "^3.0.3", "rollup": "^4.9.1", "tinybench": "^2.5.1", "tsd": "^0.30.1"}}, "click-to-react-component": {"version": "file:../../node_modules/.pnpm/click-to-react-component@1.1.0_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/click-to-react-component", "requires": {"@floating-ui/react-dom-interactions": "^0.3.1", "@types/react": "^18.0.6", "@types/react-reconciler": "^0.26.6", "eslint": "^8.0.0", "eslint-config-react-app": "^7.0.1", "htm": "^3.1.0", "react-merge-refs": "^1.1.0"}}, "cross-env": {"version": "file:../../node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env", "requires": {"cross-spawn": "^7.0.1", "kcd-scripts": "^5.5.0"}}, "csstype": {"version": "3.1.3", "resolved": "http://npm.bailongma-inc.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "peer": true}, "dayjs": {"version": "file:../../node_modules/.pnpm/dayjs@1.11.9/node_modules/dayjs", "requires": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}}, "echarts": {"version": "file:../../node_modules/.pnpm/echarts@5.6.0/node_modules/echarts", "requires": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "tslib": "2.3.0", "typescript": "4.4.3", "zrender": "5.6.1"}}, "entities": {"version": "4.5.0", "resolved": "http://npm.bailongma-inc.com/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "peer": true}, "estree-walker": {"version": "2.0.2", "resolved": "http://npm.bailongma-inc.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "peer": true}, "http-proxy-middleware": {"version": "file:../../node_modules/.pnpm/http-proxy-middleware@3.0.5/node_modules/http-proxy-middleware", "requires": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/js": "9.23.0", "@types/debug": "4.1.12", "@types/eslint": "9.6.1", "@types/express": "4.17.21", "@types/http-proxy": "^1.17.15", "@types/is-glob": "4.0.4", "@types/jest": "29.5.14", "@types/micromatch": "4.0.9", "@types/node": "22.10.2", "@types/supertest": "6.0.2", "@types/ws": "8.18.0", "body-parser": "1.20.3", "debug": "^4.3.6", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.3", "express": "4.21.2", "get-port": "5.1.1", "globals": "16.0.0", "http-proxy": "^1.18.1", "husky": "9.1.7", "is-glob": "^4.0.3", "is-plain-object": "^5.0.0", "jest": "29.7.0", "lint-staged": "15.5.0", "micromatch": "^4.0.8", "mockttp": "3.17.0", "open": "8.4.2", "patch-package": "8.0.0", "pkg-pr-new": "0.0.41", "prettier": "3.5.3", "supertest": "7.1.0", "ts-jest": "29.2.6", "typescript": "5.8.2", "typescript-eslint": "8.27.0", "ws": "8.18.1"}}, "js-cookie": {"version": "file:../../node_modules/.pnpm/js-cookie@2.2.1/node_modules/js-cookie", "requires": {"grunt": "1.0.3", "grunt-compare-size": "0.4.2", "grunt-contrib-connect": "2.0.0", "grunt-contrib-nodeunit": "2.0.0", "grunt-contrib-qunit": "2.0.0", "grunt-contrib-uglify": "2.3.0", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "21.0.0", "grunt-saucelabs": "9.0.0", "gzip-js": "0.3.2", "qunitjs": "1.23.1", "requirejs": "2.3.5"}}, "lodash-es": {"version": "file:../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es"}, "magic-string": {"version": "0.30.17", "resolved": "http://npm.bailongma-inc.com/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "peer": true, "requires": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "nanoid": {"version": "3.3.11", "resolved": "http://npm.bailongma-inc.com/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "peer": true}, "numeral": {"version": "file:../../node_modules/.pnpm/numeral@2.0.6/node_modules/numeral", "requires": {"chai": "^3.5.0", "grunt": "latest", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-jshint": "latest", "grunt-contrib-nodeunit": "1.0.0", "grunt-contrib-uglify": "latest", "grunt-karma": "^2.0.0", "grunt-mocha-test": "^0.13.2", "grunt-saucelabs": "*", "grunt-string-replace": "^1.3.1", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-firefox-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^1.1.0", "load-grunt-tasks": "^3.5.2", "mocha": "^3.1.2", "uglify-js": "latest"}}, "picocolors": {"version": "1.1.1", "resolved": "http://npm.bailongma-inc.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "peer": true}, "postcss": {"version": "8.5.6", "resolved": "http://npm.bailongma-inc.com/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "peer": true, "requires": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "prop-types": {"version": "file:../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types", "requires": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.5.0", "bundle-collapser": "^1.4.0", "eslint": "^8.6.0", "in-publish": "^2.0.1", "jest": "^19.0.2", "loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react": "^15.7.0", "react-is": "^16.13.1", "uglifyify": "^5.0.2", "uglifyjs": "^2.4.11"}}, "react": {"version": "file:../../node_modules/.pnpm/react@18.3.1/node_modules/react", "requires": {"loose-envify": "^1.1.0"}}, "react-dnd": {"version": "file:../../node_modules/.pnpm/react-dnd@16.0.1_@types+react@18.3.23_react@18.3.1/node_modules/react-dnd", "requires": {"@react-dnd/invariant": "^4.0.1", "@react-dnd/shallowequal": "^4.0.1", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@testing-library/react": "^13.1.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@types/react": "^18.0.5", "@types/react-dom": "^18.0.1", "dnd-core": "^16.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2", "jest": "^27.5.1", "jest-mock": "^27.5.1", "npm-run-all": "^4.1.5", "react": "^18.0.0", "react-dnd-test-backend": "portal:../backend-test", "react-dnd-test-utils": "portal:../test-utils", "react-dom": "^18.0.0", "shx": "^0.3.4", "typescript": "^4.6.3"}}, "react-dnd-html5-backend": {"version": "file:../../node_modules/.pnpm/react-dnd-html5-backend@16.0.1/node_modules/react-dnd-html5-backend", "requires": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@types/jest": "^27.4.1", "dnd-core": "^16.0.1", "npm-run-all": "^4.1.5", "shx": "^0.3.4", "typescript": "^4.6.3"}}, "react-dom": {"version": "file:../../node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom", "requires": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}}, "react-joyride": {"version": "file:../../node_modules/.pnpm/react-joyride@2.7.2_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/react-joyride", "requires": {"@arethetypeswrong/cli": "^0.13.5", "@gilbarbara/deep-equal": "^0.3.1", "@gilbarbara/eslint-config": "^0.7.2", "@gilbarbara/helpers": "^0.9.0", "@gilbarbara/node-helpers": "^0.1.0", "@gilbarbara/prettier-config": "^1.0.0", "@gilbarbara/tsconfig": "^0.2.3", "@playwright/experimental-ct-react": "^1.40.1", "@size-limit/preset-big-lib": "^11.0.1", "@swc/core": "^1.3.100", "@swc/jest": "^0.2.29", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@total-typescript/shoehorn": "^0.1.1", "@types/exenv": "^1.2.2", "@types/jest": "^29.5.11", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@types/scroll": "^3.0.3", "@types/scrollparent": "^2.0.3", "caniuse-lite": "^1.0.30001570", "cross-env": "^7.0.3", "csstype": "^3.1.3", "deep-diff": "^1.0.2", "deepmerge": "^4.3.1", "del-cli": "^5.1.0", "husky": "^8.0.3", "is-ci-cli": "^2.2.0", "is-lite": "^1.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.2", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-floater": "^0.7.9", "react-innertext": "^1.1.5", "react-is": "^16.13.1", "repo-tools": "^0.3.1", "scroll": "^3.0.1", "scrollparent": "^2.1.0", "size-limit": "^11.0.1", "tree-changes": "^0.11.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsup": "^8.0.1", "type-fest": "^4.8.3", "typescript": "^5.3.3"}}, "resize-observer-polyfill": {"version": "file:../../node_modules/.pnpm/resize-observer-polyfill@1.5.1/node_modules/resize-observer-polyfill", "requires": {"babel-eslint": "10.0.1", "cpy-cli": "2.0.0", "eslint": "5.10.0", "jasmine": "2.8.0", "jasmine-core": "2.8.0", "karma": "3.1.3", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-jasmine": "1.1.2", "karma-jasmine-html-reporter": "0.2.2", "karma-rollup-preprocessor": "6.1.1", "karma-sauce-launcher": "1.2.0", "karma-sourcemap-loader": "0.3.7", "karma-spec-reporter": "0.0.32", "promise-polyfill": "8.1.0", "rollup": "0.67.4", "rollup-plugin-typescript": "1.0.0", "typescript": "3.2.2"}}, "source-map-js": {"version": "1.2.1", "resolved": "http://npm.bailongma-inc.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "peer": true}, "stylelint": {"version": "file:../../node_modules/.pnpm/stylelint@15.11.0_typescript@5.8.3/node_modules/stylelint", "requires": {"@changesets/cli": "^2.26.2", "@changesets/get-github-info": "^0.5.2", "@csstools/css-parser-algorithms": "^2.3.1", "@csstools/css-tokenizer": "^2.2.0", "@csstools/media-query-list-parser": "^2.1.4", "@csstools/selector-specificity": "^3.0.0", "@jest/globals": "^29.6.4", "@stylelint/prettier-config": "^3.0.0", "@stylelint/remark-preset": "^4.0.0", "@types/balanced-match": "^1.0.3", "@types/css-tree": "^2.3.1", "@types/debug": "^4.1.8", "@types/file-entry-cache": "^5.0.2", "@types/global-modules": "^2.0.0", "@types/globjoin": "^0.1.0", "@types/imurmurhash": "^0.1.1", "@types/micromatch": "^4.0.2", "@types/normalize-path": "^3.0.0", "@types/postcss-less": "^4.0.2", "@types/postcss-resolve-nested-selector": "^0.1.0", "@types/postcss-safe-parser": "^5.0.1", "@types/style-search": "^0.1.3", "@types/svg-tags": "^1.0.0", "@types/write-file-atomic": "^4.0.0", "balanced-match": "^2.0.0", "benchmark": "^2.1.4", "colord": "^2.9.3", "common-tags": "^1.8.2", "cosmiconfig": "^8.2.0", "css-functions-list": "^3.2.1", "css-tree": "^2.3.1", "debug": "^4.3.4", "deepmerge": "^4.3.1", "eslint": "^8.48.0", "eslint-config-stylelint": "^20.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.2.3", "fast-glob": "^3.3.1", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^7.0.0", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.3.1", "husky": "^8.0.3", "ignore": "^5.2.4", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "jest": "^29.6.4", "jest-preset-stylelint": "^6.2.0", "jest-watch-typeahead": "^2.2.2", "known-css-properties": "^0.29.0", "lint-staged": "^14.0.1", "mathml-tag-names": "^2.1.3", "meow": "^10.1.5", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "np": "^8.0.4", "npm-run-all": "^4.1.5", "patch-package": "^8.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.28", "postcss-html": "^1.5.0", "postcss-import": "^15.1.0", "postcss-less": "^6.0.0", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.7", "postcss-selector-parser": "^6.0.13", "postcss-value-parser": "^4.2.0", "remark-cli": "^11.0.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "sugarss": "^4.0.1", "supports-hyperlinks": "^3.0.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "typescript": "^5.2.2", "write-file-atomic": "^5.0.1"}}, "typescript": {"version": "file:../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "requires": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}}, "vue": {"version": "3.5.18", "resolved": "http://npm.bailongma-inc.com/vue/-/vue-3.5.18.tgz", "integrity": "sha512-7W4Y4ZbMiQ3SEo+m9lnoNpV9xG7QVMLa+/0RFwwiAVkeYoyGXqWE85jabU4pllJNUzqfLShJ5YLptewhCWUgNA==", "peer": true, "requires": {"@vue/compiler-dom": "3.5.18", "@vue/compiler-sfc": "3.5.18", "@vue/runtime-dom": "3.5.18", "@vue/server-renderer": "3.5.18", "@vue/shared": "3.5.18"}}, "vuera": {"version": "0.2.7", "resolved": "http://npm.bailongma-inc.com/vuera/-/vuera-0.2.7.tgz", "integrity": "sha512-MZErNEK+xXjxBzIDFL9cdEtX3TDZk4pMJGB+WLrlFOVro9okS/FeOSpdPFkpdyZUImZN/or4CrmztA932ChGEQ==", "requires": {}}}}