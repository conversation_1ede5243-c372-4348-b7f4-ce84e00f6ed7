import config from '@/tool/config';
// 埋点文档：https://bailongma.yuque.com/pdc3yd/newbie/xod8gy7r9gzg6vog?singleDoc#RFhYN
// @ts-nocheck
interface SceneActionStartMonitor {
  sceneId: string; // 场景ID：场景-描述-风险等级，与起点一致
  uniqueId: number | string; // 唯一ID，若相同场景下存在同时触发多次此动作场景，需保证单次动作唯一性标识，与起点一致
  maxTime?: number; // 否	单位s，默认10分钟，若超过最大有效时间未触发结束点上报，则上报异常
  newPage?: boolean; // 是否是多页场景，若为true，在无对应起点时触发此终点不上报异常。此场景用于业务方无需做缓存逻辑可频繁触发上报终点。
  noLogin?: boolean; // 否	是否走非登录态场景上报
}

type AnalysisFunction = (params?: Record<any, any>) => void;

/**
 * 进入页面上报
 */
export const blmAnalysisPageView: AnalysisFunction = (params: any) => {
  window?.BlmAnalysis?.pageView?.(params);
};

/**
 * 离开页面上报
 */
export const blmAnalysisPageLeave = () => {
  window?.BlmAnalysis?.pageLeave?.();
};

/**
 * 点击上报
 */
export const blmAnalysisModuleClick: AnalysisFunction = (params) => {
  // console.log('blmAnalysisModuleClick', params);
  window?.BlmAnalysis?.moduleClick?.(params);
};

/**
 * 曝光上报
 */
export const blmAnalysisModuleExposure: AnalysisFunction = (params) => {
  window?.BlmAnalysis?.moduleExposure?.(params);
};

// 监控上报
export const reportBusinessMonitor = (
  eventType: string,
  ext?: any,
  level?: string,
) => {
  if (!config.get('sdkConfig.eventUpload')) return false;
  const extObject = ext || {};
  const { error } = extObject;

  const errorMsg =
    error instanceof Error
      ? error.stack
      : typeof error === 'string'
      ? error
      : JSON.stringify(error || '无错误信息');
  if (error) {
    console.error(error);
    extObject.error = errorMsg;
  }
  window?.$BLMLogReportCenter?.reportBusinessMonitor(
    eventType,
    extObject,
    level,
  );
};

// 监控用户行为时序
export const sceneActionStartMonitor = (params: SceneActionStartMonitor) => {
  if (!config.get('sdkConfig.eventUpload')) return false;
  window?.$BLMLogReportCenter?.sceneActionStartMonitor(params);
};
export const sceneActionEndMonitor = (params: SceneActionStartMonitor) => {
  if (!config.get('sdkConfig.eventUpload')) return false;
  window?.$BLMLogReportCenter?.sceneActionEndMonitor(params);
};

export const blmMicrofs = (expObj = {}) => {
  if (typeof window.$baseSelfReport === 'function') {
    const cloudTime =
      performance
        ?.getEntriesByType?.('resource')
        ?.find((v) => v.name.includes('/admin/v1/magic/common/getCloudConfig'))
        ?.duration || 'null';
    window.$baseSelfReport({
      entryTime:
        Date.now() - (window.$routerBeforeEachTime || window.htmlStartTime),
      entryTimeStemp: Date.now(),
      MAppRouterEndTime:
        window.$MAppRouterEndTime -
        (window.$routerBeforeEachTime || window.htmlStartTime),
      MAppMountTime:
        window.$MAppMountTime -
        (window.$routerBeforeEachTime || window.htmlStartTime),
      cloudTime,
      ...expObj,
    });
  }
};
