const env = window.envVar || 'daily';
const isProd = ['pre2', 'prepare2', 'pre', 'prepare', 'publish'].includes(env);
const envMap = {
  sit: '46d72856d9d2ad5475a6688e1f23b2c7',
  dev: '46d72856d9d2ad5475a6688e1f23b2c7',
  daily: '7d3e45cab1c85cf9b0d9d210a11757f8',
  daily2: '7d3e45cab1c85cf9b0d9d210a11757f8',
  pre2: 'f362002335e4de1a96c021fcae7a4255',
  pre: 'd9dc8d738480669adbda89454f58e98c',
  publish: '4805daa09b4ec254f336148856516ab5',
};

export default {
  env: 'use',
  key: envMap[env] || envMap.publish,
  onDemandRequest: true,
  configApi: '/bos/admin/v1/ai/platform/config/queryAppInfo',
  cdnPrefix: `https://${
    isProd ? window.cdnPubCosUrl || 'webstatic' : 'webstatic'
  }.yueyuechuxing.cn`,
}