/*
 * @Description: 请输入....
 * @Author: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 * @Date: 2025-06-09 18:03:29
 * @LastEditTime: 2025-06-09 20:52:40
 * @LastEditors: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 */
import { isLegoDev } from './common';

export const legoGeneralizeSwitch = (sceneKey = 'legoGeneralize') => {
  const devSwitch = window?.$HbUtils?.releaseCenter?.getSceneSwitch?.(
    `${sceneKey}-hb`,
  );
  // @ts-ignore
  const useSwitch = window?.$BLMReleaseCenter?.getSceneSwitch(sceneKey);
  const sceneSwitch = isLegoDev() ? devSwitch : useSwitch;
  return sceneSwitch ?? false;
};
