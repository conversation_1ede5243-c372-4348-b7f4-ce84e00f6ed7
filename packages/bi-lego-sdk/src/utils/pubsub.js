import { setVariable } from '@/utils/common';
class PubSub {
  constructor(ide) {
    this.subscriptions = setVariable(ide, {});
  }
  on(key, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Event handler should be a function')
    }
    !this.subscriptions[key] && (this.subscriptions[key] = [])
    this.subscriptions[key].push(handler)
    return ()=> {
      this.off(key, handler)
    }
  }
  dispatch(key, ...args) {
    const list = this.subscriptions[key] || [];
    const res = [];
    if (list.length == 0) {
      return
    }
    list.forEach((handlers) => {
      res.push(handlers.apply(this, args));
    });
    return res;
  }
  off(key, handler) {

    if(!key){
      for(let key in this.subscriptions){
        delete this.subscriptions[key]
      }
      return 
    }

    const fns = this.subscriptions[key];
    if (fns.length == 0) {
      return
    }

    if (!handler) {
      this.subscriptions[key].length = 0;
      delete this.subscriptions[key]
      return 
    }

    for (let i = 0, l = fns.length; i < l; i++) {
      if (handler == fns[i]) {
        this.subscriptions[key].splice(i, 1);
      }
    }
  }
  getSubscript(key){
    return key ? this.subscriptions[key] : this.subscriptions
  }
  has(dispatchRes, par, option = {}){
    const someOrEvery = option.someOrEvery || 'some'
    if(par instanceof Array){
      return dispatchRes[someOrEvery](res => par.includes(res))
    }
    return dispatchRes[someOrEvery](res => res === par)
  }
  conditionDispatch(dispatchRes, option, fun){
    if(!dispatchRes || dispatchRes.length === 0) {
      fun()
      return 
    }
    const par = typeof option === 'object' ? option.condition : option;
    if(this.has(dispatchRes, par, option) === !option.reversal){
      fun()
    }
  }
}

export default PubSub
