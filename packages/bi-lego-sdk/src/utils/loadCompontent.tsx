import { lazy } from 'react';

interface Route {
  element: any;
  id: string;
  name: string;
  pageKey: string;
  parentId: string;
  path: string;
  file?: string;
}

interface Routes {
  routes: Route[];
  children: Route[];
}

type PromiseFun = (route: Route) => Promise<any>;

interface Option {
  [key: string]:
    | {
        async?: boolean;
        file: PromiseFun | object;
        import?: string[];
      }
    | PromiseFun;
}

const loadedMap: any = {};
export function loadScript(url: string) {
  return new Promise((resolve, reject) => {
    if (loadedMap[url]) {
      resolve('');
      return;
    }
    const Script = document.createElement('script');
    Script.type = 'text/javascript';
    Script.setAttribute('ignore', 'true');
    Script.src = url;

    function onload(e: any) {
      Script.onload = null;
      Script.onerror = null;
      if (e.type === 'load') {
        loadedMap[url] = true;
        resolve('');
      } else {
        reject('');
      }
    }
    Script.onload = onload;
    Script.onerror = onload;
    document.head.appendChild(Script);
    document.head.removeChild(Script);
    Script.onerror = reject;
  });
}

export function loadStyle(url: string) {
  return new Promise((resolve, reject) => {
    if (loadedMap[url]) {
      resolve('');
      return;
    }
    const element = document.createElement('link');
    function onload(e: any) {
      element.onload = null;
      element.onerror = null;
      if (e.type === 'load') {
        loadedMap[url] = true;
        resolve('');
      } else {
        reject('');
      }
    }

    element.onload = onload;
    element.onerror = onload;
    element.href = url;
    element.rel = 'stylesheet';
    document.head.appendChild(element);
  });
}

export function loadCDN(url: string) {
  return (url.includes('.css') && loadStyle(url)) || loadScript(url);
}

export function asyncLoadCompontent(routes: Routes[], option: Option) {
  Object.keys(option).forEach((key) => {
    const index = routes[0].routes.findIndex((f) => f.pageKey === key);
    const route = routes[0].routes[index];
    const setting = option[key];
    const Compontent =
      typeof setting !== 'function' && setting.async === false
        ? setting.file
        : lazy(() => {
            if (typeof setting === 'function') {
              return (setting as PromiseFun)(route);
            } else {
              // @ts-expect-error
              const ps = setting.import.map((url: string) => loadCDN(url));
              // @ts-expect-error
              ps.unshift(setting.file(route));
              return Promise.all(ps).then((arr) => arr[0]);
            }
          });
    // @ts-expect-error
    routes[0].routes[index].element = <Compontent />;
    // @ts-expect-error
    routes[0].children[index].element = <Compontent />;
  });
}

export function preloadResource(url: string) {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = url;
  link.as = url.includes('.css') ? 'style' : 'script';
  document.head.appendChild(link);
}
