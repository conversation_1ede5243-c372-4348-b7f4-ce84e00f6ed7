import { legoGeneralizeSwitch } from '../sceneSwitch';
import { noProxyUrls, proxyConfigMaps } from './const';
import configUtils from '../../tool/config';
import { getLegoEnv } from '../common';

interface ILegoProxyConfig {
  headers: { [key: string]: string };
  baseURL: string;
}

/**
 * 根据路径从对象中获取值
 * @param obj 源对象
 * @param path 属性路径，如 'a.b.c'
 * @param defaultValue 默认值
 */
const getValueByPath = (obj, path, defaultValue = undefined) => {
  return path
    .split('.')
    .reduce(
      (acc, key) => (acc && acc[key] !== undefined ? acc[key] : defaultValue),
      obj,
    );
};

/**
 * 对象结构转换
 * @param obj 源对象
 * @param mapping 映射关系
 */
const transform = (obj, mapping) => {
  return Object.entries(mapping).reduce((result, [newKey, path]) => {
    result[newKey] = getValueByPath(obj, path);
    return result;
  }, {});
};

/**
 * 获取LEGO代理配置
 */
const getLegoProxyConfig = async () => {
  const env = getLegoEnv();
  const proxyConfigMap = proxyConfigMaps[env] || {};
  const legoConfig = await configUtils.getLegoConfig();
  // 获取当前环境配置
  return legoConfig
    ? (transform(legoConfig, proxyConfigMap) as ILegoProxyConfig)
    : null;
};

/**
 * 检查是否为无需代理的URL,支持正则匹配
 * @param url 请求URL
 */
const shouldSkipProxy = (url: string): boolean => {
  return noProxyUrls.some((pattern) => {
    if (pattern.startsWith('^')) {
      // 如果是正则表达式模式
      return new RegExp(pattern).test(url);
    }
    return url === pattern;
  });
};

export const apiProxy = async (config: any) => {
  // 如果是无需代理的URL，直接返回原配置
  if (shouldSkipProxy(config.url)) {
    return config;
  }

  // 获取当前环境的代理配置
  const legoConfig = (await getLegoProxyConfig()) as ILegoProxyConfig;

  if (!legoConfig) {
    return config;
  }

  config.url = config.url.replace(/^\/admin\/v1\/ai/, legoConfig.baseURL);
  Object.assign(config.headers, legoConfig.headers, {
    'lego-generalize': legoGeneralizeSwitch() ? 1 : 0,
  });

  return config;

  // 如果没有配置或已初始化，直接返回原配置
  // if (!legoConfig || window.legoConfigIsInit) {
  //   return config;
  // } else {
  //   // @ts-ignore
  //   window.legoConfigIsInit = true; // 标记为初始化状态
  //   return {
  //     ...config,
  //     ...legoConfig,
  //     baseURL: `${window.location.origin}${legoConfig.baseURL || ''}`,
  //     headers: {
  //       ...config.headers,
  //       ...legoConfig.headers,
  //       'lego-generalize': legoGeneralizeSwitch() ? 1 : 0,
  //     },
  //   };
  // }
};
