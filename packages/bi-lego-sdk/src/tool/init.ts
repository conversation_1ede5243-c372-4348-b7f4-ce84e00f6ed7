import config from './config';

interface Option {
  key: string;
  request?: any;
  env: 'dev' | 'use';
  configApi: string;
  onDemandRequest?: boolean;
  cdnPrefix?: string;
}

export default async function legoInit(option: Option) {
  if (!option || !option.key) {
    throw new Error('Initialization option is required with a valid key.');
  }
  // if (!option || !option.request) {
  //   throw new Error('Initialization option is required with a valid request.');
  // }
  if (!option || !option.env) {
    throw new Error('Initialization option is required with a valid env.');
  }
  if (!option || !option.configApi) {
    throw new Error(
      'Initialization option is required with a valid configApi.',
    );
  }
  window.isLegoRequestUsed = false;
  config.set('setting', option);

  if (!option.onDemandRequest) {
    await config.getLegoConfig();
  }
}
