import Store, { useStore } from '@/utils/store';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';

interface StoreConfig {
  sensitiveState: number;
  customAgreement: {
    agreementName: string;
    agreementLink: string;
  };
  reportName: string;
  canExport: boolean;
  canEditReport: boolean;
  isTemplate: boolean;
  reportId: string;
  pageMark: string;
  legoMinAuthLevel: number;
  publishStatus: number;
}

class ReportStore extends Store<StoreConfig> {
  constructor() {
    super('useLegoReport');
  }
  get(key: string) {
    if (this.state[key] === undefined) {
      for (let k in this.state) {
        if (this.state[k].reportId === key || this.state[k].pageMark === key) {
          return this.state[k];
        }
      }
    }
    return this.state[key];
  }
}

export const store = new ReportStore();

export function useLegoReport(key: string) {
  return useStore<StoreConfig>(store, key, {} as StoreConfig);
}

function create(reportId?: string) {
  const merge = reportId
    ? (value) => store.merge(reportId, value)
    : (value) => store.mergeAll(value);
  const forEach = (fun: Function) => {
    if (reportId) {
      fun(reportId);
      return;
    }
    store.keys().forEach((key) => {
      fun(key);
    });
  };
  return {
    setSensitiveState(b) {
      merge({
        sensitiveState: b ? 1 : 0,
      });
    },
    query(data) {
      if (data) {
        this.setFormData(data);
      }
      forEach((key) => {
        relationCenterExp(key).notify('all');
      });
    },

    setFormData(data) {
      forEach((key) => {
        const queryCenter = queryCenterExp(key);
        const queryData = queryCenter.query;
        const findK = (k) => {
          for (let k2 in queryData) {
            if (queryData[k2].key === k) {
              return k2;
            }
          }
        };
        for (let k in data) {
          const ck = findK(k);
          const origin = queryData[ck as any];
          if (
            origin &&
            Object.prototype.toString.call(data[k]) === '[object object]'
          ) {
            queryCenter.setQuery(ck, {
              ...origin,
              ...data[k],
            });
          } else if (origin) {
            queryCenter.setQuery(ck, {
              ...origin,
              fieldValue: data[k],
              fieldLabel: data[k],
            });
          }
        }
      });
    },
    getFormData() {
      let _reportId = reportId;
      const map = store.keys().reduce((o, key) => {
        _reportId = reportId || key;
        const f = queryCenterExp(key).getQuery().filterInfo || [];
        o[key] = f.reduce((o, v) => {
          o[v.key] = v;
          return o;
        }, {});
        return o;
      }, {});

      return map[_reportId as any] && map[_reportId as any];
    },
  };
}

const op = create();

for (const key in op) {
  create[key] = op[key];
}

export default create;
