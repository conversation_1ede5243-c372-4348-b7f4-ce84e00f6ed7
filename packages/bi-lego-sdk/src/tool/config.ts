import Store, { useStore } from '@/utils/store';

const store = new Store<any>('useTemplateConfig');

type State = 'idle' | 'loading' | 'loaded' | 'error';
class Config {
  public key: string = '';
  public promise: Promise<any> | null = null;
  constructor() {}

  async getLegoConfig() {
    if (store.get('state') !== 'loaded') {
      await this.callRequest();
    }
    return store.state;
  }

  async callRequest() {
    this.key = this.key || this.get('setting.key');

    if (!this.key) {
      throw new Error('lego key is required');
    }

    return this.promise || this.request(this.key);
  }

  request(key: string, retry = false) {
    this.setState('loading');
    this.promise = this.get('setting.request')({
      url: this.get('setting.configApi'),
      method: 'post',
      cache: true,
      data: {
        appId: key,
      },
    })
      .then((res) => {
        try {
          store.set('basicInfo', JSON.parse(res.data.basicInfo || ''));
          store.set('dataSetConfig', JSON.parse(res.data.dataSetConfig || ''));
          store.set('sdkConfig', JSON.parse(res.data.sdkConfig || ''));
          this.setState('loaded');
        } catch (e) {
          this.setState('error');
        }
        this.promise = null;
      })
      .catch(() => {
        if (!retry) {
          return this.request(key, true);
        } else {
          this.setState('error');
          this.promise = null;
        }
      });

    return this.promise;
  }

  get(key: string) {
    if (key.includes('.')) {
      return key.split('.').reduce((acc, cur) => acc && acc[cur], store.state);
    }
    return key ? store.get(key) : store.state;
  }

  set(key: string, value: any) {
    store.set(key, value);
  }

  merge(key: string, value: any) {
    store.merge(key, value);
  }

  setState(state: State) {
    store.set('state', state);
  }
}

function useTemplateConfig<T>(key: string) {
  return useStore<T>(store, key);
}

export { store, useTemplateConfig };

export default new Config();
