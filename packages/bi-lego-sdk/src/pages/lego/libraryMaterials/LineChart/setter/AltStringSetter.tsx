import * as React from 'react';
import { Input } from '@blmcp/ui';
// import { Input } from "@alifd/next";

interface SetterProps {
  // 当前值
  value: string;
  // 默认值
  initialValue: string;
  // setter唯一输出
  onChange: (val: string) => void;
  // AltStringSetter 特殊配置
  label: string;
  placeholder: string;
}

export default class AltStringSetter extends React.PureComponent<SetterProps> {
  componentDidMount() {
    const { onChange, value, initialValue } = this.props;
    if (value === undefined && initialValue) {
      onChange(initialValue);
    }
  }

  // 声明Setter的title
  static displayName = 'AltStringSetter';

  render() {
    const { onChange, value = '', label = '', placeholder } = this.props;
    return (
      <div>
        <Input
          value={value}
          placeholder={placeholder || ''}
          onChange={(value) => {
            onChange(value.target.value);
          }}
        ></Input>
      </div>
    );
  }
}
