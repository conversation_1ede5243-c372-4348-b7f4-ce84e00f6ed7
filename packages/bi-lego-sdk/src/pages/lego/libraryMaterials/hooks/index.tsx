import { queryData } from '../../api/index';
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import { ResData } from '../../components/types';

interface ChartData {
  data?: ResData;
  query: (params: unknown) => void;
  loading: boolean;
}

// 获取图表数据的统一hooks
export function useChartData(type: string): ChartData {
  const [data, setData] = useState<ResData>();
  const { loading, run } = useRequest(queryData, {
    manual: true,
    onSuccess: (result) => {
      setData(result.data);
    },
    onError: (error) => {
      console.error(error);
    },
  });

  useEffect(() => {
    run({ type });
  }, [run, type]);
  return { data, query: run, loading };
}
