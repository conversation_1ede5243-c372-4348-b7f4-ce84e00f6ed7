import React from 'react';
import { Switch } from '@blmcp/ui';
// import { isHubble } from '@/utils/hubble';
import styles from './index.module.less';
import { isLegoDev } from '@/utils/common';

interface SetterProps {
  value: any;
  onChange: any;
  type: string;
  field: any;
  noPadding: boolean;
}

export default ({ onChange, value, noPadding }: SetterProps) => {
  const selectVal = value || false;
  const switchChange = (value: any) => {
    onChange(value);
  };
  return (
    <div style={noPadding ? { marginLeft: '-10px' } : {}}>
      <div className={styles['setter-date-picker-default-value']}>
        <div className={styles['title']}>是否隐藏标题</div>
        <div className={styles['span-text']}>
          否
          <Switch defaultChecked={selectVal} onChange={switchChange} />是
        </div>
      </div>
    </div>
  );
};
