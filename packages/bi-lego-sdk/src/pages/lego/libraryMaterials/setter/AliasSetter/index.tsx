import React from 'react';
import { Input, Tooltip } from '@blmcp/ui';
// import { isHubble } from '@/utils/hubble';
import styles from './index.module.less';
import { InfoCircleOutlined } from '@ant-design/icons';
import { store } from '@/pages/lego/hooks/useComponent';

interface SetterProps {
  value: any;
  onChange: any;
  isDate?: boolean;
}

export default ({ onChange, value, isDate, selected }: SetterProps) => {
  return (
    <div className={styles['setter-date-picker-default-value']}>
      <div className={styles['title']}>筛选器名称</div>
      <div className={styles['span-text']}>
        <Input
          value={value}
          allowClear
          placeholder="请输入"
          showCount
          maxLength={50}
          onChange={(event) => {
            store.merge(selected._id, {
              title: event.target.value,
            });
            onChange(event.target.value);
          }}
        ></Input>
      </div>
    </div>
  );
};
