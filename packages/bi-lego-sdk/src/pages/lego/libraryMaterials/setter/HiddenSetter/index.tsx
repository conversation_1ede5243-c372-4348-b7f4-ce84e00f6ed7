import React from 'react';
import { Switch } from '@blmcp/ui';
// import { isHubble } from '@/utils/hubble';
import styles from './index.module.less';
import { isLegoDev } from '@/utils/common';
import { getSelectComponentNode } from '@/pages/lego/utils';
import { store } from '@/pages/lego/hooks/useComponent';

interface SetterProps {
  value: any;
  onChange: any;
  type: string;
  field: any;
  noPadding: boolean;
}

export default ({ onChange, value, noPadding }: SetterProps) => {
  const selectVal = value || false;
  const switchChange = (value: any) => {
    onChange(value);
    const node = getSelectComponentNode();
    store.merge(node.id, {
      hidden: value,
    });
  };

  return (
    (isLegoDev() && (
      <div>
        <div className={styles['setter-date-picker-default-value']}>
          <div className={styles['title']}>是否隐藏（查看态生效）</div>
          <div className={styles['span-text']}>
            否
            <Switch defaultChecked={selectVal} onChange={switchChange} />是
          </div>
        </div>
      </div>
    )) ||
    null
  );
};
