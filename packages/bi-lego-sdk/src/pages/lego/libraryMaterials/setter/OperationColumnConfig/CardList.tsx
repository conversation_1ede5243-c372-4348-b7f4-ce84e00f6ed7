// @ts-nocheck
import { Card } from './Card';

export const CardList = ({
  value,
  deleteItem,
  inputChange,
  moveCard,
  configItem,
  disabled,
}) => {
  return (
    <div>
      {value?.map((item, index: number) => {
        return (
          <Card
            key={item.key}
            disabled={disabled}
            deleteItem={deleteItem(index)}
            configItem={configItem(index)}
            inputChange={inputChange(index)}
            item={item}
            index={index}
            moveCard={moveCard}
          />
        );
      })}
    </div>
  );
};
