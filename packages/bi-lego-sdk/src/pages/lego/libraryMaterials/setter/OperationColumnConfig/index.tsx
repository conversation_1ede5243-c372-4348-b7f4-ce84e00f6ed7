// @ts-nocheck
import { <PERSON><PERSON>, Checkbox, Modal, Tooltip } from '@blmcp/ui';
import {
  SyntheticEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { PlusOutlined } from '@ant-design/icons';

import {
  EditorView,
  ViewUpdate,
  MatchDecorator,
  Decoration,
  ViewPlugin,
  DecorationSet,
  placeholder,
} from '@codemirror/view';
import { EditorState } from '@codemirror/state';
import { autocompletion } from '@codemirror/autocomplete';
import { TopDnDProvider } from '@/pages/lego/libraryMaterials/setter/TopDnDProvider';
import { ReactComponent as WarningIcon } from '@/assets/lego/warning.svg';
import Search from '@/pages/lego/modules/editPage/components/Search';

import styles from './index.module.less';
import { CardList } from './CardList';

const { confirm } = Modal;
import './index.module.less';

interface OperationItem {
  label: string;
  url: string;
}

interface OperationColumnConfigProps {
  onChange: (val: { freeze: boolean; operationList: OperationItem[] }) => void;
  value: { freeze: boolean; operationList: OperationItem[] };
  field: any;
}

interface Fields {
  columnId: string;
  dataType: number;
  fieldType: string;
  id: number;
  isAggr: boolean;
  key: string;
  title: string;
}

export const OperationColumnConfig = (props: OperationColumnConfigProps) => {
  const {
    onChange,
    value = { freeze: true, operationList: [] },
    field,
  } = props;

  const { freeze = true, operationList = [] } = value;
  const { event } = window.AliLowCodeEngine || {};

  const propsField = field.parent;
  const dataSetConfig = propsField.getPropValue('dataSetConfig');
  const [fieldsAllEnum, setFieldsAllEnum] = useState<Fields[]>([]);
  const [disabled, setDisabled] = useState(
    dataSetConfig?.contrastInfo?.length > 0,
  );
  const [current, setCurrent] = useState<number>(-1);
  const editRef = useRef<HTMLDivElement>();
  const viewRef = useRef<HTMLDivElement>();

  useEffect(() => {
    const fields = dataSetConfig?.dimensionInfo?.filter(
      (it) => it.fieldType === 'dimensionInfo',
    );
    setFieldsAllEnum(fields ?? []);
  }, [dataSetConfig?.dimensionInfo?.length]);

  const bindEvent = useCallback((disable: boolean) => {
    setDisabled(disable);
  }, []);

  const bindDimChangeEvent = useCallback((dimensionInfo: any) => {
    setFieldsAllEnum(dimensionInfo ?? []);
  }, []);

  useEffect(() => {
    event.on('common:operationColumnConfig.bindEvent', bindEvent);
    return () => event.off(`common:operationColumnConfig.bindEvent`, bindEvent);
  }, [bindEvent]);
  useEffect(() => {
    event.on('common:operationDimChange.bindEvent', bindDimChangeEvent);
    return () =>
      event.off(`common:operationDimChange.bindEvent`, bindDimChangeEvent);
  }, [bindDimChangeEvent]);

  const [isModalOpen, setModalOpen] = useState(false);

  // 关键字搜索
  const keyWordPlugin = useCallback(() => {
    if (fieldsAllEnum.length === 0) {
      return [];
    }

    const indexDatas =
      fieldsAllEnum?.length > 0
        ? fieldsAllEnum.map((v) => v.title).sort((a, b) => b.length - a.length)
        : [];
    // const keywords = [];
    const placeholderMatcher = new MatchDecorator({
      regexp: new RegExp(`${indexDatas.join('|')}|'`, 'gi'),
      decoration: function (match, view, pos) {
        const lineText = view.state.doc?.text?.join() || '';
        const [matchText] = match;
        if (
          matchText === "'" ||
          (lineText[pos - 1] === '[' &&
            lineText[pos + matchText.length] === ']')
        ) {
          return Decoration.mark({
            attributes: {
              style: 'color: #FF9C2A;',
            },
          });
        } else {
          Decoration.mark({});
        }
      },
    });

    return [
      ViewPlugin.fromClass(
        class {
          decorations: DecorationSet;
          constructor(view: EditorView) {
            this.decorations = placeholderMatcher.createDeco(view);
          }
          update(update: ViewUpdate) {
            this.decorations = placeholderMatcher.updateDeco(
              update,
              this.decorations,
            );
          }
        },
        {
          decorations: (v) => v.decorations,
        },
      ),
    ];
  }, [fieldsAllEnum]);

  // 输入提示-数据集
  const indexHelpWordPlugin = useCallback(
    (context: any) => {
      let word = context.matchBefore(/[\u4e00-\u9fa5]*|\w*/);
      if (word?.from === word?.to && !context.explicit) return null;
      return {
        from: word.from,
        options:
          fieldsAllEnum?.length > 0
            ? fieldsAllEnum.map((v) => ({
                label: v.title,
                type: 'keyword',
                apply: `[${v.title}]`,
                detail: '维度',
              }))
            : [],
      };
    },
    [fieldsAllEnum],
  );

  const inputChange = (index: number) => (val: string) => {
    const newValue = [...operationList];
    newValue[index]['label'] = val;
    onChange({ freeze, operationList: newValue });
  };

  // 删除某个元素
  const deleteItem = (index: number) => () => {
    confirm({
      title: '确认删除吗？',
      icon: <WarningIcon className={styles['warning-icon']} />,
      content: '即将删除配置的超链接，是否继续？',
      autoFocusButton: null,
      onOk() {
        const newValue = [...operationList];
        newValue.splice(index, 1);
        onChange({ freeze, operationList: newValue });
      },
      onCancel() {},
    });
  };

  // 移动卡片
  const moveCard = (dragIndex: number, hoverIndex: number) => {
    const newValue = [...operationList];
    const item = newValue.splice(dragIndex, 1);
    newValue.splice(hoverIndex, 0, item[0]);
    onChange({ freeze, operationList: newValue });
  };

  const configItem = useCallback(
    (index: number) => () => {
      setCurrent(index);
      setModalOpen(true);
      const text = operationList[index]?.url ?? '';
      setTimeout(() => {
        // 没有初始化，第一次创建
        if (!viewRef.current) {
          let startState = EditorState.create({
            doc: '',
            extensions: [
              placeholder(
                '请输入链接地址，可使用维度或指标配置超链接的动态参数',
              ),
              EditorView.lineWrapping,
              autocompletion({ override: [indexHelpWordPlugin] }),
            ].concat(keyWordPlugin()),
          });
          viewRef.current = new EditorView({
            state: startState,
            parent: editRef.current,
          });
          if (text?.length > 0) {
            viewRef?.current?.dispatch?.({
              changes: {
                from: 0,
                to: viewRef.current.state.doc.length,
                insert: text,
              },
              selection: {
                anchor: text.length,
              },
              startState,
            });
            viewRef?.current?.focus();
          }
        } else {
          let startState = EditorState.create({
            extensions: [
              placeholder(
                '请输入链接地址，可使用维度或指标配置超链接的动态参数',
              ),
              EditorView.lineWrapping,
              autocompletion({ override: [indexHelpWordPlugin] }),
            ].concat(keyWordPlugin()),
          });
          viewRef?.current?.setState(startState);

          viewRef?.current?.dispatch?.({
            changes: {
              from: 0,
              to: viewRef.current.state.doc.length,
              insert: text,
            },
            selection: {
              anchor: text.length,
            },
          });
          viewRef?.current?.focus();
        }
      }, 100);
    },
    [indexHelpWordPlugin, keyWordPlugin, operationList],
  );
  const handleOk = () => {
    const richUrl = viewRef?.current?.state?.doc?.toString();
    const newValue = [...operationList];
    newValue[current].url = richUrl;
    onChange({ freeze, operationList: newValue });
    setModalOpen(false);
  };

  // 插入
  const insertText = useCallback((text: string) => {
    const view = viewRef.current!;
    if (!view) return;
    const { state } = view;
    const [range] = state.selection.ranges || [];

    let from = range.from;
    let to = range.to;
    view.focus();

    view.dispatch({
      changes: {
        from: from,
        to: to,
        insert: text,
      },
      selection: {
        anchor: from + text.length,
      },
    });
  }, []);
  const clickField = (text: string) => () => {
    insertText(`[${text}]`);
  };
  //operationColsFreeze

  const onChangeSwitch = useCallback(
    (e: SyntheticEvent) => {
      onChange({ freeze: e.target?.checked, operationList });
    },
    [onChange, operationList],
  );

  return (
    <TopDnDProvider>
      <div>
        <div className={styles['setting-title']}>操作列设置</div>
        <div className={styles['freeze-cols']}>
          <Checkbox
            checked={freeze}
            onChange={onChangeSwitch}
            disabled={disabled}
          ></Checkbox>
          <span className={styles['freeze-title']}> 冻结操作列</span>
        </div>
        {operationList?.length > 0 ? (
          <CardList
            value={operationList}
            disabled={disabled}
            moveCard={moveCard}
            configItem={configItem}
            deleteItem={deleteItem}
            inputChange={inputChange}
          />
        ) : null}

        <Tooltip
          title={'最多支持配置5个操作列'}
          open={operationList?.length >= 5 ? undefined : false}
        >
          <Button
            type="dashed"
            disabled={operationList?.length >= 5 || disabled}
            icon={<PlusOutlined />}
            className={styles['add-btn']}
            onClick={() => {
              const newValue = [
                ...operationList,
                {
                  label: '未命名',
                  key: 'operation' + Date.now(),
                  placeholder: ' ',
                },
              ];

              onChange({ freeze, operationList: newValue });
            }}
          >
            添加操作列
          </Button>
        </Tooltip>
        <Modal
          title={`操作列设置`}
          width={720}
          open={isModalOpen}
          closable={true}
          centered={true}
          okText="保存"
          onOk={handleOk}
          onCancel={() => setModalOpen(false)}
        >
          <div className={styles.title}>
            规则类型：<span className={styles.text}>超链接设置</span>
          </div>

          <div className={styles.content}>
            <div className={styles.left}>
              <Search
                placeholder="请输入关键字查询"
                description={'找不到关键字'}
                data={fieldsAllEnum.map((v) => ({
                  ...v,
                  label: v.title,
                  value: v.key,
                }))}
                onChange={(v) => insertText(`[${v.title}]`)}
              />
              <div className={styles['list-title']}>维度</div>
              <div className={styles.list}>
                {fieldsAllEnum.map((it, index) => {
                  return (
                    <div
                      key={it.key + index}
                      className={styles['list-item']}
                      onClick={clickField(it.title)}
                    >
                      {it.title}
                    </div>
                  );
                })}
              </div>
            </div>
            <div className={styles.right + ' lego-bi-scroll-hide'}>
              <div ref={editRef} className={styles.editBox}></div>
            </div>
          </div>
        </Modal>
      </div>
    </TopDnDProvider>
  );
};
