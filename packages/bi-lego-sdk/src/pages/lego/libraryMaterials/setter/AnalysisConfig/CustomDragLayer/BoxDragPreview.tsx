// @ts-nocheck
import { memo } from 'react';
import type { FC } from 'react';
import { Box } from '../Box';

export interface BoxDragPreviewProps {
  title: string;
}

export interface BoxDragPreviewState {
  tickTock: any;
}
export const BoxDragPreview: FC<BoxDragPreviewProps> = memo(
  function BoxDragPreview({ title }) {
    return (
      <div style={{ color: 'red' }}>
        <Box title={title} preview />
      </div>
    );
  },
);
