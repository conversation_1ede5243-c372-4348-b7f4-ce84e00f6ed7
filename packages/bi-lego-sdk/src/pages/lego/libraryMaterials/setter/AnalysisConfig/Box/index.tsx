// @ts-nocheck
import { Modal, Popover } from '@blmcp/ui';
import { useDrag } from 'react-dnd';
import { useEffect, useRef, useState } from 'react';
import { deleteVirtualField } from '@/pages/lego/api';
import { ReactComponent as DragIcon } from '@/assets/lego/drag-icon.svg';
import { ReactComponent as DeleIcon } from '@/assets/lego/dele.svg';
import { ReactComponent as EditIcon } from '@/assets/lego/edit.svg';
import { generateRandomNumber } from '@/pages/lego/utils';
import type { DragSourceMonitor } from 'react-dnd';
import type { FC } from 'react';
import styles from '../index.module.less';

import { ItemTypes } from '../ItemTypes';
import { AddFun, FieldType, SetterData } from '../types';

export interface BoxProps {
  name: string;
  columnId: number;
  field: string;
  fieldDesc: string;
  add: AddFun;
  disabled: boolean;
  type: FieldType;
  dataType?: number; //  字段类型， 2: 日期  1 number:数字  0 string: 字符串
  isAggr: boolean; //  是否聚合过 true，聚合过，false 未聚合
  isEdit: number;
  dataSourceId: string;
  update: (columnId: number) => void;
  deleteCallback: () => void;
  setting: SetterData;
  draggingRef: { current: number };
  innerRef?: { current: HTMLDivElement };
}

interface DropResult {
  allowedDropEffect: string;
  dropEffect: string;
  name: string;
  onlyOne: boolean;
  ignoreSetDefaultComputeType?: boolean;
  dateFormatDisabled?: boolean;
  dropType: string;
  index: number;
}

export const Box: FC<BoxProps> = ({
  name,
  columnId,
  field,
  fieldDesc,
  add,
  disabled,
  isAggr,
  type,
  dataType,
  isEdit,
  update,
  deleteCallback,
  dataSourceId,
  setting,
  draggingRef,
  innerRef,
}) => {
  const [{ opacity, cursor, isDragging }, drag] = useDrag(
    () => ({
      type: ItemTypes.BOX,
      item: () => {
        draggingRef.current = generateRandomNumber();
        return {
          name,
          columnId,
          key: field,
          fieldType: type,
          dataType,
          isAggr: isAggr,
        };
      },
      canDrag: () => !disabled,
      end(item, monitor) {
        draggingRef.current = 0;
        const dropResult = monitor.getDropResult() as DropResult;
        if (item && dropResult) {
          const { name, fieldType, isAggr, ...rest } = item;
          let computeMode: {
            computeModeId?: number;
            dateFormat?: number;
            numberFormat: number;
          } = {};
          if (!dropResult?.ignoreSetDefaultComputeType && !isAggr) {
            computeMode.computeModeId = fieldType === 'dimensionInfo' ? 1 : 6;
            computeMode.numberFormat = 1;
          }
          // 日期默认值
          if (!dropResult.dateFormatDisabled && item?.dataType === 2) {
            computeMode.dateFormat = 203;
          }
          if (dropResult.dropType === 'sort') {
            // console.log('排序插入', dropResult.index);

            add({
              ...rest,
              fieldType,
              title: name,
              ...computeMode,
              dropType: dropResult.name,
              onlyOne: dropResult.onlyOne,
              dataSourceId,
              isAggr,
              insertIndex: dropResult.index,
            });
          } else {
            add({
              ...rest,
              fieldType,
              title: name,
              ...computeMode,
              dropType: dropResult.name,
              onlyOne: dropResult.onlyOne,
              dataSourceId,
              isAggr,
            });
          }
        }
      },
      collect: (monitor: DragSourceMonitor) => ({
        opacity: monitor.isDragging() ? 0.6 : 1,
        isDragging: monitor.isDragging(),
        cursor: disabled
          ? 'not-allowed'
          : monitor.isDragging()
          ? 'grabbing'
          : 'grab',
      }),
    }),
    [name, columnId, field, setting],
  );
  const [open, setOpen] = useState(false);

  const editRef = useRef<boolean>(false);
  const BoxDOM = (
    <div ref={innerRef} className={styles['dim-item-wrap']}>
      <div
        ref={drag}
        style={{ opacity, cursor }}
        className={
          disabled
            ? styles['dim-item-disable']
            : isEdit
            ? styles['dim-item-edit']
            : styles['dim-item']
        }
      >
        <span className={styles.text} style={{ opacity, cursor }}>
          {name}
        </span>
        <div className={styles['dim-item-icon']} style={{ opacity, cursor }}>
          {isEdit && !disabled && (
            <>
              <div
                className={styles['item-icon']}
                onClick={(event: any) => {
                  event.stopPropagation();
                  update(columnId);
                }}
              >
                <EditIcon
                  style={{ fill: 'rgba(0,0,0,0.4)', fontSize: '16px' }}
                  className="operation-item"
                />
              </div>
              <div
                className={styles['item-icon']}
                onClick={(event: any) => {
                  if (editRef.current) {
                    return;
                  }
                  editRef.current = true;
                  event.stopPropagation();
                  Modal.confirm({
                    title: '您确认要删除该数据吗？',
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                      editRef.current = false;
                      deleteVirtualField({
                        columnId,
                        dataSourceId,
                      }).then(() => {
                        deleteCallback();
                      });
                    },
                    onCancel: () => {
                      editRef.current = false;
                    },
                  });
                }}
              >
                <DeleIcon
                  style={{ fill: 'rgba(0,0,0,0.4)', fontSize: '16px' }}
                  className="operation-item"
                />
              </div>
            </>
          )}
          <DragIcon
            className={disabled ? 'operation-drag-disabled' : 'operation-drag'}
          />
        </div>
      </div>
    </div>
  );

  const content = (
    <div className={styles['desc-popover']}>
      <span className={styles.name}>{name}</span>
      <span className={styles['desc']}>
        {type === FieldType.Index ? '指标说明：' : '维度说明：'}
        {fieldDesc}
      </span>
    </div>
  );

  useEffect(() => {
    if (isDragging) {
      setOpen(false);
    }
  }, [isDragging]);

  return (
    <Popover
      trigger="hover"
      open={open}
      placement="left"
      destroyTooltipOnHide
      title={''}
      content={content}
      onOpenChange={(open: boolean) => {
        setOpen(open);
      }}
    >
      {BoxDOM}
    </Popover>
  );
};
