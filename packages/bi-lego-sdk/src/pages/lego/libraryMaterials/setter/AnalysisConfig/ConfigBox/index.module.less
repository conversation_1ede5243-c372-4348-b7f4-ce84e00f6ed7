.box-wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 160px;

  border-radius: 4px;
  background: #f9f9f9;
  border: 1px dashed #dcdfe6;
  position: relative;
  padding: 8px;
  overflow: auto;
}

.box-wrap-over {
  .box-wrap;
  border: 1px dashed rgba(39, 97, 243, 0.15);
  background: rgba(39, 97, 243, 0.05);
}

.placeholder {
  text-align: center;
  font-size: 13px;
  border-radius: 4px;
  width: 100%;
  line-height: 20px;
  z-index: 100;
}

.placeholderOver {
  .placeholder;
  color: #537aff;
}

.placeholderDisabled {
  .placeholder;
  color: #c0c4cc;
  cursor: not-allowed;
}

.operation {
  cursor: pointer;
}
.operation-menu {
  position: relative;
  height: 16px;
}

.text {
  display: inline-block;
  line-height: 16px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.9);
  text-overflow: ellipsis;
}

.operation-text {
  width: 120px;
  font-size: 13px;
  font-weight: normal;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.9);
  display: inline-flex;
  align-items: center;
  position: relative;
}

.operation-text-disabled {
  .operation-text;
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.26);
}

.operation-icon {
  margin-right: 10px;
  font-weight: normal;
}

.right {
  position: absolute;
  right: 0px;
}

.new-guide-wrap {
  position: absolute;
  width: 32px;
  height: 32px;
  left: -8px;
  top: -6px;
  pointer-events: none;
}

.li-item {
  background: white;
  cursor: move;
  position: relative;

  &:hover {
    background: rgba(37, 52, 79, 3%);
  }

  .column {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px 4px 4px;
    margin: 0px 0px 5px;
    background: #f0faf7;
    border: 1px solid #dcede8;
  }
  &:only-child {
    .column {
      padding: 4px 8px;
    }
  }
}

:global {
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title {
    line-height: 28px;
  }
}
