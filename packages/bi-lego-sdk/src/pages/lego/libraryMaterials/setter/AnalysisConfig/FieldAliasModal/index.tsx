// @ts-nocheck
import { Button, Checkbox, Form, Input, Modal, Select } from '@blmcp/ui';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { dataFormatValue, metricFormatConfig } from '../../../module/utils';
import { Aggregate, AggregateType } from '../constant';
import { checkTextSafeNew } from '@/pages/lego/api';
import { MapTitle, getAggregateText, getQuickText } from '../utils';
import styles from './index.module.less';
const { TextArea } = Input;

const { Option } = Select;
export interface AliasModalRef {
  showModal?: (type: string, index: number, itemConfig: any) => void;
}

interface NumberFormatModalProps {
  setFiledAlias: (type: string, index: number, itemConfig: any) => void;
}

export const FieldAliasModal = forwardRef(
  ({ setFiledAlias }: NumberFormatModalProps, ref) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fieldInfo, setFieldInfo] = useState<{
      type?: string;
      index?: number;
      itemConfig?: string;
    }>({});
    // 敏感词提示
    const [textSafeTips, setTextSafeTips] = useState();
    // 敏感词loading
    const [textSafeLoading, setTextSafeLoading] = useState(false);
    // 计算字段
    const [aggregateText, setAggregateText] = useState<string>('');
    // 快速计算字段
    const [quickText, setQuickText] = useState<string>('');
    const [form] = Form.useForm();
    useImperativeHandle(
      ref,
      () => {
        return {
          showModal(type: string, index: number, itemConfig: any) {
            setFieldInfo({ type, index, itemConfig });
            const aggregateTextStr = getAggregateText(itemConfig);
            const quickTextStr = getQuickText(itemConfig);
            setAggregateText(aggregateTextStr);
            setQuickText(quickTextStr);
            try {
              form.setFieldsValue({
                alias:
                  itemConfig?.alias ||
                  `${aggregateTextStr}${itemConfig?.title}${quickTextStr}`,
                tips: itemConfig?.tips,
              });
            } catch (e) {
              console.log(e, '错误信息');
              console.error('解析数据格式化数据失败', itemConfig);
            }
            setIsModalOpen(true);
          },
        };
      },
      [form],
    );

    const handleOk = () => {
      form.submit();
    };

    const handleCancel = async () => {
      setIsModalOpen(false);
      setTextSafeTips(null);
    };
    const onFinish = (values: any) => {
      try {
        if (Object.values(values).filter(Boolean).length > 0) {
          setTextSafeLoading(true);
          checkTextSafeNew({ contents: Object.values(values).filter(Boolean) })
            .then((res) => {
              if (res.code === 1) {
                if (
                  res?.data?.results?.some(
                    (item) => item?.suggestion === 'block',
                  )
                ) {
                  setTextSafeTips('文本涉及敏感字段');
                } else {
                  setFiledAlias(
                    fieldInfo?.type ?? '',
                    fieldInfo?.index ?? 0,
                    values,
                  );
                  setIsModalOpen(false);
                  setTextSafeTips(null);
                }
              }
            })
            .finally(() => {
              setTextSafeLoading(false);
            });
        } else {
          setFiledAlias(fieldInfo?.type ?? '', fieldInfo?.index ?? 0, {});
          setIsModalOpen(false);
        }
        // 敏感词逻辑
      } catch (error) {
        console.log(error, '错误信息');
        console.error('序列化数据格式化失败', error, values);
      }
    };

    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    return (
      <Modal
        title="设置字段名称"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={560}
        centered
        footer={null}
        destroyOnClose
        closable={true}
        maskClosable={false}
      >
        <div className={styles['data-form']}>
          <div className={styles['origin-filed']}>
            原字段名称: {aggregateText}
            {fieldInfo?.itemConfig?.title}
            {quickText}
          </div>
          <Form
            layout="vertical"
            form={form}
            name="data-format"
            onFinish={onFinish}
            style={{ maxWidth: 600 }}
            colon={false}
            initialValues={{
              alias: `${aggregateText}
            ${fieldInfo?.itemConfig?.title}${quickText}`,
            }}
          >
            <Form.Item
              name="alias"
              label="实际显示名称"
              tooltip={{
                title: '若不填写，保存后将显示默认字段名称',
                placement: 'top',
                arrowPointAtCenter: true,
              }}
              rules={[{ max: 50, message: '最多支持50个字符' }]}
            >
              <Input
                placeholder="请输入实际显示名称"
                count={{
                  show: true,
                  max: 50,
                }}
              />
            </Form.Item>
            <Form.Item
              name="tips"
              label="字段描述"
              rules={[{ max: 200, message: '最多支持200个字符' }]}
            >
              <TextArea
                placeholder="请输入字段描述"
                count={{
                  show: true,
                  max: 200,
                }}
              />
            </Form.Item>
          </Form>
        </div>
        {textSafeTips && (
          <div className={styles['textSafe-tips']}>{textSafeTips}</div>
        )}

        <p className={styles['footer']}>
          <Button
            className={styles['modal-btn']}
            style={{ background: 'white' }}
            onClick={handleCancel}
          >
            取消
          </Button>{' '}
          <Button
            className={styles['modal-btn']}
            type="primary"
            onClick={handleOk}
            loading={textSafeLoading}
          >
            确定
          </Button>
        </p>
      </Modal>
    );
  },
);
