.setter-wrap {
  display: block;
}

.data-source-setter-wrap {
  display: flex;
  width: 100%;
  //padding: 10px;
  //border-width: 1px;
  //border-style: solid;
  //border-color: #ebeef5;
  z-index: 2;

  :global {
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      padding: 5px 5px;
    }
  }

  .dim-item-wrap {
    transition: border-width 100ms ease-out;
    border-width: 0px;
    margin-top: 5px;
    margin-bottom: 5px;
    border-radius: 4px;
    z-index: 3;
    cursor: grab;
  }

  :global {
    .anchor-emphasize {
      border: 0px solid #276bec;
      border-width: 1px;
    }
  }
}

.panel-all {
  flex: 1;
  box-sizing: border-box;
  padding-left: 0px;
  height: calc(100vh - 60px);
  overflow: auto;
  overflow-y: hidden;
  position: relative;

  &:hover {
    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }

    /* 隐藏滚动条轨道 */
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    overflow-y: auto;
  }

  &:first-child {
    border-right: 1px solid #ebeef5;
  }
}

.title-panel {
  height: 38px;
  line-height: 18px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  padding: 10px 0;
}

.title {
  margin-top: 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #303133;
  margin-bottom: 10px;
}

.title-disabled {
  .title;
  color: rgba(0, 0, 0, 0.26);
}

.search-panel {
  margin-top: 10px;
  width: 180px;
}

.data-source-detail {
  display: block;
  margin-top: 10px;

  :global {
    .ant-collapse-content-box {
      padding: 0px 4px !important;
    }
    .ant-collapse-content {
      background: #fff !important;
    }
  }
}

.collapse {
  background: #fff !important;
  font-size: 12px;
  font-weight: normal;
  border-bottom: 0px !important;
  height: calc(100vh - 230px);
  overflow-y: hidden;
  position: relative;

  &:hover {
    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }

    /* 隐藏滚动条轨道 */
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    overflow-y: auto;
  }
}

.panel {
  background: #fff;
  font-size: 12px;
  font-weight: normal;
  border-bottom: 0px !important;
  position: relative;
  padding: 0px 10px;
}

.panel-content {
  font-size: 12px;
  font-weight: normal;
  border-bottom: 0px !important;
  position: relative;
  padding: 0px;

  :global {
    .ant-collapse-header {
      position: sticky !important;
      top: 0px !important;
      background: #fff;
      z-index: 100;
    }

    .ant-collapse-expand-icon {
      height: 20px !important;
      padding-inline-end: 8px !important;
    }
  }
}
.panel-sub-content {
  font-size: 12px;
  font-weight: normal;
  border-bottom: 0px !important;

  padding: 0px;
  :global {
    .ant-collapse-header {
      border-radius: 6px !important;
      // background: #f3f8ff;
      margin: 8px 0px;
    }
  }
}

.config-box-item {
  margin-right: 10px;
}

.panel-title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0em;
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
}

.content {
  padding-top: 0px;
  :global {
    .ant-collapse {
      background-color: #fff;
    }
  }
}

.dim-item {
  width: 170px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0px 10px;
  align-self: stretch;
  border: 1px solid #e7e8eb;
  z-index: 3;
  // margin-top: 5px;
  // margin-bottom: 5px;
  cursor: grab;

  &:hover {
    :global {
      .operation-item {
        display: inline;
      }
    }
  }

  :global {
    .operation-item {
      display: none;
      margin: 0 8px;
      cursor: pointer;
      vertical-align: middle;

      &:first-child {
        margin: 0;
      }
    }

    .operation-drag {
      vertical-align: middle;
    }

    .operation-drag-disabled {
      vertical-align: middle;
      color: #dcdde1;
    }
  }
}
.dim-item-edit {
  .dim-item;
  background-color: #f1f5fd;
}

.dim-item-disable {
  .dim-item;
  cursor: not-allowed;
  background: #f3f3f4;
  color: rgba(0, 0, 0, 0.26);
  border: 1px solid #e7e8eb;
}

.index-item {
  width: 160px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0px 10px;
  align-self: stretch;
  border: 1px solid #ebeef5;
  z-index: 3;
  margin-top: 5px;
  margin-bottom: 5px;
}

.text {
  display: inline-block;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: grab;
}

.empty {
  width: 100%;
  text-align: center;
}

.sub-rate-menu {
  line-height: 25px;
  width: 120px;
  display: 'inline-block';
}

.new-guide-wrap {
  width: calc(100% + 10px);
  height: calc(100% + 20px);
  position: absolute;
  left: -10px;
  top: -10px;
  pointer-events: none;
}

.item-icon {
  display: inline-block;
  line-height: 32px;
  width: 24px;
  cursor: pointer;
  text-align: center;
}
.new-field {
  cursor: pointer;
  color: #366cfe;
  margin-left: 5px;
}
.new-field-disabled {
  color: #366cfe;
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  margin-left: 5px;
}
.new-field-wrap {
  margin-top: 10px;
  margin-left: 2px;
  cursor: pointer;
  color: #366cfe;
  padding-bottom: 10px;
  border-bottom: 1px solid #e7e8eb;
}

.fold-title-wrap {
  display: flex;
  align-items: center;
  max-width: 140px;
}
.fold-title {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);

  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 不换行 */
}
.fold-icon {
  margin-right: 5px;
}
.desc-popover {
  max-width: 321px;
  min-width: 120px;
  max-height: 98px;
  display: flex;
  flex-direction: column;
  margin: 4px;
  overflow: auto;
}
.name {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.9);
}
.desc {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);
}
.warning-icon {
  position: absolute;
  top: 14px;
  left: 17px;
}
.dataset-input-disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.26);
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dataset-input {
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.data-time-tag {
  margin-right: 8px;
  :global {
    .ant-tag {
      border: none;
    }
  }
}

.data-time-tag-offline {
  margin-right: 8px;
  :global {
    .ant-tag {
      border: none;
      color: #000 !important;
    }
  }
}
