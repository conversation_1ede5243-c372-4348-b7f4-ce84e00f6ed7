// @ts-nocheck
import { ReactComponent as ASCIcon } from '@/assets/lego/asc.svg';
import { ReactComponent as DESCIcon } from '@/assets/lego/desc.svg';
import { SortType } from '../../../../components/types';

export const steps = [
  {
    title: '更多操作',
    content: '选择指标的计算方式。',
    placement: 'left',
    target: '#__OperationId',
    disableBeacon: true,
  },
  {
    title: '发布',
    content: '配置完成，点击发布。',
    placement: 'bottom',
    target: '#publishId',
    disableBeacon: true,
    locale: { last: '完成' },
  },
];

export const sortIconMap = {
  [SortType.ASC]: <ASCIcon />,
  [SortType.DESC]: <DESCIcon />,
};
