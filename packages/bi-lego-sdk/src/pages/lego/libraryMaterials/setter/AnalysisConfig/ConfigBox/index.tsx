// @ts-nocheck
// 放置指标维度的盒子容器
import { useDrop } from 'react-dnd';
import { useRef, useState } from 'react';
import { NewGuide } from '@/pages/lego/components/NewGuide';
import { CalcType, FieldType } from '../types';
import { ItemTypes } from '../ItemTypes';
import { SortType } from '../../../../components/types';
import styles from './index.module.less';
import { steps } from './config';
import { ModifyBoxFun, SortClickFun, getMenuConfig } from './getMenuConfig';
import { Box } from './Box';

export interface ColumnsType {
  title: string;
  columnId: number;
  id?: number;
  fieldType: FieldType;
  dataType: number;
  computeModeId: number;
  isAggr: boolean;
  summationComputeModeId: number;
  dateFormat: number;
  advanceComputeModeId: number;
  numberFormat: number;
  sortType?: SortType; //排序
}

export interface ConfigBoxProps {
  allowedDropEffect: string;
  columns?: ColumnsType[];
  disabled?: boolean;
  deleteBox: (index: number) => void;
  setNumberFormat: (index: number) => void;
  setFieldAlias: (index: number) => void;
  modifyBox: ModifyBoxFun;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  onSortClick: SortClickFun;
  resetItemSort: () => void;
  placeholder?: string;
  onlyOne?: boolean;
  combineModeDim: Record<string, CalcType[]>;
  combineModeIndex: Record<string, CalcType[]>;
  rateConfig: any;
  ignoreSetDefaultComputeType?: boolean;
  dateFormatDisabled?: boolean;
  numberFormatConfig?: boolean;
  numberFormatIndexOnly?: boolean;
  disabledSortConfig?: boolean;
  draggingRef?: any;
  fieldAliasConfig?: boolean;
}

export const ConfigBox = ({
  allowedDropEffect,
  columns = [],
  deleteBox,
  modifyBox,
  onSortClick,
  moveCard,
  setNumberFormat,
  setFieldAlias,
  placeholder = '请将字段拖至此处',
  onlyOne,
  combineModeDim = {},
  combineModeIndex = {},
  rateConfig,
  ignoreSetDefaultComputeType,
  dateFormatDisabled,
  numberFormatConfig,
  numberFormatIndexOnly,
  disabledSortConfig,
  disabled,
  draggingRef,
  fieldAliasConfig,
}: ConfigBoxProps) => {
  const [isDrag, setIsDrag] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const [{ isOver }, drop] = useDrop(
    () => ({
      accept: disabled ? Symbol('disabled') : ItemTypes.BOX,
      drop: (item, monitor) => {
        const hoverBoundingRect = ref.current?.getBoundingClientRect();
        const index = Math.floor(
          (monitor.getClientOffset().y - hoverBoundingRect.y - 25) / 37,
        );
        return {
          name: `${allowedDropEffect}`,
          allowedDropEffect,
          onlyOne,
          index: index,
          dropType: 'sort',
          ignoreSetDefaultComputeType,
          dateFormatDisabled,
        };
      },
      collect: (monitor: any) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    }),
    [
      allowedDropEffect,
      disabled,
      onlyOne,
      ignoreSetDefaultComputeType,
      dateFormatDisabled,
    ],
  );

  // 添加总计
  const addSummationCompute = (index: number) => {
    modifyBox(index, 'summationComputeModeId', '31');
  };
  const deleteSummationCompute = (index: number) => {
    modifyBox(index, 'summationComputeModeId', undefined);
  };
  const onClick =
    (index: number) =>
    ({ key }: { key: string }) => {
      if (key === 'delete') {
        deleteBox(index);
      } else if (key === 'total') {
        addSummationCompute(index);
      } else if (key === 'total-cancel') {
        deleteSummationCompute(index);
      } else if (key === 'format') {
        setNumberFormat(index);
      } else if (key === 'fieldAlias') {
        setFieldAlias(index);
      }
    };

  const getRootItems = getMenuConfig(
    combineModeIndex,
    combineModeDim,
    rateConfig,
    dateFormatDisabled,
    numberFormatConfig,
    numberFormatIndexOnly,
    disabledSortConfig,
    fieldAliasConfig,
    modifyBox,
    onSortClick,
  );
  const draggingOver = isOver || draggingRef.current;
  let textTip = onlyOne ? '仅支持拖入 1 个字段' : placeholder;
  if (columns.length === 1 && onlyOne && isOver) {
    // 如果只允许释放一个指标，且当前已经拖入了一个字段，在hover状态下显示替换文案
    textTip = '替换当前字段';
  }
  if (disabled) {
    textTip = '不可拖入字段';
  }
  drop(ref);
  return (
    <>
      <div
        ref={ref}
        id={`configBox_${allowedDropEffect}`}
        className={draggingOver ? styles['box-wrap-over'] : styles['box-wrap']}
      >
        <span>
          {columns?.map((col, index) => {
            return (
              <Box
                key={`${col.columnId}-${col.id}`}
                col={col}
                index={index}
                onClick={onClick}
                getRootItems={getRootItems}
                more={columns.length > 1}
                moveCard={moveCard}
                allowedDropEffect={allowedDropEffect}
                isDrag={isDrag}
                setIsDrag={setIsDrag}
                deleteItem={deleteBox}
                isDraggingField={draggingOver}
                extraParams={{
                  onlyOne,
                  ignoreSetDefaultComputeType,
                  dateFormatDisabled,
                }}
              />
            );
          })}
        </span>
        <div
          className={
            disabled
              ? styles.placeholderDisabled
              : draggingOver
              ? styles.placeholderOver
              : styles.placeholder
          }
          style={{ margin: columns?.length > 0 ? 'auto' : '10px auto' }}
        >
          {textTip}
        </div>
        <NewGuide
          steps={steps}
          localKey="New_Guide_Lego_Edit"
          visible={columns?.length > 0}
        />
      </div>
    </>
  );
};
