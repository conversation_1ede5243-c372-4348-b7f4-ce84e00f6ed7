import AltStringSetter from './LineChart/setter/AltStringSetter';
import LinkComponentSetter from './setter/LinkComponentSetter';
import DatePickerDefaultValue from './DatePickerFilter/setter/DatePickerDefaultValue';
import DatePickerDisabled from './DatePickerFilter/setter/DatePickerDisabled';
import DatePickerRange from './DatePickerFilter/setter/DatePickerRange';
import {
  AnalysisConfig,
  DescriptionConfig,
  OperationColumnConfig,
} from './setter/index';

import TabSetter from './XTab/setter';
import ListFilterDefaultValueSetter from './ListFilter/setter/ListFilterDefaultValueSetter';
import HiddenSetter from './setter/HiddenSetter';
import AliasSetter from './setter/AliasSetter';
import TitleHiddenSetter from './setter/TitleHiddenSetter';

export default function () {
  const { setters } = window.AliLowCodeEngine || {};
  setters?.registerSetter?.('AltStringSetter', AltStringSetter);
  setters?.registerSetter?.('AnalysisConfig', AnalysisConfig);
  setters?.registerSetter?.('DescriptionConfig', DescriptionConfig);
  setters?.registerSetter?.('LinkComponentSetter', LinkComponentSetter);
  setters?.registerSetter?.('DatePickerDefaultValue', DatePickerDefaultValue);
  setters?.registerSetter?.('DatePickerDisabled', DatePickerDisabled);
  setters?.registerSetter?.('DatePickerRange', DatePickerRange);
  setters?.registerSetter?.('TabSetter', TabSetter);
  setters?.registerSetter?.('OperationColumnConfig', OperationColumnConfig);
  setters?.registerSetter?.(
    'ListFilterDefaultValueSetter',
    ListFilterDefaultValueSetter,
  );
  setters?.registerSetter?.('HiddenSetter', HiddenSetter);
  setters?.registerSetter?.('AliasSetter', AliasSetter);
  setters?.registerSetter?.('TitleHiddenSetter', TitleHiddenSetter);
}
