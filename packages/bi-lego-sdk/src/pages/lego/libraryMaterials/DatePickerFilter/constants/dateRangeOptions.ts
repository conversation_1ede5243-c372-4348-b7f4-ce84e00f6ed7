/**
 * 时间选择器可选范围枚举
 */

export interface DateRangeOption {
  value: number;
  label: string;
  description?: string;
}

// 时间范围选项枚举
export const DATE_RANGE_OPTIONS: DateRangeOption[] = [
  {
    value: 93,
    label: '3个月',
    description: '可选择最多3个月的时间跨度',
  },
  {
    value: 186,
    label: '6个月',
    description: '可选择最多6个月的时间跨度',
  },
];

// 默认选项（3个月）
export const DEFAULT_DATE_RANGE = 93;

// 根据值获取选项
export const getDateRangeOption = (
  value: number,
): DateRangeOption | undefined => {
  return DATE_RANGE_OPTIONS.find((option) => option.value === value);
};

// 获取选项标签
export const getDateRangeLabel = (value: number): string => {
  const option = getDateRangeOption(value);
  return option ? option.label : `${value}天`;
};
