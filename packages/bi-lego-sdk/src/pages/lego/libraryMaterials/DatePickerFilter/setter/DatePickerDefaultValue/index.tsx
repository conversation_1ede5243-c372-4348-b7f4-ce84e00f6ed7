import React, { useState, useRef } from 'react';
import dayjs from 'dayjs'; // 全局使用
import { Popover, Select, Row, Col } from '@blmcp/ui';
import { getPresets } from './tools';
// @ts-expect-error
import styles from './index.module.less';
import './index.scss';

interface SetterProps {
  value: any;
  onChange: any;
  type: string;
  field: any;
}

interface defaultValueType {
  category: string;
  dateType?: string;
}

export default ({ onChange, value, type, field, selected }: SetterProps) => {
  const boxDomRef = useRef<HTMLDivElement>(null);
  const isPartialContainerFilter = selected.getPropValue(
    'partialContainerFilterId',
  );
  const categoryArr = [
    {
      value: 'day',
      label: '按天',
      children: [
        { value: 'cur-day', label: '今日' },
        { value: 'yesterday', label: '昨日' },
        { value: 'last-7', label: '近7天' },
        { value: 'last-15', label: '近15天' },
      ],
    },
    {
      value: 'week',
      label: '按周',
      children: [
        { value: 'cur-week', label: '本周' },
        { value: 'last-week', label: '上周' },
      ],
    },
    {
      value: 'month',
      label: '按月',
      children: [
        { value: 'cur-month', label: '本月' },
        { value: 'last-month', label: '上月' },
      ],
    },
    {
      value: 'quarter',
      label: '按季度',
      children: [
        { value: 'cur-quarter', label: '本季' },
        { value: 'last-quarter', label: '上季' },
      ],
    },
  ];
  const categoryOptions =
    type === 'drop-component'
      ? [
          {
            value: 'all',
            label: '全部',
            children: [
              { value: 'all', label: '全部时间' },
              { value: 'last-7', label: '默认7天' },
            ],
          },
          ...categoryArr,
        ]
      : [
          {
            value: 'all',
            label: '全部',
            children: [{ value: 'last-7', label: '默认7天' }],
          },
          ...categoryArr,
        ];
  const categoryOptionsMap: any = {};
  categoryOptions.forEach((item: any) => {
    item.children.forEach((child: any) => {
      categoryOptionsMap[item.value + '-' + child.value] = child;
    });
  });
  const [categoryValue, setCategoryValue] = useState(value?.category || 'all');
  const [dateValue, setDateValue] = useState<any>(
    value?.dateType || (type === 'drop-component' ? 'all' : 'last-7'),
  );
  const [curText, setCurText] = useState<any>(
    categoryOptionsMap?.[value?.category + '-' + value?.dateType]?.label ||
      '设置默认时间',
  );
  const [curDateValueOptions, setCurDateValueOptions] = useState<any>(
    categoryOptions.find((item: any) => item.value === categoryValue)
      ?.children || categoryOptions[0].children,
  );
  const onDefaultValueChange = (value: defaultValueType) => {
    onChange(value);
    const propsField = field.parent;
    const dataSetConfig = propsField.getPropValue('dataSetConfig');
    if (field.node.componentName === 'DatePickerFilter') {
      // 如果是拖拽进入的时间组件，则判断是否设置了维度，如果设置了则触发一次dataSetConfig的change
      if (dataSetConfig?.dimensionInfo?.length) {
        // 因为dataSetConfig有新旧值是否相等的判断，所以这里强制更新下
        propsField.setPropValue('dataSetConfig', {
          ...dataSetConfig,
          dimensionInfo: [
            {
              ...dataSetConfig.dimensionInfo[0],
              // 给一个随机值，目标就是为了触发dataSetConfig的变化
              // title: '默认时间',
              defaultVal: Math.random() + '-' + Date.now(),
            },
          ],
        });
      } else if (propsField.getPropValue('partialContainerFilterId')) {
        propsField.setPropValue('dataSetConfig', {
          dataSourceId: Math.random() + '-' + Date.now(),
        });
      }
    } else if (field.node.componentName === 'DateFilterGlobal') {
      // 如果是默认的时间控件，其本身没有dataSetConfig，需要构建一个
      propsField.setPropValue('dataSetConfig', {
        dataSourceId: Math.random() + '-' + Date.now(),
      });
    }
  };
  // 分类下拉框的变化回调
  const onCategoryChange = (value: any) => {
    setCategoryValue(value);
    const item = categoryOptions.find((item) => item.value === value);
    setDateValue(item?.children[0].value);
    setCurText(item?.children[0].label);
    setCurDateValueOptions(item?.children);
    onDefaultValueChange({
      category: value,
      dateType: item?.children[0].value,
    });
  };
  // 具体时间下拉框变化的回调
  const onDateValueChange = (value: any) => {
    setDateValue(value);
    setCurText(categoryOptionsMap[categoryValue + '-' + value].label);
    onDefaultValueChange({
      category: categoryValue,
      dateType: value,
    });
  };
  const fill = (value: number) => {
    if (value < 10) {
      return '0' + value;
    }
    return value;
  };
  const rangePresets = getPresets();
  // 计算当前的默认时间范围
  // @ts-expect-error
  const targetRange: [Date, Date] =
    rangePresets.find((item) => item.type === dateValue)?.value ||
    rangePresets.find((item) => item.type === 'last-7')?.value;
  const curRange = [dayjs(targetRange[0]), dayjs(targetRange[1])];
  const content = (
    <div style={{ width: isPartialContainerFilter ? '348px' : 'auto' }}>
      <Row gutter={16}>
        <Col span={12}>
          <Select
            value={categoryValue}
            options={categoryOptions}
            onChange={onCategoryChange}
            style={{ width: '100%' }}
          ></Select>
        </Col>
        <Col span={12}>
          <Select
            value={dateValue}
            options={curDateValueOptions}
            onChange={onDateValueChange}
            style={{ width: '100%' }}
          ></Select>
        </Col>
      </Row>
      <div className={styles['date-picker-default-value-callback']}>
        <span>时间范围</span>
        <span className={styles['date-picker-default-value-icon']}></span>
        <span>
          {dateValue === 'all' && type === 'drop-component'
            ? '全部时间'
            : `${curRange[0].year()}-${fill(curRange[0].month() + 1)}-${fill(
                curRange[0].date(),
              )} ~ ${curRange[1].year()}-${fill(
                curRange[1].month() + 1,
              )}-${fill(curRange[1].date())}`}
        </span>
      </div>
    </div>
  );
  return (
    <div
      className={
        styles['setter-date-picker-default-value'] + ` ${styles[type]}`
      }
      ref={boxDomRef}
    >
      <div className={styles['title']}>设置默认时间</div>
      <Popover
        content={content}
        trigger="click"
        arrow={false}
        placement="bottomLeft"
      >
        <div className={styles['default-date-time']}>{curText}</div>
      </Popover>
    </div>
  );
};
