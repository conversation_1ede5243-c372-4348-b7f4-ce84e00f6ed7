.title {
  margin-top: 20px;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  color: #303133;
  margin-bottom: 10px;
}

.default-date-time {
  border: 1px solid rgba(37, 52, 79, 0.12);
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  height: 30px;
  line-height: 30px;
  width: 100%;
  cursor: pointer;
}

.setter-date-picker-default-value {
  z-index: 2;
  width: 100%;
  &.default-component {
    position: relative;
    top: 0;
  }
}

.date-picker-default-value-callback {
  margin: 15px 5px 0;
  min-width: 210px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.9);
}

.date-picker-default-value-icon {
  position: relative;
  top: 3px;
  margin: 0 15px 0 5px;
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url(./image/cal.svg) center center no-repeat;
  background-size: 16px 16px;
}
