import { setVariable } from '@/utils/common';

interface LinkageFun {
  (value: unknown): void;
}

const expCenter = setVariable('__lego__bi_linkageCenter', {});

interface LinkageCenter {
  linkagePool: Record<string, LinkageFun[]>;
  notify: (type: string, params?: unknown) => void; // 类型查询， all: 所有的都去查询，init: 页面初始化的查询，idString,// 通过具体的筛选器去查询
  subscribe: (type: string, query: LinkageFun) => void;
  unsubscribe: (type: string, query: LinkageFun) => void;
  clear: () => void;
}
function getLinkage() {
  const linkageCenter: LinkageCenter = {
    linkagePool: {},
    notify(type: string, params: unknown) {
      const pool = this.linkagePool[type];
      pool?.forEach((fun) => {
        fun(params);
      });
    },
    // 订阅 global: 订阅全局查询按钮
    subscribe(type, linkage) {
      const pool = this.linkagePool[type];
      if (pool) {
        pool.push(linkage);
      } else {
        this.linkagePool[type] = [linkage];
      }

      return () => {
        this.unsubscribe(type, linkage);
      };
    },
    // 解绑 global: 联动事件
    unsubscribe(type, linkage) {
      const pool = this.linkagePool[type];
      if (pool) {
        const index = pool.findIndex((item) => item === linkage);
        if (index !== -1) {
          pool.splice(index, 1);
        }
      }
    },
    clear() {
      this.linkagePool = {};
    },
  };
  return linkageCenter;
}

function exportFunction(reportId: string) {
  if (!reportId) {
    console.error('reportId is required');
  }
  return expCenter[reportId] || (expCenter[reportId] = getLinkage());
}

const funExp = getLinkage();
// eslint-disable-next-line guard-for-in
for (let k in funExp) {
  (expCenter as any)[k] = (funExp as any)[k];
  (exportFunction as any)[k] = (funExp as any)[k];
}

export default exportFunction;
