import numeral from 'numeral';
import { ColumnItem } from '../../components/types';
import { Aggregate, AggregateType } from '../setter/AnalysisConfig/constant';
import { MapTitle } from '../setter/AnalysisConfig/utils';

export const metricFormatConfig: Array<{
  text: string;
  format: (value: number) => { value: number; unit: string };
}> = [
  {
    text: '无',
    format: (value: number) => {
      return { value, unit: '' };
    },
  },
  {
    text: '万',
    format: (value: number) => {
      return { value: value / 10000, unit: '万' };
    },
  },
  {
    text: '百万',
    format: (value: number) => {
      return { value: value / 1000000, unit: '百万' };
    },
  },
  {
    text: '亿',
    format: (value: number) => {
      return { value: value / 100000000, unit: '亿' };
    },
  },
  {
    text: 'K',
    format: (value: number) => {
      return { value: value / 1000, unit: 'K' };
    },
  },
  {
    text: 'M',
    format: (value: number) => {
      return { value: value / 1000000, unit: 'M' };
    },
  },
];

interface DataFormatConfig {
  numStyle: '0' | '1' | '2';
  valueUnit: string;
  percentageAgain: boolean;
  thousandSplit: '1' | '0';
  metricFormat: string;
  scale: string;
}
export const dataFormatValue = (value: number, config: DataFormatConfig) => {
  const {
    percentageAgain,
    thousandSplit,
    metricFormat = 0,
    numStyle = '0',
    scale,
    valueUnit,
  } = config;
  // 类型是默认的
  if (String(numStyle) === '0') {
    if (valueUnit) {
      return `${value} ${valueUnit}`;
    } else {
      return value;
    }
  } else if (String(numStyle) === '1') {
    // 类型是数值的
    const itemConfig =
      metricFormatConfig[metricFormat ?? (0 as number)]?.format(value);
    // 转换精度格式化
    const scaleNum = Number(scale ?? 0);
    let scaleStr = '';

    if (scaleNum > 0) {
      for (let i = 0; i < scaleNum; i++) {
        scaleStr += '0';
      }
      scaleStr = `.${scaleStr}`;
    }
    if (String(thousandSplit) === '1') {
      return `${numeral(
        Math.abs(itemConfig.value) > 0.0001 ? itemConfig?.value : 0,
      ).format(`0,0${scaleStr}`)} ${itemConfig.unit} ${valueUnit}`;
    } else {
      return `${numeral(
        Math.abs(itemConfig?.value) > 0.0001 ? itemConfig?.value : 0,
      ).format(`0${scaleStr}`)} ${itemConfig?.unit} ${valueUnit}`;
    }
  } else if (String(numStyle) === '2') {
    // 类型是百分比的
    // 转换精度格式化
    const scaleNum = Number(scale || 0);
    let scaleStr = '';

    if (scaleNum > 0) {
      for (let i = 0; i < scaleNum; i++) {
        scaleStr += '0';
      }
      scaleStr = `.${scaleStr}`;
    }

    const newValue = percentageAgain ? value / 100 : value;
    if (String(thousandSplit) === '1') {
      return `${numeral(newValue).format(`0,0${scaleStr}%`)} ${valueUnit}`;
    } else {
      return `${numeral(newValue).format(`0${scaleStr}%`)} ${valueUnit}`;
    }
  } else {
    // 返回原始值
    return value;
  }
};

export function toFixed(num: number, digit: number) {
  return (num + Number.EPSILON).toFixed(digit);
}

export const formatValue = (
  value: number,
  {
    advanceComputeModeId,
    valueFormat,
    numberFormat,
    numFormatInfo,
  }: {
    advanceComputeModeId: number;
    valueFormat?: number;
    numberFormat?: number;
    numFormatInfo?: string; // 数据格式化字符串
  },
): string | number => {
  if (isNaN(value)) return value;
  // 有数据格式化配置，优先 numFormatInfo
  let config: Partial<DataFormatConfig> = {};
  try {
    config = JSON.parse(numFormatInfo || '{}');
  } catch (error) {
    config = {};
  }

  const numStyle = String(config.numStyle);
  const numStyles = ['1', '2'];
  const valueUnit = config?.valueUnit || '';

  if (numStyles.includes(numStyle)) {
    return dataFormatValue(value, config as DataFormatConfig);
  } else if (
    String(advanceComputeModeId) === '30' ||
    String(valueFormat) === '2' ||
    String(numberFormat) === '2'
  ) {
    return numeral(value).format('0.[00]%') + valueUnit;
  } else {
    return `${value} ${valueUnit}`;
  }
};

export const getText = (text: string, measureInfo: ColumnItem) => {
  const { computeModeId, advanceComputeModeId, numberFormat, isAggr } =
    measureInfo;
  const AggregateText =
    computeModeId && !isAggr
      ? `(${Aggregate[computeModeId as AggregateType]})`
      : '';
  let quickText = '';
  if (String(advanceComputeModeId) === '30') {
    quickText = '-列占比';
  } else if (MapTitle?.[advanceComputeModeId]) {
    const suffix = String(numberFormat) === '2' ? '率' : '值';
    quickText = `-${MapTitle?.[advanceComputeModeId]}${suffix}`;
  }
  return AggregateText + text + quickText;
};

export const transformDataSet = function (
  dataSource: any,
  callback?: (type: string, value: any, index: number) => object | undefined,
) {
  const { dimensionInfo, measureInfo, values } = dataSource || {};
  return {
    dimensions: [
      ...(dimensionInfo || []).map((v: any, i: number) => ({
        ...v,
        name: v.key + '_D' + i,
        ...((callback && callback('dimensionInfo', v, i)) || {}),
      })),
      ...(measureInfo || []).map((v: any, i: number) => ({
        ...v,
        name: v.key + '_M' + i,
        displayName: getText(v.title, v),
        ...((callback && callback('measureInfo', v, i)) || {}),
      })),
    ],
    source: values || [],
  };
};

export let dataSourceIdPrevious: number | undefined = undefined;
export const getPreviousDataSource = () => dataSourceIdPrevious;
export const setPreviousDataSource = (id: number) =>
  (dataSourceIdPrevious = id);

export const isNull = (value: any) =>
  Object.prototype.toString.call(value) === '[object Null]';
export const isUndefined = (value: any) =>
  Object.prototype.toString.call(value) === '[object Undefined]';
export const isArray = (value: any) =>
  Object.prototype.toString.call(value) === '[object Array]';
// 判断数据是否是一个有效值，不能是null也不能是undefined
export const isValueValid = (value: any) =>
  !isNull(value) && !isUndefined(value);
