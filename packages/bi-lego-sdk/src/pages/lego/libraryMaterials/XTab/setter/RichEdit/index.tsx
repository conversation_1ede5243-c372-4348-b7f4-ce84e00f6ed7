import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import { Modal } from '@blmcp/ui';
import {
  // Boot,
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
  // IButtonMenu,
  DomEditor,
} from '@wangeditor/editor';
import { useSize } from 'ahooks';
import useComponent from '@/pages/lego/hooks/useComponent';
import { setSelectComponentPropsDataById } from '@/pages/lego/utils';
import { checkTextSafe } from '@/pages/lego/api';
import './index.less';
import { message } from 'antd';
const MAX_LENGTH = 5000;

function RichEditor(props: any) {
  // props
  const { open, onOk, onCancel, richTips } = props;
  const [meta] = useComponent(props.componentId);
  const [tooltipTitle, setTooltipTitle] = useState('');
  // 编辑器实例
  const [editor, setEditor] = useState<IDomEditor | null>(null); // TS 语法
  // 工具栏配置
  const containerRef = useRef<HTMLDivElement>(null);
  const containerSize = useSize(containerRef);
  const [editorHeight, setEditorHeight] = useState(80);
  const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: [
      'group-image',
      'todo',
      'blockquote',
      'emotion',
      'group-image',
      'group-video',
      'group-more-style',
      'group-indent',
      'insertTable',
      'codeBlock',
      'fullScreen',
      'undo',
      'redo',
      'lineHeight',
      'fontFamily',
      'divider',
      'group-justify',
      'headerSelect',
      'color',
      'bgColor',
    ],
  }; // TS 语法
  // 敏感字段展示（红色）
  const formatContentHtml = (result: any = {}) => {
    const { suggestion } = result;
    // 插入链接的字符转成#[]
    if (suggestion === 'block') {
      const txt = `文本内容涉及敏感，请修改或者删除`;
      setTooltipTitle(txt);
      // message.error(txt)
    }
  };
  // 失去焦点校验敏感词汇
  const onBlur = async (val: string) => {
    (window.top.proxy || window.top).__lego_checkTextSafe = true;

    let checkRes: any = {};
    if (val !== '') {
      checkRes = await checkTextSafe({
        contents: [val],
      });
    }

    if (val !== '' && checkRes.data?.results[0]?.suggestion === 'block') {
      formatContentHtml(checkRes.data.results[0]);
    } else {
      setTooltipTitle('');
      setSelectComponentPropsDataById(props.componentId, 'text', val);
    }
    (window.top.proxy || window.top).__lego_checkTextSafe = false;
    return checkRes;
  };
  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    onBlur: (editor: IDomEditor) => {
      const linkModal: any = document.querySelector('.w-e-modal');
      if (linkModal && linkModal.style.display !== 'none') return;
      const val = editor.getHtml();
      onBlur(val);
    },
    onCreated: (editor: IDomEditor) => {
      editor?.setHtml(richTips || '');
    },
    maxLength: MAX_LENGTH,
    MENU_CONF: {
      fontSize: {
        fontSizeList: [
          '12px',
          '13px',
          '14px',
          '15px',
          '16px',
          '17px',
          '18px',
          '19px',
        ],
      },
    },
  };

  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor === null) return;
      editor.destroy();
      setEditor(null);
      // parentDoc.addEventListener('click', autoBlur);
    };
  }, [editor]);

  const onChange = (editor: IDomEditor) => {
    // console.log(editor.selection, 'kkkkk');
  };
  const handleOk = async () => {
    const result = await onBlur(editor?.getHtml() || '');
    if (result.data?.results[0]?.suggestion === 'block') {
      message.error('文本涉及敏感字段');
    } else {
      onOk?.(editor?.getHtml());
    }
  };
  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <Modal
      title="备注"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      width={760}
      okText="保存"
      maskClosable={false}
    >
      <div className="lego-rich-tips-wrap">
        <div
          data-edit
          style={{ height: `100%`, width: '100%' }}
          ref={containerRef}
        >
          <Toolbar
            editor={editor}
            defaultConfig={toolbarConfig}
            mode="default"
            style={{
              borderBottom: '1px solid rgba(37, 52, 79, 0.0784)',
              position: 'relative',
            }}
          />
          <Editor
            data-edit
            defaultConfig={editorConfig}
            onCreated={setEditor}
            onChange={(editor: IDomEditor) => onChange(editor)}
            style={{ height: 330, overflowY: 'hidden' }}
            mode="simple"
          />
        </div>
      </div>
      {props.__designMode === 'design' && tooltipTitle && (
        <div className="errorInfo">文本涉及敏感字段</div>
      )}
    </Modal>
  );
}

export default RichEditor;
