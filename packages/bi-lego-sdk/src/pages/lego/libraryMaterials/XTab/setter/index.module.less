.setting-title {
  margin-top: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0em;
  color: rgba(0, 0, 0, 0.9);
}
.add-btn {
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
  width: 114px;
}
.item-input {
  width: 372px !important;
  margin: 8px 0px !important;
}
.icon {
  cursor: pointer;
  margin-right: 8px;
  margin-left: 8px;
}
.icon-drag {
  cursor: move;
  margin-top: 5px;
  margin-right: 8px;
}
.warning-icon {
  position: absolute;
  top: 14px;
  left: 17px;
}
.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.item-button {
  width: 372px;
}
.category-item-label {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-top: 2px;
  color: rgba(0, 0, 0, 0.9);
}
