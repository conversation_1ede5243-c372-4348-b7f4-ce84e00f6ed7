import { Button, Transfer, Modal, message } from '@blmcp/ui';
import { PlusOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { globalCache } from '@/pages/lego/utils/cache';
import useComponent from '@/pages/lego/hooks/useComponent';
// @ts-expect-error
import styles from './style.module.less';

export default function ({ selected, field }) {
  const [meta, setMeta] = useComponent(selected._id);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [targetKeys, setTargetKeys] = useState([]);
  const [options, setOptions] = useState([]);

  // 初始化 将已设置的值保存上
  useEffect(() => {
    const dataSetConfig = field.parent.getPropValue('dataSetConfig');
    if (dataSetConfig?.dimensionInfo?.length) {
      const selects =
        dataSetConfig?.dimensionInfo[0].defaultVal?.stringVal || [];
      setMeta(
        {
          componentTempProps: { selects },
        },
        true,
      );
    }
  }, []);

  const handleOk = function () {
    if (targetKeys.length > 100) {
      message.error('默认值最多支持配置100个');
      return;
    }
    // onChange(targetKeys);
    setIsModalOpen(false);
    const dataSetConfig = field.parent.getPropValue('dataSetConfig');
    if (dataSetConfig?.dimensionInfo?.length) {
      // 因为dataSetConfig有新旧值是否相等的判断，所以这里强制更新下
      field.parent.setPropValue('dataSetConfig', {
        ...dataSetConfig,
        dimensionInfo: [
          {
            ...dataSetConfig.dimensionInfo[0],
            defaultVal: {
              stringVal: targetKeys,
            },
            random: Math.random() + '-' + Date.now(),
          },
        ],
      });
      setMeta(
        {
          componentTempProps: { selects: targetKeys },
        },
        true,
      );
    }
  };
  return (
    <div className={styles.setterBox}>
      <span>默认值配置</span>
      <Button
        disabled={!meta.queryState}
        icon={<PlusOutlined />}
        onClick={async () => {
          const dataSetConfig = field.parent.getPropValue('dataSetConfig');
          if (dataSetConfig?.dimensionInfo?.length) {
            setIsModalOpen(true);
            setTargetKeys(
              dataSetConfig?.dimensionInfo[0].defaultVal?.stringVal || [],
            );

            const options =
              (await globalCache.getDataByIndexId(selected._id, {
                await: true,
                weakMatch: true,
                expire: false,
              })) || [];

            setOptions(options);
          } else {
          }
        }}
      >
        {meta?.componentTempProps?.selects?.length ? (
          <>已设置({meta?.componentTempProps?.selects.length})</>
        ) : (
          '设置默认值'
        )}
      </Button>
      <Modal
        title="设置默认值"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <Transfer
          showSearch
          filterOption={(inputValue: string, option: any) => {
            // console.log('inputValue', inputValue, option);
            return String(option.label).indexOf(inputValue) !== -1;
          }}
          listStyle={{
            width: 335,
            height: 320,
          }}
          onChange={(targetKeys) => {
            setTargetKeys(targetKeys);
          }}
          dataSource={options}
          titles={['待选内容', '已选列表']}
          targetKeys={targetKeys}
          render={(item) => item.label}
          rowKey={(record) => record.value}
          pagination={{
            pageSize: 10,
          }}
        />
      </Modal>
    </div>
  );
}
