import Store, { useStore } from '@/utils/store';

interface ComponentValue {
  // 组件id 服务的
  elementId: number;
  // 组件id 前端的
  id: string;
  // 组件分组
  componentType: number;
  //组件数据类型
  dataType: number;
  // 组件查询函数
  query?: (params?: unknown) => Promise<any>;
  // 请求状态
  queryState: boolean;
  // 组件配置
  dataSetConfig?: {
    [key: string]: {
      limit: [];
      type: string;
      to: string;
    };
  };
  // 组件标题
  title: string;
  // resize 回调
  resize: (() => void)[];
  // 不参与或数据
  notQueryData: boolean;
  // 是否初始查询
  isInitQuery: boolean;
  // loading 态
  loading: boolean;
  // 是否与数据集关联
  isLinkDataSet: boolean;
  // 报错信息
  error?: {
    msg: string;
    code: number;
    data: any;
  };
  // 本次请求是否复用
  repeatedSubmission?: boolean;
  // 组件内部临时变量
  componentTempProps: { [key: string | number | symbol]: any };
  // 渲染状态
  isRender: boolean;
  // 是否隐藏该组件
  hidden: boolean;
  // 是否不渲染该组件
  noRender: boolean;
  // 组件是否在可视区
  inViewDom: boolean;
  // 是否触发查询（查询按钮、重置、拖拽指标查询、初始化）
  isTriggerQuery: boolean;
  // 筛选器组件的关联项
  filterLinkComponents: {
    componentId: string;
  }[];
  dataSourceId: string;
}

const store = new Store<ComponentValue>('useComponent');

export default function useComponent(key: string) {
  return useStore<ComponentValue>(store, key, {} as ComponentValue);
}

export { store, ComponentValue };
