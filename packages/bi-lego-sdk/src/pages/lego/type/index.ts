export enum FieleType {
  '物理字段' = 1,
  '计算字段' = 2,
}

// 组件数据集配置 item
export interface DatasetItem {
  columnId: number; // 字段id
  computeModeId: string; // 计算模式，增长率等
  key: string; // 数据对应的key
  dateFormat?: string; // 日期格式化
  feConfig?: any; // 自定义配置
  title: string; // 名称
  fieldType: string;
  dataType: number; // 2 date: 日期 1 number:数字  0 string: 字符串
  advanceComputeModeId: number; // 平均？百分比
  sortNum?: number;
  type: boolean; // 是否为虚拟字段
  defaultVal: {
    stringVal?: string[] | string[];
    dateInterval: string;
    dateUnit: string;
    dateVal: number;
  }; // 默认值，如日期控件的
  isSensitive: 0 | 1;
  tenantFlag: 0 | 1; // 是否为用户自建字段
  isAggr: boolean; // 是否为聚合字段
}

// 组件数据集配置
export interface DataSetConfig {
  dataSourceId: number; // 数据集id
  dimensionInfo?: DatasetItem[]; // 组件维度
  measureInfo?: DatasetItem[]; // 组件指标
  contrasts?: DatasetItem[]; // 组件对比
  secondary?: DatasetItem[]; // 用于次轴、辅助值
  filterInfo?: DatasetItem[]; // 用于筛选器
}

// 物料 props
export interface ComponentProps<T> {
  // 组件数据集配置
  dataSetConfig: DataSetConfig;
  // 组件数据
  dataSource: T;
  // 组件id 服务的
  // elementId: number;
  // 组件id
  componentId: string;
  __id?: string;
  // 组件title
  title: string;
  // 插槽
  children?: JSX.ElementType;
  // 编辑态or渲染态
  __designMode: 'design' | undefined;
  // 请求函数
  query: (params: unknown) => void;
  // resize 函数
  useResize: (fn: () => void) => void;
  updateComponentConfig: (key: string, value: unknown) => void;
  // 高度
  height: number;
  // 宽度
  width: number;
  isEdit?: boolean;
  // 是否在标题上展示箭头（移动端文本组件展示），以便表示该组件可点击
  showTitleArrow?: boolean;
}

// 数据集数据
export interface IndexData<T> {
  elementId: number; // 组件服务ID
  componentType: number; // 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
  dataSourceId: number; // 数据集ID
  dimensionInfo: DatasetItem[]; // 图表下代表x轴，交叉表代表行， 表格没有， 筛选器的行
  measureInfo: DatasetItem[]; // 图表下代表y轴，交叉表代表列， 表格下代表列
  contrasts: DatasetItem[]; //交叉表下代表对比
  values: T;
  isMore?: number; // 下一页是否还有数据   1：有数据  0：无数据
  pageSize?: number; // 分页阈值
  pageNum?: number; // 当前页数
  totalPage?: number; // 总页数
  totalNum?: number; // 总条数
}

// 指标卡数据
export interface IndicatorCardData {
  elementId: number; // 组件服务ID
  componentType: number; // 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
  dataSourceId: number; // 数据集ID
  title: string; // 指标标题
  value: number; // 值
  MoM: string; // 环比
  YoY: string; // 同比
  explain: string; // 文本描述
  chartData: {
    dimensionInfo: DatasetItem[]; // 维度
    measureInfo: DatasetItem[]; // 数值
    values: IndexDataKeyValue;
  };
}

interface KeyValue {
  [key: string]: any;
}
interface TableValue {
  cells: number | string[];
}

// 普通图表数据结构
export type IndexDataKeyValue = KeyValue[];

// 表格数据结构
export type IndexDataTableValue = {
  rows: TableValue[];
  columns: TableValue[];
  values: number | string[][];
};
