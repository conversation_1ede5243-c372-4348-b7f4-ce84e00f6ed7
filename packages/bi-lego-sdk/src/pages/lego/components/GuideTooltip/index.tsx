// @ts-nocheck
import { CloseOutlined } from '@ant-design/icons';
import './index.less';
import { Step } from 'react-joyride';

export const locale = {
  back: '上一步',
  next: '下一步',
  skip: '跳过',
  close: '下一步',
  last: '我知道了',
};

interface OperationProps {
  onClick: (e: Event) => void;
}
interface GuideTooltipProps {
  continuous?: boolean;
  index: number;
  step: Step;
  size: number;
  backProps: OperationProps;
  closeProps: OperationProps;
  primaryProps: OperationProps;
  skipProps: OperationProps;
  isLastStep: boolean;
  tooltipProps: unknown;
}
export const GuideTooltip = (props: GuideTooltipProps) => {
  const {
    continuous,
    index,
    step,
    size,
    backProps,
    closeProps,
    primaryProps,
    skipProps,
    isLastStep,
    tooltipProps,
  } = props;

  return (
    <>
      <div
        style={{
          background: '#2761F3',
          padding: '24px',
          width: '254px',
          height: size > 1 ? '162px' : '142px',
          borderRadius: '4px',
        }}
      >
        <div
          style={{
            justifyContent: 'space-between',
            display: size > 1 ? 'flex' : 'none',
            marginBottom: '8px',
          }}
        >
          <span className="normal-font">
            {index + 1}/{size}
          </span>

          <span
            className="normal-font"
            style={{
              cursor: 'pointer',
            }}
            onClick={(e) => {
              e.stopPropagation();
              skipProps.onClick(e);
            }}
          >
            <CloseOutlined />
          </span>
        </div>
        <div
          style={{
            color: '#FFF',
          }}
        >
          <div className="tooltip-title">{step.title}</div>
          <div className="tooltip-content">{step.content}</div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginTop: '18px',
            justifyContent: 'space-between',
          }}
        >
          <div>
            {step.showSkipButton ? (
              <span
                className="tooltip-skip"
                onClick={(e) => {
                  e.stopPropagation();
                  skipProps.onClick(e);
                }}
              >
                跳过
              </span>
            ) : null}
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {index > 0 && (
              <div
                className="tooltip-pre"
                onClick={(e) => {
                  e.stopPropagation();
                  backProps.onClick(e);
                }}
              >
                上一步
              </div>
            )}
            {!isLastStep && (
              <div
                className="tooltip-next"
                onClick={(e) => {
                  e.stopPropagation();
                  primaryProps.onClick(e);
                }}
              >
                下一步
              </div>
            )}
            {isLastStep && (
              <div
                className="tooltip-next"
                onClick={(e) => {
                  e.stopPropagation();
                  primaryProps.onClick(e);
                }}
              >
                {step?.locale?.last ?? '完成'}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
