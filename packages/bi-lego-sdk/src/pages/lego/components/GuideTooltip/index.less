.tooltip-title {
  font-family: <PERSON>Fang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0em;

  /* 信息/font-white-1 */
  /* 样式描述：强调（Dark） */
  color: rgba(255, 255, 255, 0.98);
}

.tooltip-content {
  margin-top: 8px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0em;

  /* 信息/font-white-1 */
  /* 样式描述：强调（Dark） */
  color: rgba(255, 255, 255, 0.98);
}

.tooltip-pre {
  cursor: pointer;
  width: 71px;
  height: 24px;
  color: #f0f0f0;
  border-radius: 6px;
  font-family: PingFang SC;
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0em;
  // padding: 0px 16px;
  gap: 4px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  /* 信息/font-white-1 */
  /* 样式描述：强调（Dark） */
  border: 1px solid rgba(255, 255, 255, 0.98);
}

.tooltip-skip {
  cursor: pointer;
  margin-right: 30px;
  font-family: PingFang SC;
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0em;

  /* 信息/font-white-1 */
  /* 样式描述：强调（Dark） */
  color: rgba(255, 255, 255, 0.98);
}

.tooltip-next {
  width: 71px;
  height: 24px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  margin-left: 8px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 4px;
  font-family: PingFang SC;
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0em;

  /* 信息/blue6 */
  /* 样式描述：蓝色信息，如一般类信息 */
  color: #2761f3;
  /* whiteColor */
  /* 样式描述：反白信息色 */
  background: #ffffff;
}

.normal-font {
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0em;

  /* 信息/font-white-1 */
  /* 样式描述：强调（Dark） */
  color: rgba(255, 255, 255, 0.98);
}

.react-joyride__overlay {
  z-index: 199 !important;
}

.__floater__open {
  z-index: 200 !important;
}

.__floater__arrow > span > svg > polygon {
  fill: rgb(39, 97, 243);
}
