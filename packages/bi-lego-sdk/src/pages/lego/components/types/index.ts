// 计算关系，TODO: 需要和后端给出
export enum ComputeMode {}
export interface BaseChart {
  width: number;
  height: number;
}

export interface DataItem {
  name: string;
  value: number;
  valueText: string;
}
export interface Dimension {
  name?: string;
  displayName?: string;
}
export type Dimensions = string[] | Dimension[];
export interface DataSource {
  source: Record<string, string | string | number>[];
  dimensions: Dimensions;
}
export interface ColumnItem {
  title: string;
  computeModeId: number; // 计算规则
  advanceComputeModeId: number; //（同比、环比）
  valueFormat: number; // 数值格式化，1是正常显示 2是需要百分比处理
  numberFormat: number; // 数据格式化，按列-百分比 ，同环比的默认展示
  isAggr?: boolean;
  columnId: number;
  feConfig: string; // 前端展示相关配置
  id: number; // 指标的ID
  key: string; // 指标key
  config: string; //  JSON 字符串
}
export interface RowItem {
  title: string;
  id: number; // 维度的ID
  key: string; // 维度key
  columnId: number;
  config: string; //  JSON 字符串
}
export interface ContrastsItem {
  title: string;
  id: number; // 维度ID
  key: string; // 维度key
  columnId: number;
  config: string;
}
export interface ResData<T = Record<string, unknown>[]> {
  dimensionInfo: RowItem[];
  measureInfo: ColumnItem[];
  contrastInfo: ContrastsItem[];
  values: T;
  totalSize?: number;
  pageNo?: number;
  pageSize?: number;
  sort: { sortOrder: 0 | 1; index: number }[];
  value?: Array<any>;
}

export enum SortType {
  'NONE' = '0',
  'ASC' = '1',
  'DESC' = '2',
}
