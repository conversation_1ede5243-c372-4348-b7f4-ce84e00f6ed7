// @ts-nocheck
import BLMExport from './components/BLMExport';
import request from '@/utils/request';

import './index.less';

interface LegoExportProps {
  searchParams: any;
  otherParams: any;
  handleExportBeforeOpen?: any;
  uuid?: string | null;
}

export const LegoExport = ({
  searchParams,
  otherParams,
  handleExportBeforeOpen,
  uuid,
}: LegoExportProps) => {
  return (
    <div className="lego-exp">
      <BLMExport
        request={request}
        title={'数据乐高导出'}
        exportType={9} // ❗️指定为BI数据乐高导出类型
        searchParams={searchParams}
        otherParams={otherParams}
        onBeforeOpen={handleExportBeforeOpen}
        uuid={uuid}
      />
    </div>
  );
};
