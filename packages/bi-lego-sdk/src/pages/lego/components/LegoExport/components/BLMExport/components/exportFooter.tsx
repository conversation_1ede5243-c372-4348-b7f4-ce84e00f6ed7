import React, { useState, useRef } from 'react';
import { isFunction } from 'lodash-es';
import { useDebounceFn } from 'ahooks';
import { Button, message } from '@blmcp/ui';
import { debounceTime } from '../utils';
import Agreement from '../../BLMAgreement';
import {
  AgreementInfo,
  IAgreementRef,
  IcustomAgreement,
} from '../../BLMAgreement/data';

interface IExportFooterProps {
  request: any;
  // 导出页面标识
  pageFlag: string;
  // 页面资源标识
  resourceKey?: string;
  // 自定义导出协议（供乐高泛化导出使用，无协议版本号概念）
  customAgreement?: IcustomAgreement;
  // 导出钩子
  onExport?: (e: any, data: AgreementInfo) => void;
}

const ExportFooter: React.FC<IExportFooterProps> = (props) => {
  const [checkedAgree, setCheckedAgree] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const _ref = useRef<IAgreementRef>(null);

  const handleCheckedChange = (e: any) => {
    setCheckedAgree(e.target.checked);
  };

  const handleExport = (e: React.MouseEvent) => {
    // 若 React 版本 <= 16，需持久化事件
    // e.persist();
    const _agrInfo = _ref.current?.info || {};
    if (!checkedAgree) {
      message.error(
        `需要“勾选”${_agrInfo?.agreementName || '个人信息处理规范'}才可导出`,
      );
    } else if (isFunction(props.onExport)) {
      props.onExport(e, _agrInfo);
    }
    setExportLoading(false);
  };
  const { run: debouncedExport } = useDebounceFn(
    (e) => {
      setExportLoading(true);
      handleExport(e);
    },
    { wait: debounceTime + 100, leading: true, trailing: false }, // 防抖时间
  );
  return (
    <div className="export-footer">
      <Agreement
        ref={_ref}
        request={props.request}
        checked={checkedAgree}
        onChange={(e: any) => handleCheckedChange(e)}
        pageFlag={props.pageFlag}
        resourceKey={props.resourceKey}
        customAgreement={props.customAgreement}
      />
      <Button
        className="export-footer-button"
        type="primary"
        loading={exportLoading}
        onClick={(e: any) => debouncedExport(e)}
      >
        导出
      </Button>
    </div>
  );
};

export default ExportFooter;
