import { message } from '@blmcp/ui';
import { isFunction, map } from 'lodash-es';
import exportImg from '../img/export.png';
import { ExportDataBOS, ExportContentData } from '../types';
import { standardExportBOS, standardExportBiLego } from '../api';
import { AgreementInfo } from '../../BLMAgreement/data';

/** 导出工具类 */
interface UtilsInterface {
  // 组织导出数据
  organizeParams: (agreement: AgreementInfo) => ExportDataBOS;
  // 调用BOS标准导出接口
  onExport?: (params: ExportDataBOS) => void;
  // 导出方法
  export?: () => void;
  // 其他属性
  [T: string]: any;
}

/**
 * BOS导出工具类（标准导出）
 */
export class Export4BOS implements UtilsInterface {
  // request
  request: any;

  // 用户协议数据
  agreement: AgreementInfo;

  // 导出主体数据
  contentParams: ExportContentData;

  // 其他参数
  otherParams: { [T: string]: any };

  // 导出回调
  exportCallback: (res: any) => void;

  constructor(
    request: any,
    agreement: AgreementInfo,
    contentParams: ExportContentData,
    otherParams: { [T: string]: any } = {},
    exportCallback: (res: any) => void,
  ) {
    this.request = request;
    this.agreement = agreement;
    this.contentParams = contentParams;
    this.otherParams = otherParams;
    this.exportCallback = exportCallback;
  }

  /** 组织导出数据 */
  organizeParams = () => {
    const params: ExportDataBOS = {
      ...this.contentParams,
      ...this.otherParams,
    };
    params.agreementFlag = Boolean(this.agreement.agreementId);
    if (this.agreement.agreementId) {
      params.agreementId = this.agreement.agreementId;
      params.agreementVersion = this.agreement.agreementVersion;
    }
    params.desensExportFlag = params.isDesensExport;
    console.log('🦌🦌🦌 BOS 导出数据 ---》', params);
    return params;
  };

  /** 导出方法 */
  onExport = (params: ExportDataBOS) => {
    if (!params) {
      return;
    }
    const _flag = isFunction(this.exportCallback);
    let taskId = ''; // 审批流任务信息
    standardExportBOS(this.request, params)
      .then((res: any) => {
        // code = 1：导出成功
        if (res && res.code === 1) {
          if (res.data && res.data !== false) {
            taskId = res.data; // 存在审批流会返回审批流的信息
          }
        } else {
          message.error(`${res.tips}`);
        }
      })
      .catch((err: any) => {
        console.error(err);
      })
      .finally(() => {
        if (_flag) {
          this.exportCallback(taskId);
        }
      });
  };

  export = () => {
    const params = this.organizeParams();
    this.onExport(params);
  };
}

/**
 * 业务导出工具类
 */
export class Export4Business implements UtilsInterface {
  // request
  request: any;

  // 用户协议数据
  agreement: AgreementInfo;

  // 导出主体数据
  contentParams: ExportContentData;

  // 其他参数
  otherParams: { [T: string]: any };

  constructor(
    request: any,
    agreement: AgreementInfo,
    contentParams: ExportContentData,
    otherParams: { [T: string]: any } = {},
  ) {
    this.request = request;
    this.agreement = agreement;
    this.contentParams = contentParams;
    this.otherParams = otherParams;
  }

  /** 组织导出数据 */
  organizeParams = () => {
    // 业务导出提供给业务侧的数据按照BOS标准封装
    const params: ExportDataBOS = {
      ...this.contentParams,
      ...this.otherParams,
    };
    params.agreementFlag = Boolean(this.agreement.agreementId);
    if (this.agreement.agreementId) {
      params.agreementId = this.agreement.agreementId;
      params.agreementVersion = this.agreement.agreementVersion;
    }
    params.desensExportFlag = params.isDesensExport;
    console.log('🦌🦌🦌 业务导出数据 ---》', params);
    return params;
  };
}

/** 导出动画 & 导出按钮防抖时间 */

export const debounceTime = 1000;

/** 导出动画 */
export const ballAnimation = (
  startPointLeft: number,
  startPointTop: number,
) => {
  // 计算动画结束坐标
  let [endPointLeft, endPointTop] = [0, 0];
  let findFlag = false;
  const span = document.querySelectorAll('.nav-right .nav-bar-item span');
  map(span, (item) => {
    if (item.innerHTML === '导出记录') {
      findFlag = true;
      const { left, top, width, height } = item.getBoundingClientRect();
      endPointLeft = left + width / 2;
      endPointTop = top + height / 2;
    }
  });

  if (!findFlag) {
    endPointLeft = document.body.clientWidth - 250;
    endPointTop = 24;
    // return message.success('导出任务已创建')
  }

  // 小球动画
  const ball = document.createElement('div');
  ball.className = 'export-animation-ball';
  ball.style.backgroundImage = `url(${exportImg})`;
  ball.style.left = `${startPointLeft}px`;
  ball.style.top = `${startPointTop}px`;
  document.body.appendChild(ball);

  requestAnimationFrame(() => {
    const ball: any = document.querySelector('.export-animation-ball');
    if (ball) {
      const scale = 0.7;
      const { width, height } = ball.getBoundingClientRect();
      const _offsetLeft = (width - width * scale) / 2;
      const _offsetTop = height * scale;
      ball.style.left = `${endPointLeft - _offsetLeft}px`;
      ball.style.top = `${endPointTop - _offsetTop}px`;
      ball.style.transform = `scale(${scale})`;
      ball.style.opacity = `0`;
    }
    setTimeout(() => {
      ball.remove();
    }, debounceTime);
  });
};

/** 打开导出记录抽屉 */
export const triggerExportCenter = () => {
  const span = document.querySelectorAll('.nav-right .nav-bar-item span');
  map(span, (item) => {
    if (item.innerHTML === '导出记录') {
      if (item.parentElement) {
        item.parentElement.dispatchEvent(new Event('click'));
      }
    }
  });
};

/** 获取品牌id */
export const getBrandId = () => {
  const brandId =
    window.sessionStorage.getItem('SESSION_BRANDID') ||
    window.localStorage.getItem('SESSION_BRANDID') ||
    window.localStorage.getItem('brandId');
  return brandId;
};

/**
 * Bi乐高导出工具类（乐高导出，业务导出）
 */
export class Export4BiLego implements UtilsInterface {
  // request
  request: any;

  // 用户协议数据
  agreement: AgreementInfo;

  // 导出参数
  contentParams: { [T: string]: any };

  // 其他参数
  otherParams: { [T: string]: any };

  // 导出回调
  exportCallback: (res: any) => void;

  constructor(
    request: any,
    agreement: AgreementInfo,
    contentParams: ExportContentData,
    otherParams: { [T: string]: any } = {},
    exportCallback: (res: any) => void,
  ) {
    this.request = request;
    this.agreement = agreement;
    this.contentParams = contentParams;
    this.otherParams = otherParams;
    this.exportCallback = exportCallback;
  }

  /** 组织导出数据 */
  organizeParams = () => {
    const params: ExportDataBOS = {
      ...this.contentParams,
      ...this.otherParams,
    };
    params.agreementFlag = Boolean(this.agreement.agreementId);
    if (this.agreement.agreementId) {
      params.agreementId = this.agreement.agreementId;
      params.agreementVersion = this.agreement.agreementVersion;
    }
    params.desensExportFlag = params.isDesensExport;
    console.log('🦌🦌🦌 BOS 导出数据 ---》', params);
    return params;
  };

  /** 导出方法 */
  onExport = (params: ExportDataBOS) => {
    if (!params) {
      return;
    }
    const _flag = isFunction(this.exportCallback);
    let taskId = ''; // 审批流任务信息
    standardExportBiLego(this.request, params)
      .then((res: any) => {
        // code = 1：导出成功
        if (res && res.code === 1) {
          if (res.data && res.data !== false) {
            taskId = res.data; // 存在审批流会返回审批流的信息
          }
        } else {
          message.error(`${res.tips}`);
        }
      })
      .catch((err: any) => {
        console.error(err);
      })
      .finally(() => {
        if (_flag) {
          this.exportCallback(taskId);
        }
      });
  };

  export = () => {
    const params = this.organizeParams();
    this.onExport(params);
  };
}
