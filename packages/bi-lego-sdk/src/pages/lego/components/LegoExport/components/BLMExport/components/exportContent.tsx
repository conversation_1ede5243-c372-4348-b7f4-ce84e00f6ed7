/* eslint-disable max-lines */
import React, { useEffect, useState, useImperativeHandle } from 'react';
import { find, get, isEmpty, isFunction, map } from 'lodash-es';
import {
  Checkbox,
  Input,
  Alert,
  message,
  Segmented,
  Tooltip,
  Card,
  Tag,
} from '@blmcp/ui';
import {
  ExportTypeEnum,
  ExportSearchParams,
  DesensColumnsItem,
  ExportContentData,
  ExportContentParams,
  ExportContentParamsItem,
} from '../types';
import {
  getExactSearchFieldsBOS,
  getTemplateDataBOS,
  getTemplateDataBI,
} from '../api';

import ExportDescription from './ExportDescription';
import request from '@/utils/request';

declare const window: any;
interface IExportContentProps {
  // 外层导出弹框是否打开
  open: boolean;
  // request
  request: any;
  // 导出标识
  pageFlag: string;
  // 导出类型：1业务导出 2标准导出 3准实时 9bi 乐高导出
  exportType?: ExportTypeEnum;
  // 导出模版版本，默认V1
  exportVersion?: number;
  // 查询参数，组件用于回显
  searchParams?: ExportSearchParams[];
  // 框搜搜索字段，与框搜标识必须配套出现
  exactSearchKey?: string;
  // 框搜标识，用于前端转换命中字段并展示
  exactSearchCode?: string;
  otherParams?: any;
  extraData?: any; // 组+品放量字段
  // 其他参数
  [key: string]: any;
}

/** 暴露ref句柄 */
export interface IExportContentRef {
  // hubble 模版完整数据
  templateData: { [T: string]: any };
  // 获取导出主体参数
  getExportContentParams: () => ExportContentData;
  // 获取框搜参数
  getExactSearchParam: () => ExportSearchParams | undefined;
  // 校验：明文导出下是否勾选明文字段，未勾选不可导出
  checkIndesensSwitch: () => boolean;
}

const ExportContent = React.forwardRef<IExportContentRef, IExportContentProps>(
  (props, ref) => {
    const switchOptions = [
      { label: '个人信息脱敏导出', value: false },
      { label: '个人信息明文导出', value: true },
    ];
    // 备注最大长度
    const _remarkMaxLength = 20;
    // hubble 模版数据
    const [templateData, setTemplateData] = useState<{ [T: string]: any }>({});
    // 业务模版数据（templateMap处理后），key为业务字段，value为模版内匹配到业务字段为该字段的所有模版对象，可能存在多个
    const [bizTemplateMap, setBizTemplateMap] = useState<{
      [T: string]: { [T: string]: any }[];
    }>({});
    // 查询参数
    const [_searchParams, setSearchParams] = useState<ExportSearchParams[]>([]);
    // 备注信息
    const [exportRemark, setExportRemark] = useState<string>('');
    // 是否明文导出
    const [isIndesens, setIsIndesens] = useState<boolean>(false);
    // 明文字段
    const [desensColumns, setDesensColumns] = useState<DesensColumnsItem[]>([]);
    // 导出字段
    const [exportColumns, setExportColumns] = useState<string[]>([]);
    // 组+品放量 对参数进行处理
    const filterOriAndBtParams = (originData: any = []) => {
      const { del = [] } = props.extraData || {};
      let filterData = [];
      // 命中时extra-data 内 新增字段 不处理 （searchData内要有）
      // 处理删除字段
      console.log(props.extraData, ' props.extraData----111', originData);
      filterData = originData?.filter((item: any) => {
        if (Array.isArray(item?.bizColumn)) {
          return !del?.find(
            (it: any) => JSON.stringify(it) === JSON.stringify(item?.bizColumn),
          );
        } else {
          return !del.includes(item?.bizColumn);
        }
      });
      return filterData;
    };
    // 导出实时、离线状态
    const [dataUpdateType, setDataUpdateType] = useState<number>(0);
    // 图表导出数据
    const [maxAggrDownNum, setMaxAggrDownNum] = useState<number>(1500);
    // 明细表导出数据
    const [maxDetailDownNum, setMaxDetailDownNum] = useState<number>(5000);
    /** 处理模版数据 */
    const handleBizTemplateMap = (bizTemplateMap: { [T: string]: any }) => {
      const _after = {} as { [T: string]: any };
      if (!bizTemplateMap) {
        return _after;
      }
      map(bizTemplateMap, (item: any) => {
        if (item.bizColumnName) {
          _after[item.bizColumnName] = _after[item.bizColumnName] || [];
          _after[item.bizColumnName].push(item);
        }
      });
      return _after;
    };

    /** 查找某个查询条件对应的模版数据 */
    const _findBizColumnTemplateData = (
      item: ExportSearchParams,
      bizTemplateMap: { [T: string]: any },
    ): { [T: string]: any } => {
      let result = {};
      console.log(item, ' props.extraData----66666', bizTemplateMap);

      if (!isEmpty(item)) {
        const bizTemplate =
          bizTemplateMap[
            Array.isArray(item.bizColumn)
              ? item.bizColumn.join(',')
              : item.bizColumn
          ];
        console.log(bizTemplate, ' props.extraData----77777', bizTemplateMap);

        if (bizTemplate && bizTemplate.length >= 1) {
          if (bizTemplate.length === 1) {
            // 有唯一映射的数据库字段
            result = bizTemplate[0];
          } else if (!isEmpty(item.multFieldStr)) {
            // 多映射字段：配置了 multFieldStr，则根据该字段匹配对应的数据库字段
            map(bizTemplate, (i: { [T: string]: any }) => {
              if (i.multFieldStr === item.multFieldStr) {
                result = i;
              }
            });
            if (isEmpty(result)) {
              console.error(
                `【导出ERROR】未找到与 multFieldStr 匹配的模版对象，查询字段：${item.bizColumn}`,
              );
            }
          } else if (!isEmpty(item.dbColumn)) {
            // 多映射字段：配置了数据字段，理论上只有业务导出会有这种情况
            map(bizTemplate, (i: { [T: string]: any }) => {
              if (i.columnName === item.dbColumn) {
                result = i;
              }
            });
            if (isEmpty(result)) {
              console.error(
                `【导出ERROR】未找到与 dbColumn 匹配的模版对象，查询字段：${item.bizColumn}`,
              );
            }
          } else {
            console.error(
              `【导出ERROR】多映射字段${item.bizColumn}未配置表字段匹配规则`,
            );
          }
        } else {
          console.error(
            `【导出ERROR】未找到业务字段${item.bizColumn}匹配的模版对象`,
          );
        }
      }
      return result;
    };

    /** 查找搜索条件参数中的框搜配置对象 */
    const _getExactSearchParam = () => {
      return find(props.searchParams, ['bizColumn', props.exactSearchKey]);
    };

    /** 处理查询条件，补充回显label */
    const updateSearchParams = (
      bizTemplateMap: { [T: string]: any },
      exactSearchRes: string[] = [],
    ) => {
      const _searchParams = [] as ExportSearchParams[];
      if (props.searchParams && bizTemplateMap) {
        const _params = [...props.searchParams];
        console.log(_params, 'props.extraData----3333', exactSearchRes);
        // 配置框搜规则及字段，且命中框搜规则，转换框搜查询条件字段；暂时不删除框搜原本的查询参数，因为未命中模版字段会被过滤所以不会有影响
        if (
          props.exactSearchKey &&
          props.exactSearchCode &&
          exactSearchRes.length > 0
        ) {
          const _v = _getExactSearchParam();
          if (/** 有框搜参数 */ !isEmpty(_v)) {
            map(exactSearchRes, (key: string) => {
              _params.push({
                bizColumn: key,
                displayValue: get(_v, 'displayValue'),
                value: get(_v, 'value'),
              });
            });
          }
        }

        // 依此匹配模版对象里面相应字段的中文名
        map(_params, (item: ExportSearchParams) => {
          const _biz = _findBizColumnTemplateData(item, bizTemplateMap);
          console.log(_params, 'props.extraData----4444', item, '_biz', _biz);

          if (!isEmpty(_biz)) {
            _searchParams.push({ ...item, displayLabel: _biz.columnNameZh });
          }
        });
      }
      console.log(
        filterOriAndBtParams(_searchParams),
        '✨导出过滤后字段props.extraData----2222',
        _searchParams,
      );
      setSearchParams(filterOriAndBtParams(_searchParams));
    };

    /** 初始化模版配置信息 */
    const initTemplateData = async () => {
      // 组织模型+ 品类三阶段放量控制
      const { pageFlag, exportVersion } = props;
      getTemplateDataBOS(props.request, {
        pageFlag,
        version: exportVersion,
      })
        .then(async (res: any) => {
          if (res && res.code === 1) {
            // 获取框搜命中字段集合
            const _exactSearchRes: string[] = await getExactSearchResult();

            const { exportCol, desensCol, bizTemplateMap = {} } = res.data;
            if (res.data) {
              setTemplateData(res.data);
            }
            // 导出参数埋点
            try {
              const buryData = (props.searchParams || []).map(
                (it: any) => it.bizColumn,
              );
              window.$BLMLogReportCenter.businessLogBury(
                'publicExport-SearchData-input',
                {
                  searchData: buryData || [],
                  resourceKey: window.$baseRouter.cuRoute.name,
                  exportType: props.exportType,
                  pageFlag: props.pageFlag,
                  publicExportVersion: props.exportVersion,
                  extraData: props.extraData,
                },
              );
            } catch (error) {
              console.log(error);
            }

            // 处理业务模版数据
            const _bizTemplateMap = handleBizTemplateMap(bizTemplateMap);
            console.info('🍀 导出模版处理后：', _bizTemplateMap);
            setBizTemplateMap(_bizTemplateMap);

            // 查询参数处理-名称回显
            updateSearchParams(_bizTemplateMap, _exactSearchRes);

            // 明文字段处理
            if (desensCol) {
              const _arr = [] as DesensColumnsItem[];
              desensCol.split(',').forEach((item: string) => {
                const _config = get(bizTemplateMap, item);
                if (_config) {
                  _arr.push({
                    fieldName: item,
                    fieldTitle: _config.columnNameZh,
                    selected: 0,
                  });
                }
              });
              setDesensColumns(_arr);
            }

            // 导出字段处理
            const _exportColumns: string[] = [];
            if (exportCol) {
              JSON.parse(exportCol).forEach((item: string[]) => {
                if (item[0]) {
                  _exportColumns.push(item[0]);
                }
              });
            }
            setExportColumns(_exportColumns);
          } else {
            message.error(res.msg);
          }
        })
        .catch((err: any) => {
          console.error('获取 模版', err);
        });
    };

    /** 初始化BI数据乐高专用模版配置信息 */
    const initBiLegoTemplateData = () => {
      getTemplateDataBI(request, props.otherParams)
        .then(async (res: any) => {
          // 真实代码， 后续需放回promise
          if (res && res.code === 1) {
            const {
              exportCol,
              desensCol,
              bizTemplateMap = {},
              dataUpdateType,
              maxAggrDownNum,
              maxDetailDownNum,
            } = res.data;
            if (res.data) {
              setTemplateData(res.data);
            }
            // 配置导出类型
            setDataUpdateType(dataUpdateType);

            // 处理业务模版数据
            const _bizTemplateMap = handleBizTemplateMap(bizTemplateMap);
            console.info('🍀 导出模版处理后：', _bizTemplateMap);
            setBizTemplateMap(_bizTemplateMap);

            // 查询参数处理-名称回显
            updateSearchParams(_bizTemplateMap);

            // 明文字段处理
            if (desensCol) {
              const _arr = [] as DesensColumnsItem[];
              desensCol.split(',').forEach((item: string) => {
                console.log(item, 999911, bizTemplateMap);
                const _config = get(bizTemplateMap, item);
                if (_config) {
                  _arr.push({
                    fieldName: item,
                    fieldTitle: _config.columnNameZh,
                    selected: 0,
                  });
                }
              });
              setDesensColumns(_arr);
            }
            // 导出字段处理
            const _exportColumns: string[] = [];
            if (exportCol) {
              JSON.parse(exportCol).forEach((item: string[]) => {
                if (item[0]) {
                  _exportColumns.push(item[0]);
                }
              });
            }
            if (maxAggrDownNum) {
              setMaxAggrDownNum(maxAggrDownNum);
            }
            if (maxDetailDownNum) {
              setMaxDetailDownNum(maxDetailDownNum);
            }
            setExportColumns(_exportColumns);
          } else {
            message.error(res.msg);
          }
        })
        .catch((err: any) => {
          console.error(err);
        });
    };

    /** 框搜规则命中字段转换 */
    const getExactSearchResult = async () => {
      if (!props.exactSearchKey && props.exactSearchCode) {
        return [];
      }
      const _v = _getExactSearchParam();
      if (/** 无框搜参数 */ isEmpty(_v)) {
        return [];
      }
      const content = get(_v, 'value');
      if (isEmpty(content)) {
        return [];
      }
      console.log(
        props.exactSearchKey,
        '---',
        props.exactSearchCode,
        '---',
        _getExactSearchParam(),
        '---',
        content,
        'props.extraData----8888',
      );
      return new Promise<string[]>((resolve /* , reject */) => {
        getExactSearchFieldsBOS(props.request, {
          sceneCode: props.exactSearchCode,
          searchContent: get(_v, 'value'),
        })
          .then((res: any) => {
            if (res && res.code === 1) {
              resolve(res.data ?? []);
              return;
            }
            message.error(`${res.tips ?? '框搜规则转换失败！'}`);
            resolve([]);
          })
          .catch((err: any) => {
            console.error(`${err ?? '框搜规则转换失败！'}`);
            resolve([]);
          });
      });
    };

    /** 切换脱敏/明文 */
    const handleSwitch = (e: boolean) => {
      setIsIndesens(e);
    };

    /** 明文字段勾选 */
    const handleDesensColumnsCheck = (e: any, index: number) => {
      const _desensColumns = [...desensColumns];
      _desensColumns[index].selected = e.target.checked ? 1 : 0;
      setDesensColumns([..._desensColumns]);
    };

    /** 组织导出主体部分参数 */
    const getExportContentParams = (): ExportContentData => {
      const _params = {} as ExportContentData;
      // 查询条件参数
      const _exportParams = {} as ExportContentParams;
      if (_searchParams && bizTemplateMap) {
        map(_searchParams, (item: ExportSearchParams) => {
          const _biz = _findBizColumnTemplateData(item, bizTemplateMap);
          if (!isEmpty(_biz)) {
            const _obj: ExportContentParamsItem = {
              matchValue: isFunction(item.displayValue)
                ? item.displayValue()
                : item.displayValue,
              value: item.value,
            };
            _exportParams[_biz.columnName] = _obj;
          }
        });
      }

      // 组织模型+ 品类三阶段放量控制
      const { pageFlag, exportVersion } = props;
      _params.version = exportVersion;
      _params.exportRemark = exportRemark.trim();

      if (props.exportType !== ExportTypeEnum.BILEGO) {
        _params.exportParams = _exportParams;
        _params.pageFlag = pageFlag;
      }
      _params.isDesensExport = !isIndesens;
      if (isIndesens) {
        _params.desensitizeFieldInfos = JSON.stringify(desensColumns);
      } // 明文导出字段
      try {
        window.$BLMLogReportCenter.businessLogBury(
          'publicExport-SearchData-output',
          {
            params: _params,
            // isOri3AndBt2,
          },
        );
      } catch (error) {
        console.log(error);
      }
      return _params;
    };

    /** 校验：明文导出下是否勾选明文字段，未勾选不可导出 */
    const _checkIndesensSwitch = (): boolean => {
      if (desensColumns?.length > 0 && isIndesens) {
        const _desensColumns = [...desensColumns];
        const hasCheck = find(_desensColumns, ['selected', 1]);
        return !isEmpty(hasCheck);
      } else {
        return true;
      }
    };

    const _getMsgFromExportType = () => {
      if (dataUpdateType === 1) {
        return '您当前导出的是T-1更新数据，不包含导出当天的数据，具体查看导出说明；';
      } else if (dataUpdateType === 0) {
        return '您当前导出的是上个整点更新的准实时数据；';
      }
      return '';
    };

    useEffect(() => {
      if (!props.open) {
        return;
      }
      if (props.exportType === ExportTypeEnum.BILEGO) {
        initBiLegoTemplateData();
      } else {
        initTemplateData();
      }
    }, [props.open]);

    useImperativeHandle(
      ref,
      () => {
        const _handler = {} as IExportContentRef;
        _handler.templateData = templateData;
        _handler.getExportContentParams = getExportContentParams;
        _handler.getExactSearchParam = _getExactSearchParam;
        _handler.checkIndesensSwitch = _checkIndesensSwitch;
        return _handler;
      },
      [
        _searchParams,
        bizTemplateMap,
        exportRemark,
        isIndesens,
        desensColumns,
        templateData,
        props.exactSearchKey,
        props.exactSearchCode,
      ],
    );

    return (
      <div className="export-content">
        {props.exportType === ExportTypeEnum.BILEGO &&
          _getMsgFromExportType() && (
            <Alert
              showIcon={true}
              type="warning"
              message={_getMsgFromExportType()}
              style={{ marginBottom: '16px' }}
            />
          )}
        {/* 查询条件卡片 */}
        <Card
          title="查询条件"
          bordered={false}
          style={{ marginBottom: '16px' }}
        >
          <div>
            {_searchParams && _searchParams.length > 0 ? (
              _searchParams.map((item: ExportSearchParams, index) => {
                const labelOrigin = isFunction(item.displayValue)
                  ? item.displayValue()
                  : item.displayValue;

                let label = `${item.displayLabel}：${labelOrigin}`; // 展示值
                let tooltipFlag = false; // 是否缩略Tootip提示

                const tooltip = item.overflowTooltip;
                if (tooltip !== false) {
                  const splitBy = get(tooltip, 'splitBy', '、'); // 默认使用中文顿号作为分隔符
                  const arr = String(labelOrigin).split(splitBy);
                  if (arr?.length > 1) {
                    label = `${item.displayLabel}：${arr[0]} 共${arr.length}个`;
                    tooltipFlag = true;
                  }
                }

                return (
                  <>
                    {!isEmpty(labelOrigin) && (
                      <>
                        {tooltipFlag ? (
                          <Tooltip title={labelOrigin} key={index}>
                            <Tag
                              bordered={false}
                              size={'large'}
                              style={{
                                marginBottom: '8px',
                                marginRight: '8px',
                                whiteSpace: 'normal',
                                height: 'auto',
                              }}
                            >
                              {label}
                            </Tag>
                          </Tooltip>
                        ) : (
                          <Tag
                            bordered={false}
                            size={'large'}
                            style={{
                              marginBottom: '8px',
                              marginRight: '8px',
                              whiteSpace: 'normal',
                              height: 'auto',
                            }}
                            key={index}
                          >
                            {label}
                          </Tag>
                        )}
                      </>
                    )}
                  </>
                );
              })
            ) : (
              <div className="export-search-empty-box">
                请先添加查询条件，否则将导出空白文档
              </div>
            )}
          </div>
        </Card>
        {/* 备注卡片 */}
        <Card title="备注" bordered={false} style={{ marginBottom: '16px' }}>
          <Input
            allowClear
            maxLength={_remarkMaxLength}
            placeholder="请描述要导出的原因"
            onChange={(e: any) => setExportRemark(e.target.value)}
            suffix={
              <>
                <span className="remark-length-span">{`${exportRemark.length}`}</span>
                <span className="remark-max-span">{`/${_remarkMaxLength}`}</span>
              </>
            }
          />
        </Card>

        {desensColumns.length > 0 && (
          <Card
            title="导出类型"
            bordered={false}
            style={{ marginBottom: '16px' }}
          >
            <div className="export-indesens-switch-box">
              <Segmented
                value={isIndesens}
                options={switchOptions}
                onChange={handleSwitch}
              />
            </div>
            {isIndesens && (
              <div className="export-columns-check-box">
                {desensColumns.map(
                  ({ fieldName, fieldTitle, selected }, index: number) => {
                    return (
                      <div
                        key={fieldName}
                        className="export-columns-check-item"
                      >
                        <Checkbox
                          checked={selected === 1}
                          onChange={(e: any) => {
                            handleDesensColumnsCheck(e, index);
                          }}
                        >
                          {fieldTitle}
                        </Checkbox>
                      </div>
                    );
                  },
                )}
              </div>
            )}
          </Card>
        )}

        <Card
          title="导出字段"
          bordered={false}
          style={{ marginBottom: '16px' }}
        >
          <div className="export-columns-span-box">
            {exportColumns.map((item: string, index: number) => (
              <Tag
                key={index}
                size="large"
                bordered={false}
                style={{
                  marginBottom: '8px',
                  marginRight: '8px',
                  whiteSpace: 'normal',
                  height: 'auto',
                }}
              >
                {item}
              </Tag>
            ))}
          </div>
        </Card>

        <ExportDescription
          exportType={props.exportType}
          pageFlag={props?.pageFlag}
          maxAggrDownNum={maxAggrDownNum}
          maxDetailDownNum={maxDetailDownNum}
        />
      </div>
    );
  },
);

export default ExportContent;
