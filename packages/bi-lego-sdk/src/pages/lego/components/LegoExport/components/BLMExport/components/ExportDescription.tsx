import React, { useState, useEffect } from 'react';
import { Card } from '@blmcp/ui';
import { ExportTypeEnum } from '../types';

interface IExportDescriptionProps {
  exportType: ExportTypeEnum;
  pageFlag: string;
  maxAggrDownNum?: number;
  maxDetailDownNum?: number;
}

const ExportDescription: React.FC<IExportDescriptionProps> = ({
  exportType,
  pageFlag,
  maxAggrDownNum,
  maxDetailDownNum,
}) => {
  const formatNumberToWan = (num) => {
    if (typeof num !== 'number') return num.toString();
    // 检查数字是否能被10000整除且至少有4个0
    if (num % 10000 === 0 && num.toString().match(/0{4,}$/)) {
      const wanValue = num / 10000;
      if (wanValue % 1 === 0) {
        return wanValue + '万';
      }
    }
    return num.toString();
  };
  return (
    <Card title="导出说明" bordered={false} style={{ marginBottom: '16px' }}>
      {
        // 乐高数据导出
        exportType === ExportTypeEnum.BILEGO ? (
          <div className="export-description">
            1、图表、交叉表支持导出{maxAggrDownNum}条数据，明细表支持导出
            {formatNumberToWan(maxDetailDownNum || 5000)}
            条数据；
            <br />
            2、数据导出时，图表中【数据格式】【日期类型】的数据转化功能将不会随数据一并导出；
            <br />
            3、实时数据，每小时整点更新数据；T-1离线数据，每日0点更新前一天的数据，因不同业务数据更新时长不一致，建议每日7点之后导出前一天的数据；
            <br />
            4、操作导出后，可在页面右上角的“导出记录”中查看导出结果；
            <br />
            5、数据处理完成后生成的excel文件，将为您保存7天，在此期间您可以将文件下载到电脑本地；若文件超出1G时，下载会失败，可精简查询条件减少单次导出数据量，分批导出再下载；
            <br />
            6、明细表页面使用排序功能且导出数据量较大时，会出现页面查看与导出数据排序不一致的问题，可通过减少数据量导出的方式保障查询与导出的数据排序一致；
          </div>
        ) : null
      }
    </Card>
  );
};

export default ExportDescription;
