// eslint-disable-next-line @typescript-eslint/no-unused-vars
import React, { useRef, useState } from 'react';
import { cloneDeep, isFunction } from 'lodash-es';
import { Drawer, Button, message, BLMIconFont } from '@blmcp/ui';
import ExportContent, { IExportContentRef } from './components/exportContent';
import ExportFooter from './components/exportFooter';
import {
  ExportTypeEnum,
  ExportSearchParams,
  ExportContentData,
  ExportDataBOS,
} from './types';
import {
  Export4BOS,
  Export4Business,
  ballAnimation,
  Export4BiLego,
} from './utils';
import { AgreementInfo, IcustomAgreement } from '../BLMAgreement/data.d';
import './style.less';
import { useLegoReport } from '@/tool/report';
import request from '@/utils/request';

interface ExportProps {
  /**
   * 导出标识
   */
  pageFlag: string;
  /**
   * 页面资源标识
   */
  resourceKey?: string;
  /**
   * 框搜搜索字段，与框搜标识必须配套出现，框搜规则Code，BOS服务提供（该字段与下方字段需要走BOS框搜规范）
   */
  exactSearchKey?: string;
  /**
   * 框搜标识，用于前端转换命中字段并展示
   */
  exactSearchCode?: string;
  /**
   * 导出标题
   */
  title?: string;
  /**
   * 导出按钮文本
   */
  exportBtnText?: string;
  /**
   * 是否展示组件
   */
  visible?: boolean;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 按钮类型，类型同 antd Button type
   */
  type?: string;
  /**
   * 是否无hover态背景颜色
   */
  noHoverBg?: boolean;
  /**
   * 导出类型：1业务导出 2标准导出 3准实时
   * @default 2
   */
  exportType?: ExportTypeEnum;
  /**
   * 导出模版版本，默认V1
   */
  exportVersion?: number;
  /**
   * 查询参数，组件用于回显
   */
  searchParams?: ExportSearchParams[];
  /**
   * 导出接口其他参数
   */
  otherParams?: { [T: string]: string };
  /**
   * 自定义导出协议（供乐高泛化导出使用，无协议版本号概念）
   */
  customAgreement?: IcustomAgreement;
  /**
   * 导出抽屉弹出前钩子，返回false则不弹出导出抽屉
   */
  onBeforeOpen?: () => boolean;
  /**
   * 抽屉内导出按钮点击回调
   */
  onExportBtnClick?: () => void;
  /**
   * 业务导出钩子，只有业务导出场景下才有效
   */
  onBusinessExport?: (
    params: ExportDataBOS,
    exactSearchParam: ExportSearchParams | undefined,
  ) => Promise<string | number>;
  /**
   * 导出调用成功回调（任务不一定执行完成）
   */
  onExportSuccess?: () => void;
  // 其他参数
  [key: string]: any;
}

/**
 * CP4.1 导出组件
 */
const BLMExport = (props: ExportProps) => {
  const { type } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [reportMeta] = useLegoReport(props.uuid);

  // 导出抽屉标识
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const changeVisible = (flag: boolean) => {
    setDrawerOpen(flag);
  };
  // 导出主体部分Ref
  const _contentRef = useRef<IExportContentRef>(null);
  // 导出小球开始坐标
  const _startPositionRef = useRef<number[]>([0, 0]);
  console.log('props.extraData----9999', props?.searchParams);
  /** 外层导出按钮  */
  const handleOpen = async () => {
    if (!request) {
      messageApi.error(`导出组件缺失必传参数‘request‘,请检查并指定`);
    } else if (isFunction(props.onBeforeOpen)) {
      try {
        const _beforeCheck = props.onBeforeOpen();
        if (_beforeCheck === true) {
          changeVisible(true);
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      changeVisible(true);
    }
  };

  /** 任务创建动画 */
  const exportAnimation = () => {
    setDrawerOpen(false);
    const [startPointLeft, startPointTop] = _startPositionRef.current;
    ballAnimation(startPointLeft, startPointTop);
    // NOTE：hook 导出任务调用完成
    if (isFunction(props.onExportSuccess)) {
      props.onExportSuccess();
    }
  };

  /** 导出完成回调 */
  const _exportCallback = (taskId: any) => {
    exportAnimation();
  };

  /** 抽屉内导出按钮 */
  const handleExportBtnClick = async (
    e: any,
    agreement: AgreementInfo = {},
  ) => {
    // 选择明文导出时校验是否勾选了字段
    if (_contentRef?.current) {
      const flag = _contentRef.current.checkIndesensSwitch();
      if (!flag) {
        messageApi.error('明文信息选择请至少选择一项');
        return;
      }
    }

    // NOTE：hook 点击导出按钮回调
    if (isFunction(props.onExportBtnClick)) {
      props.onExportBtnClick();
    }

    // 初始化动画开始坐标
    const { width, height } = e.currentTarget.getBoundingClientRect();
    _startPositionRef.current = [e.clientX - width, e.clientY - height];

    // 导出主体数据
    let _params: ExportContentData = {};
    // 框搜数据
    let _exactSearchParam: any = {};
    if (_contentRef?.current) {
      _params = cloneDeep(_contentRef.current.getExportContentParams());
      _exactSearchParam = {
        ...(_contentRef.current.getExactSearchParam() ?? {}),
      };
    }
    // 标准导出 - 标准导出/准实时导出：调用BOS导出接口
    if (
      props.exportType === ExportTypeEnum.STANDARD ||
      props.exportType === ExportTypeEnum.REALTIME
    ) {
      new Export4BOS(
        request,
        agreement,
        _params,
        props.otherParams,
        _exportCallback,
      ).export();
    }

    // 业务导出：接入BOS，兜底为业务侧接口调用
    if (props.exportType === ExportTypeEnum.BUSINESS) {
      if (!isFunction(props.onBusinessExport)) {
        messageApi.error(
          '导出失败，缺失业务导出实现方法，请添加onBusinessExport',
        );
        return;
      }
      const _data = new Export4Business(
        request,
        agreement,
        _params,
        props.otherParams,
      ).organizeParams();

      await props
        .onBusinessExport(_data, _exactSearchParam)
        .then((res) => {
          exportAnimation();
        })
        .catch((err) => {
          messageApi.error(`导出任务创建失败${err || ''}`);
        });
    }

    // 数据乐高导出：未接BOS，兜底为业务侧接口调用
    if (props.exportType === ExportTypeEnum.BILEGO) {
      new Export4BiLego(
        request,
        agreement,
        _params,
        props.otherParams,
        _exportCallback,
      ).export();
    }
  };

  return (
    <>
      {contextHolder}
      {props.visible ? (
        <div className="export-container">
          <Button
            type={type}
            disabled={props.disabled}
            icon={
              <BLMIconFont
                type="BLM-ic-export-o"
                style={{ width: '16px', height: '16px' }}
              />
            }
            className={props.noHoverBg ? 'export-button-no-hover-bg' : ''}
            onClick={handleOpen}
          >
            {props.exportBtnText}
          </Button>

          {/* 导出抽屉 */}
          <Drawer
            width={560}
            title={props.title}
            destroyOnClose
            open={drawerOpen}
            closeIcon={false}
            placement="right"
            className={'standard-export-drawer'}
            extra={
              <BLMIconFont
                type="BLM-ic-close-o"
                onClick={() => changeVisible(false)}
              />
            }
            footer={
              <ExportFooter
                request={request}
                pageFlag={props.pageFlag}
                onExport={handleExportBtnClick}
                customAgreement={reportMeta.customAgreement}
              />
            }
            onClose={() => changeVisible(false)}
            mode="card"
          >
            <ExportContent ref={_contentRef} open={drawerOpen} {...props} />
          </Drawer>
        </div>
      ) : null}
    </>
  );
};

BLMExport.defaultProps = {
  title: '导出',
  exportType: ExportTypeEnum.STANDARD,
  exportVersion: 1,
  exportBtnText: '导出',
  visible: true,
  disabled: false,
  noHoverBg: false,
};

export default BLMExport;
