@primaryColor: rgba(0, 0, 0, 0.9);
@secondaryColor: rgba(0, 0, 0, 0.6);
@weekColor: rgba(0, 0, 0, 0.3);
@fillColor1: rgba(37, 52, 79, 0.03);
@exceptionBackgroundColor: rgba(248, 144, 3, 0.05);
@exceptionStrokeColor: rgba(248, 144, 3, 0.15);
@transitionTime: 0.6s;
@cubicBezier: 0, 1.23, 0.92, 1.04;

.standard-export-drawer {
  .ant-drawer-header {
    .ant-drawer-extra svg {
      width: 16px;
      height: 16px;
    }
  }

  .ant-drawer-footer {
    .export-footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.export-search-empty-box {
  color: @secondaryColor;
  text-align: center;
}

.export-content {
  user-select: none;
  -webkit-user-select: none;

  .export-columns-check-box {
    .export-columns-check-item {
      display: inline-flex;
      padding: 16px 24px 0 0px;
    }
  }

  .remark-length-span {
    margin-inline-end: 0px !important;
    color: @weekColor;
  }

  .remark-max-span {
    color: @weekColor;
  }

  .export-description {
    font-size: 14px;
    color: @secondaryColor;
    line-height: 22px;
  }
}

.export-audit-modal {
  .export-auditors-span {
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  .export-auditors-box {
    font-size: 14px;
  }
}

.export-animation-ball {
  position: absolute;
  width: 50px;
  height: 50px;
  z-index: 9999;
  border-radius: 20px;
  background-color: #fff;
  background-size: 32px 32px;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0px 5px 10px 0px rgba(42, 48, 72, 0.1);
  transition: left @transitionTime linear,
    top @transitionTime cubic-bezier(@cubicBezier),
    opacity (@transitionTime) step-end, transform @transitionTime linear;
}

.export-button-no-hover-bg {
  &:hover {
    background-color: transparent !important;
  }
}
