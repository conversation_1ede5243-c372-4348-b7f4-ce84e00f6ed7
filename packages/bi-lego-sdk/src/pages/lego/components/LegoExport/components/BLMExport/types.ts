/** 导出场景类型 */
export enum ExportTypeEnum {
  // 业务导出
  BUSINESS = 1,
  // 标准导出
  STANDARD = 2,
  // 准实时
  REALTIME = 3,
  // 数据乐高导出
  BILEGO = 9,
}

/** 查询条件 */
export interface ExportSearchParams {
  // 字段key，同配置表内的 “业务字段映射”
  bizColumn: string | string[];
  /**
   * 数据库key，同配置表内的 “字段名称”；配置该字段的场景：同一前端字段映射多个服务字段
   * 1. 业务导出，直接配置 displayLabel 解决查询条件回显问题
   * 2. 离线导出，配置该字段，否则无法映射当前业务字段对应的数据库字段，会被丢弃处理
   */
  dbColumn?: string;
  // 字段值（eg：时间类型为时间戳；字典/枚举为键值）
  value: any;
  // 字段展示名称（由组件匹配，业务侧不必提供，如果业务侧提供优先取业务侧提供字段）
  displayLabel?: string;
  // 字段展示值
  displayValue: number | string | (() => number) | (() => string);
  // 如果字段是NvN，传这个标识用于区分bi侧的字段
  multFieldStr?: string;
  // 溢出tooltip展示配置
  overflowTooltip?: boolean | OverflowTooltipConfig;
}

/** 导出查询数据字段溢出Tooltip配置 */
export interface OverflowTooltipConfig {
  // 数据分隔符，默认中文顿号
  splitBy?: string;
}

/** 导出主体数据-查询参数 */
export interface ExportContentParams {
  [T: string]: ExportContentParamsItem;
}

export interface ExportContentParamsItem {
  matchValue: number | string | (() => number) | (() => string);
  value: any;
}

/** 明文字段 */
export interface DesensColumnsItem {
  // 字段 filed
  fieldName: string;
  // 字段展示名称
  fieldTitle: string;
  // 是否勾选 1-勾选 0-未勾选
  selected: number;
}

/** 导出主体数据 */
export interface ExportContentData {
  // 导出标识
  pageFlag?: string;
  // 导出模版版本
  version?: number;
  // 导出查询参数
  exportParams?: ExportContentParams;
  // 导出备注信息
  exportRemark?: string;
  // 是否脱敏导出
  isDesensExport?: boolean;
  // 明文字段
  desensitizeFieldInfos?: string;
}

/** 导出数据 - BOS入参 */
export interface ExportDataBOS {
  // 导出标识
  pageFlag?: string;
  // 页面资源标识
  resourceKey?: string;
  // 协议ID
  agreementId?: string | number;
  // 协议版本
  agreementVersion?: string | number;
  // 导出查询参数
  exportParams?: ExportContentParams;
  // 导出备注信息
  exportRemark?: string;
  // 明文字段
  desensitizeFieldInfos?: string;
  // 是否同意协议
  agreementFlag?: boolean;
  // 是否脱敏导出
  desensExportFlag?: boolean;
  // 其他参数
  [T: string]: any;
}
