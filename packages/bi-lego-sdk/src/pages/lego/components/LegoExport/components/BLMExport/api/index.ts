/**
 * BOS 获取导出模板
 */
export function getTemplateDataBOS(request: any, data = {}) {
  return request({
    url: '/admin/v2/ai/common/export/server/getTemplateData',
    method: 'post',
    data,
  });
}

/**
 * BOS 标准导出接口
 */
export function standardExportBOS(request: any, data = {}) {
  return request({
    url: '/admin/v1/common/export/server/standardExport',
    method: 'post',
    data,
  });
}

/**
 * BOS 获取框搜命中字段
 */
export function getExactSearchFieldsBOS(request: any, data = {}) {
  return request({
    url: '/admin/v1/common/search/getRule',
    method: 'post',
    data,
  });
}

// 数据乐高导出获取模版数据 CM模块提供
export function getTemplateDataBI(request: any, data = {}) {
  return request({
    url: '/admin/v1/ai/chartexport/getTemplateData',
    method: 'post',
    data,
  });
}

/**
 * 数据乐高导出接口
 */
export function standardExportBiLego(request: any, data = {}) {
  return request({
    url: '/admin/v1/ai/chartexport/standardExport',
    method: 'post',
    data,
  });
}
