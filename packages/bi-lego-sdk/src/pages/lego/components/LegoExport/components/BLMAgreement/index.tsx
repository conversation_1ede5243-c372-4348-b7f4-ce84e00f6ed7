import React, { useEffect, useState, useImperativeHandle } from 'react';
import { Checkbox, message } from '@blmcp/ui';
import { isFunction } from 'lodash-es';
import { getAgreementInfo } from './api';
import { AgreementInfo, IAgreementRef, IcustomAgreement } from './data.d';

interface IAgreementProps {
  /**
   * 请求方法实例
   */
  request: any;
  /**
   * 勾选协议框
   */
  checked: boolean;
  // 资源标识
  resourceKey?: string;
  // 导出标识
  pageFlag?: string;
  // 自定义导出协议（供乐高泛化导出使用，无协议版本号概念）
  customAgreement?: IcustomAgreement;
  /**
   * 勾选协议回调
   */
  onChange?: (e: any) => void;
}

const Agreement = React.forwardRef<IAgreementRef, IAgreementProps>(
  (props, ref) => {
    const [_checked, setChecked] = useState<boolean>(false);
    const [agreementInfo, setAgreementInfo] = useState<AgreementInfo>();

    /** 获取协议数据 */
    const initAgreementInfo = () => {
      if (!props.request) {
        message.error(`Agreement组件初始化失败，缺失request参数！`);
      } else {
        getAgreementInfo(props.request, { agreementCode: 'personalAgreement' })
          .then((res: any) => {
            if (res && res.code === 1) {
              if (res.data) {
                const _agree: AgreementInfo = {
                  agreementId: res.data.agreementId,
                  agreementVersion: res.data.agreementVersion,
                  agreementName: res.data.agreementTitle
                    ? res.data.agreementTitle
                    : '《》',
                  agreementLink: res.data.agreementLink
                    ? res.data.agreementLink
                    : '#',
                };
                setAgreementInfo(_agree);
              }
            } else {
              message.error(`获取用户协议信息失败${res.tips ? res.tips : ''}`);
            }
          })
          .catch((err: any) => {
            console.error(`获取用户协议信息失败${err}`);
          });
      }
    };

    /** checkbox change */
    const handleChange = (e: any) => {
      if (props.onChange && isFunction(props.onChange)) {
        props.onChange(e);
      }
    };

    useEffect(() => {
      setChecked(props.checked);
    }, [props.checked]);
    useEffect(() => {
      console.log(props.resourceKey, props.pageFlag);
      if(props.customAgreement){
        const customAgreementInfo = {
          agreementName: props.customAgreement.agreementName,
          agreementLink: props.customAgreement.agreementLink
        }
        setAgreementInfo(customAgreementInfo);
      }else {
        initAgreementInfo();
      }
    }, []);

    useImperativeHandle(
      ref,
      () => {
        const _handler = {} as IAgreementRef;
        _handler.info = agreementInfo ? agreementInfo : {};
        return _handler;
      },
      [agreementInfo],
    );

    return (
      <div className="agreement-container">
        {agreementInfo && (
          <Checkbox checked={_checked} onChange={handleChange}>
            阅读并同意
            <a
              href={agreementInfo.agreementLink}
              target="_blank"
              rel="noreferrer"
            >
              <span style={{ color: '#2761F3' }}>
                {agreementInfo.agreementName}
              </span>
            </a>
          </Checkbox>
        )}
      </div>
    );
  },
);

export default Agreement;
