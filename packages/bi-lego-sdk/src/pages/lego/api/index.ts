// @ts-nocheck
import request from '@/utils/request';
// import { RequestQueue } from './requestQueue';
import { isLegoDev } from '@/utils/common';
import sdkConfig from '@/tool/config';

import {
  DataSourceDetail,
  DataSourceItem,
  ResBaseData,
  ResData,
} from './types';

// 创建分批查询队列实例
// const queryInstance = new RequestQueue<unknown, ResBaseData<ResData>>();

// bigbang ignore export queryData
export const queryData = (params: unknown): Promise<ResBaseData<ResData>> => {
  return request({
    url: '/api/v1/queryDataList',
    method: 'POST',
    data: params,
  });
};

// 取得数据集列表
export const queryDataSourceList = (): Promise<
  ResBaseData<DataSourceItem[]>
> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryAllDatasets',
    method: 'POST',
    data: {},
  });
};

// 取得数据集详情
export const queryDataSourceDetail = ({
  dataSourceId,
  title,
}: {
  dataSourceId: number; // 数据源ID 必填
  title?: string; //字段名称 非必填null默认筛选全部数据集下字段
}): Promise<ResBaseData<DataSourceDetail>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryDatasetsColumnsV2',
    method: 'POST',
    data: {
      datasetId: dataSourceId,
      title,
    },
  });
};

// 公式验证
export const verifySql = (params: unknown): Promise<ResBaseData<any>> => {
  // @ts-expect-error
  return request({
    url: '/admin/v1/ai/chart-manager/validateVirtualField',
    method: 'POST',
    data: params,
    notMessage: true,
  });
};

// 公式保存
export const saveSql = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/saveVirtualField',
    method: 'POST',
    data: params,
  });
};

// 公式获取
export const getSql = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryVirtualFieldByColumnId',
    method: 'POST',
    data: params,
  });
};

export const deleteVirtualField = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/deleteVirtualField',
    method: 'POST',
    data: params,
  });
};

// 获取指标数据
// bigbang ignore export getIndexData
export const getIndexData = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/api/v1/getIndexData',
    method: 'POST',
    data: params,
  });
};

export const queryReportPageList = (params: unknown) => {
  //@ts-ignore
  return request({
    url: !isLegoDev()
      ? '/admin/v1/ai/chart-manager/queryReportPageListForTemplate'
      : '/admin/v1/ai/chart-manager/queryReportPageList',
    method: 'POST',
    data: params,
    preloadReceive: true,
    key: 'queryReportPageList' + JSON.stringify(params),
  });
};

// 删除报告
export const deleteReport = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/deleteReport',
    method: 'POST',
    data: params,
  });
};

// 获取被分享人/转让人
export const queryShareList = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryShareList',
    method: 'POST',
    data: params,
  });
};

// 添加被分享人
export const addShares = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/addShares',
    method: 'POST',
    data: params,
  });
};

// 提交转让人
export const exchangeOwner = (params: unknown): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/exchangeOwner',
    method: 'POST',
    data: params,
  });
};

// 获取权限列表
export const queryUserAndDataSetsList = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  //@ts-ignore
  return request({
    url: '/admin/v1/ai/chart-manager/queryUserAndDataSetsList',
    method: 'POST',
    data: params,
    bindRoutePK: ['legoPermissionBI'],
  });
};

// 获取被授权人
export const queryUserListForAuthor = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryUserListForAuthor',
    method: 'POST',
    data: params,
  });
};

// 获取数据集
export const queryDomainAndDatasetsInfo = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/queryDomainAndDatasetsInfo',
    method: 'POST',
    data: params,
  });
};

// 添加人员权限
export const addDataSetsToUser = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/addDataSetsToUser',
    method: 'POST',
    data: params,
  });
};

// 获得画布数据
export const getPageStructure = (params: {
  pageMark?: string;
  reportId?: string;
  /* 0草稿态 1发布态 */
  publishStatus: 0 | 1;
}) => {
  // @ts-expect-error
  return request({
    url: '/admin/v1/ai/chart-manager/queryReportLayoutInfo',
    method: 'POST',
    data: params,
    notMessage: true,
    preloadReceive: true,
    key: 'queryReportLayoutInfo' + params.reportId + params.pageMark,
  });
};

// 删除人员权限
export const delDataSetsToUser = (
  params: unknown,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/delDataSetsToUser',
    method: 'POST',
    data: params,
  });
};

// 获取卡片数据
// bigbang ignore export getCardData
// export const getCardData = (
//   params: unknown,
//   repeatedSubmission: boolean | undefined,
// ): Promise<ResBaseData<ResData>> => {
//   return queryInstance.request(params, { repeatedSubmission });
// };

// bigbang ignore export getFilters
// export const getFilters = (params: unknown) => {
//   return queryInstance.request(params);
// };

// 保存组件数据
export const saveComponentData = (
  params: unknown,
  // token: any,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/saveElementStructureInfo',
    method: 'POST',
    data: params,
    repeatedSubmission: false,
    cancelKey: 'saveComponentData' + params?.elementId,
  });
};

// 批量保存数据关联
export const saveComponentsData = function (params: unknown) {
  // return Promise.all(params.map((v) => saveComponentData(v)));

  return request({
    url: '/admin/v1/ai/chart-manager/saveElementStructureInfos',
    method: 'POST',
    data: {
      list: params,
    },
    // 批量接口不能做取消上次的
  });
};

export const savePartialContainerFilterApi = function (params: unknown) {
  // return Promise.all(params.map((v) => saveComponentData(v)));

  return request({
    url: '/admin/v1/ai/chart-manager/saveFilterBindingInfos',
    method: 'POST',
    data: {
      list: params.list,
    },
  });
};

// 保存画布数据
export const savePageStructure = (
  params: unknown,
  token: any,
): Promise<ResBaseData<any>> => {
  return request({
    url: '/admin/v1/ai/chart-manager/saveReportLayoutInfo',
    method: 'POST',
    data: params,
    repeatedSubmission: false,
    cancelToken: token,
    // cancelKey: 'savePageStructure',
  });
};
// 新建报告
export const createReportInitStatus = (
  ownType?: number,
): Promise<
  ResBaseData<{
    id: number; // 报表ID
    reportName: string; // 报表名称
    reportId: string; // 报表唯一标识
  }>
> => {
  return request({
    url: '/admin/v1/ai/chart-manager/createReportInitStatus',
    method: 'POST',
    data: { ownType },
    preloadReceive: true,
  });
};

// 发布报告
export const publishReport = (params: unknown) => {
  return request({
    url: '/admin/v1/ai/chart-manager/publishReport',
    method: 'POST',
    data: params,
  });
};

// bigbang ignore export getComponentData
// export const getComponentData = (
//   params: unknown,
//   repeatedSubmission: boolean | undefined,
// ): Promise<ResBaseData<ResData>> => {
//   return queryInstance.request(params, { repeatedSubmission });
// };

export const checkTextSafe = (data = {}) => {
  return request({
    url: '/bos/admin/v2/base/manage/safety/textListScan',
    method: 'POST',
    data,
  });
};

export const copyTemplate = (reportId = '') => {
  return request({
    url: '/admin/v1/ai/chart-manager/copyTemplate',
    method: 'POST',
    data: { reportId },
  });
};

export const checkTextSafeNew = (data = {}) => {
  return request({
    url: isLegoDev()
      ? sdkConfig.get('sdkConfig.manageSensitiveInfoPort')
      : sdkConfig.get('sdkConfig.userSensitiveInfoPort'),
    method: 'POST',
    data,
  });
};
