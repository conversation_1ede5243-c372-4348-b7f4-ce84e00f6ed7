import request from '@/utils/request';

// 哈勃获取品牌接口
// export const getHubbleBrandData = () => {
//   return request({
//     url: '/saasApi/v1/admin/tenant/listTenants',
//     method: 'POST',
//     data: {
//       businessTypes: [1],
//       platformUsageList: [1, 2],
//       tenantType: 1,
//     },
//   });
// };

// 复制模板
export const copyTemplate = (data = {}) => {
  return request({
    url: '/admin/v1/ai/chart-manager/copyTemplate',
    method: 'POST',
    data: data,
  });
};

// 发布模板操作
export const publishReportState = (data = {}) => {
  return request({
    url: '/admin/v1/ai/chart-manager/releaseConfig/modifyTemplateStatus',
    method: 'POST',
    data: data,
  });
};

// 删除报告
export const deleteReport = (params: unknown) => {
  return request({
    url: '/admin/v1/ai/chart-manager/deleteReport',
    method: 'POST',
    data: params,
  });
};

// 获取所有放量配置
export const getAllConfig = () => {
  return request({
    url: '/dockingApi/v1/admin/invoker/adminTenantCustomerHService_getTenantList',
    method: 'POST',
    data: {},
  });
};

// 查询模板放量列表
export const getTemplateIncreaseQuantityList = (params: unknown) => {
  return request({
    url: '/admin/v1/ai/chart-manager/releaseConfig/queryTemplateStatus',
    method: 'POST',
    data: params,
  });
};

//撤回公共模板
export const revokeTemplate = (data: unknown) => {
  return request({
    url: '/admin/v1/ai/chart-manager/releaseConfig/revokeTemplate',
    method: 'POST',
    data,
  });
};
// 下线
export const offlineTemplate = (data: unknown) => {
  return request({
    url: '/admin/v1/ai/chart-manager/releaseConfig/offlineTemplate',
    method: 'POST',
    data,
  });
};

// 获取SP后台资源菜单
export const pageListData = (data: unknown) => {
  return request({
    url: '/dockingApi/v1/admin/invoker/page-list',
    method: 'POST',
    data,
  });
};

// 获取SP后台资源菜单 (FK)
export const functionListData = (data: unknown) => {
  return request({
    url: '/dockingApi/v1/admin/invoker/adminBlmFunctionHService_allFunctions',
    method: 'POST',
    data,
  });
};
