// @ts-nocheck
import { Select, Popover, Collapse } from '@blmcp/ui';
import React, {
  useState,
  useEffect,
  useImperativeHandle,
  ReactNode,
} from 'react';

import { CaretRightOutlined } from '@ant-design/icons';
import { Text } from '@/pages/lego/libraryMaterials/setter/AnalysisConfig/DataSourcePanel/Text';
import { FieldsItemType } from '../fieldsEnum';

import styles from './index.module.less';

enum TypeName {
  '维度' = 1,
  '指标',
}

interface SelectFieldsProps {
  title: string; // 字段标题
  data?: Array<any>; // 字段列表数据
  style?: any; // 样式
  group?: boolean; // 是否分组
  onClick: (value: FieldsItemType) => void; // 点击列表事件
  defaultValue?: string; //默认分组值
  type?: TypeName;
  disabledPopover?: boolean;
}

/** 选择字段组件 */
export const SelectFields = React.forwardRef(function (
  {
    title,
    data = [],
    style = {},
    group,
    defaultValue = '0',
    onClick,
    disabledPopover,
  }: SelectFieldsProps,
  ref,
) {
  // 列表数据
  const [list, setList] = useState<Array<FieldsItemType>>([]);
  const [value, setValue] = useState(defaultValue);
  // 下拉选择更新列表
  const handleChange = function (value: string) {
    setValue(value);
    // setList(data.find((v) => v.value === value).list || [])
  };
  useImperativeHandle(ref, () => {
    return {
      reset() {
        setValue(defaultValue);
      },
    };
  });

  // 数据检测
  useEffect(() => {
    setList(group ? data.find((v) => v.value === value)?.list || [] : data);
  }, [data, group, value]);
  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  let listDOM: ReactNode = null;

  const getItems = (indexGroup) => {
    return indexGroup.map((itemGroup) => {
      return {
        key: itemGroup.category,
        className: styles['panel-sub-content'],
        label: (
          <span className={styles['fold-title-wrap']}>
            <Text text={itemGroup.category}>
              <span className={styles['fold-title']}>{itemGroup.category}</span>
            </Text>
          </span>
        ),
        children: itemGroup?.columnList?.map((item) => {
          const content = (
            <div className={styles['desc-popover']}>
              <span className={styles.name}>{item.name}</span>
              <span>指标说明：{item?.fieldDesc}</span>
            </div>
          );
          return (
            <Popover
              key={item.key}
              placement="left"
              title={''}
              open={disabledPopover ? false : undefined}
              content={content}
            >
              <li
                key={item.key}
                className={styles.textOverflow}
                onMouseDown={(event) => event.preventDefault()}
                onClick={() => {
                  onClick(item);
                  return false;
                }}
              >
                {item.name}
              </li>
            </Popover>
          );
        }),
      };
    });
  };
  if (title === '指标') {
    // 分组数据展示
    listDOM = (
      <Collapse
        key={'index'}
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        items={getItems(list)}
      />
    );
  } else {
    // 常规数据展示
    listDOM = list.map((item) => {
      const content = (
        <div className={styles['desc-popover']}>
          <span className={styles.name}>{item.name}</span>
          <span className={styles['desc']}>
            {title}说明：{item?.fieldDesc}
          </span>
        </div>
      );
      return (
        <Popover
          key={item.key}
          placement="left"
          title={''}
          open={disabledPopover ? false : undefined}
          content={content}
        >
          <li
            className={styles.textOverflow}
            onMouseDown={(event) => event.preventDefault()}
            onClick={() => {
              onClick(item);
              return false;
            }}
          >
            {item.name}
          </li>
        </Popover>
      );
    });
  }

  return (
    <div className={styles.SelectFields} style={style}>
      {(!group && <h1>{title}</h1>) || (
        <Select
          className="bi-lego-AddCalculateFields-SelectFields"
          value={value}
          style={{ width: '100%' }}
          bordered={false}
          onChange={handleChange}
          options={data}
        />
      )}
      <ul>{listDOM}</ul>
    </div>
  );
});
