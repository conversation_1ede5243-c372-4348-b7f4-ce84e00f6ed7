.lego-bi-calculate-form{
  .ant-form-item{
    margin-right: 16px;
  }
  .ant-form-item-label{
    font-size: 14px;
    font-weight: 500;
    >label::after{
      visibility: hidden;
    }
  }
}

.lego-bi-form-inline{
  .ant-form-item:last-child{
    margin-right: 0
  }
}

// 滚动条默认隐藏，滚动显示
.lego-bi-scroll-hide .cm-scroller::-webkit-scrollbar{
  width: 5px;
  height: 5px;
}
.lego-bi-scroll-hide .cm-scroller::-webkit-scrollbar-thumb{
  background-color: transparent;
}
.lego-bi-scroll-hide:hover .cm-scroller::-webkit-scrollbar-thumb{
  background-color: rgba(0,0,0,.3);
}

/* 快捷公式编辑框样式 */
.lego-tagfor-leftEditBox,.lego-tagfor-rightEditBox{
  .cm-activeLine,.cm-activeLineGutter{
    background: #E8F2FF !important;
  }

  .cm-gutterElement:first-child{
    height: 0 !important;
  }
  .cm-gutterElement{
    box-sizing: content-box;
    padding: 0;
    height: auto !important;
  }
  
  // .cm-line.cm-line-error-placeholder{
  //   position: relative;
  //   background: #FDECEE !important;
  //   &::after{
  //     content: '必须为数值类型';
  //     display: block;
  //     color: #E34D59;
  //     font-size: 12px;
  //     font-weight: normal;
  //     line-height: 20px;
  //   }
  // }
  
  // .cm-gutterElement.cm-line-error-placeholder{
  //   position: relative;
  //   background: #FDECEE !important;
  // }
  
  .cm-placeholder{
    width: 150px;
    top: 80px;
    position: absolute;
    margin: auto;
    transform: translate(50%, 50%);
    text-align: center;
    line-height: 20px;
    font-weight: normal;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
  }
  .cm-gutters{
    background: #fff !important;
  }
}

.TargetFormulaModalTree .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-title{
  color: rgba(0, 0, 0, 0.9)
}