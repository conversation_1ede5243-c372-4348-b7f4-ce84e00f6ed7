@border-color: #ebeef5;
.panel-sub-content {
  font-size: 13px;
  font-weight: normal;
  border-bottom: 0px !important;

  padding: 0px;
  :global {
    .ant-collapse-header {
      border-radius: 6px !important;
      background-color: white;
      margin: 0px 0px;
      padding: 9px 6px !important;
    }
  }
}
.fold-title-wrap {
  display: flex;
  align-items: center;
  max-width: 64px;
}
.fold-title {
  font-size: 13px;
  font-weight: normal;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);

  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 不换行 */
}
.desc-popover {
  max-width: 321px;
  min-width: 120px;
  max-height: 98px;
  display: flex;
  flex-direction: column;
  margin: 4px;
  overflow: auto;
}
.name {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.9);
}
.desc {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);
}

.textOverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.SelectFields {
  display: inline-block;
  width: 100%;
  height: 100%;
  > h1 {
    font-size: 13px;
    font-weight: 500;
    padding: 10px;
    line-height: 1;
    border-bottom: 1px solid @border-color;
  }
  > ul {
    height: calc(100% - 65px);
    overflow: auto;
    padding: 0;
    :global {
      .ant-collapse-content-box {
        padding: 0px !important;
        background: white;
      }
    }

    & li {
      padding: 10px;
      font-size: 13px;
      cursor: pointer;
      line-height: 1;
    }
    & li:hover {
      background: rgba(37, 52, 79, 0.05);
      border-radius: 6px;
    }
  }
}
