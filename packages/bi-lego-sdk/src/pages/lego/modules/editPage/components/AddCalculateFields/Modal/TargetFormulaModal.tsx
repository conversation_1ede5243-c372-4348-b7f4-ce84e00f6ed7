import { Modal, Select, TreeSelect, Form, message, Button } from '@blmcp/ui';
import {
  useState,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useEffect,
  useCallback,
} from 'react';

import { EditorState } from '@codemirror/state';
import {
  EditorView,
  placeholder,
  lineNumbers,
  highlightActiveLineGutter,
  highlightActiveLine,
  keymap,
} from '@codemirror/view';
import { history, historyKeymap, defaultKeymap } from '@codemirror/commands';
// @ts-expect-error
import styles from './style.module.less';

const options = [
  { value: 0, label: '文本' },
  { value: 1, label: '数值' },
];

interface TreeDataItem {
  name: string;
  key: string;
  columnList: TreeDataItem[];
}

interface FormulaModalProps {
  // 下拉树数据
  treeData: TreeDataItem[];
  // 确定回调
  onOk: (str: string, type: number) => void;
  // 模态框标题
  title: string;
  leftLabel: string;
  rightLabel: string;
  leftHead: string;
  rightHead: string;
  // 默认类型
  initType: number;
  leftPlaceholder: string;
  rightPlaceholder: string;
}

function FormulaModal(props: FormulaModalProps, ref: any) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const {
    treeData,
    onOk,
    title,
    leftHead,
    leftLabel,
    rightHead,
    rightLabel,
    initType,
    rightPlaceholder,
    leftPlaceholder,
  } = props;
  const leftEditDomRef = useRef<any>();
  const rightEditDomRef = useRef<any>();
  const leftEditRef = useRef<EditorView>({} as any);
  const rightEditRef = useRef<EditorView>({} as any);
  const [editLineState, setEditLineState] = useState<any>({});
  const [targetIndex, setTargetIndex] = useState<any>(0);
  const [originIndex, setOriginIndex] = useState<any>(0);
  const targetOnoff = useRef<any>(true);

  const styleState = useMemo(() => {
    let str = '';
    const errorStyle = (
      index: any,
      originIndex: number,
      item: any,
      key: string,
    ) => {
      if (!item[key]) return '';
      const originClassName = key === 'left' ? 'leftEditBox' : 'rightEditBox';

      return `.lego-tagfor-${originClassName} .cm-content .cm-line:nth-child(${
        index + (originIndex ? 2 : 1)
      }){
        position: relative;
        background: #FDECEE !important;
      }
      .lego-tagfor-${originClassName}  .cm-content .cm-line:nth-child(${
        index + (originIndex ? 2 : 1)
      })::after{
        content: "${item.info || '与当前所选字段的数值类型不符'}";
        display: block;
        color: #E34D59;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        position: absolute;
        bottom: 0;
      }
      .lego-tagfor-${originClassName} .cm-gutter .cm-gutterElement:nth-child(${
        index + 2
      }) {
        background: #FDECEE !important;
      }
      `;
    };
    // eslint-disable-next-line guard-for-in
    for (let k in editLineState) {
      const index = Number(k);
      const targetFromIndex = index - targetIndex;
      const originFromIndex = index - originIndex;
      const item = editLineState[index];
      const error = item.left || item.right;
      if (!error) continue;
      str += `
      ${errorStyle(targetFromIndex, targetIndex, item, 'right')}
      ${errorStyle(originFromIndex, originIndex, item, 'left')}
      `;

      if (error) {
        str += `
        .lego-tagfor-leftEditBox .cm-gutter .cm-gutterElement:nth-child(${
          originFromIndex + 2
        }) {
          padding-bottom: ${error ? 20 : 0}px;
        }
        .lego-tagfor-leftEditBox .cm-line:nth-child(${
          originFromIndex + (originIndex ? 2 : 1)
        }) {
          padding-bottom: ${error ? 20 : 0}px;
        }
        .lego-tagfor-rightEditBox .cm-gutter .cm-gutterElement:nth-child(${
          targetFromIndex + 2
        }) {
          padding-bottom: ${error ? 20 : 0}px;
        }
        .lego-tagfor-rightEditBox .cm-line:nth-child(${
          targetFromIndex + +(targetIndex ? 2 : 1)
        }) {
          padding-bottom: ${error ? 20 : 0}px;
        }
        `;
      }
    }
    return str;
  }, [editLineState, targetIndex, originIndex]);

  const events = EditorView.domEventHandlers({
    scroll(event) {
      const isLeft = event.target === leftEditRef.current.scrollDOM;
      const originDOM = isLeft
        ? leftEditRef.current.scrollDOM
        : rightEditRef.current.scrollDOM;
      const targetDOM = isLeft
        ? rightEditRef.current.scrollDOM
        : leftEditRef.current.scrollDOM;

      targetDOM.scrollTop = originDOM.scrollTop;

      const targetList2 =
        rightEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');
      const originList2 =
        leftEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');

      const originFromIndex = Number(originList2[1].innerHTML) - 1;
      const targetFromIndex = Number(targetList2[1].innerHTML) - 1;

      setTargetIndex(targetFromIndex);
      setOriginIndex(originFromIndex);
    },
  });

  const change = EditorView.updateListener.of((editView) => {
    if (!editView.selectionSet) return;
    if (!targetOnoff.current) return;
    const isLeft = editView.view.dom === leftEditRef.current.dom;
    const originView = isLeft ? leftEditRef.current : rightEditRef.current;
    const focus = originView.state.selection.ranges[0];
    const targetEditView = isLeft ? rightEditRef.current : leftEditRef.current;

    // const styleArr = [];
    const originDOM = isLeft
      ? leftEditRef.current.scrollDOM
      : rightEditRef.current.scrollDOM;
    const targetDOM = isLeft
      ? rightEditRef.current.scrollDOM
      : leftEditRef.current.scrollDOM;
    const originActiveDom = isLeft
      ? leftEditRef.current.contentDOM
      : rightEditRef.current.contentDOM;
    if (originActiveDom !== document.activeElement) {
      return false;
    }

    setEditLineState({});

    // const originList = originDOM.querySelectorAll('.cm-line');
    // const targetList = targetDOM.querySelectorAll('.cm-line');
    const targetList2 = targetDOM.querySelectorAll('.cm-gutterElement');
    const originList2 = originDOM.querySelectorAll('.cm-gutterElement');

    const originFromIndex = Number(originList2[1].innerHTML) - 1;
    const targetFromIndex = Number(targetList2[1].innerHTML) - 1;
    const index = originView.state.doc.lineAt(focus.from).number - 1;
    // const _originIndex = index - originFromIndex;
    // const _targetIndex = index - targetFromIndex;

    if (targetIndex !== targetFromIndex) {
      setTargetIndex(targetFromIndex);
    }
    if (originIndex !== originFromIndex) {
      setOriginIndex(originFromIndex);
    }

    // 删除所有err 高亮
    // for (let i = 0; i < originList.length; i++) {
    //   originList[i].classList.remove('cm-line-error-placeholder');
    //   originList2[i + 1]?.classList.remove('cm-line-error-placeholder');
    // }
    // for (let i = 0; i < targetList.length; i++) {
    //   targetList[i].classList.remove('cm-line-error-placeholder');
    //   targetList2[i + 1]?.classList.remove('cm-line-error-placeholder');
    // }

    // 删除所有高亮，排查当前点击的
    // for (let i = 1; i < originList2.length; i++) {
    //   if (_originIndex + 1 === i) continue;
    //   originList2[i].classList.remove('cm-activeLineGutter');
    // }

    targetOnoff.current = false;
    const targetTexts = targetEditView.state.doc.toJSON();

    targetEditView.dispatch({
      selection: { anchor: targetTexts.slice(0, index + 1).join(',').length },
    });

    setTimeout(() => {
      targetOnoff.current = true;
    }, 10);

    // setTimeout(() => {
    //   // 延迟执行，解决active 不对问题
    //   for (let i = 0; i < targetList.length; i++) {
    //     if (i === _targetIndex) {
    //       targetList[_targetIndex].classList.add('cm-activeLine');
    //       targetList2[i + 1].classList.add('cm-activeLineGutter');
    //     } else {
    //       targetList[i]?.classList.remove('cm-activeLine');
    //       targetList2[i + 1]?.classList.remove('cm-activeLineGutter');
    //     }
    //   }
    // }, 10);

    return;
  });

  // 文本整体替换
  const replaceText = useCallback((editView: EditorView, text: string) => {
    const allText = editView.state.doc.toJSON().join();
    editView.dispatch({
      changes: {
        from: 0,
        to: allText.length,
        insert: text,
      },
      selection: {
        anchor: text.length,
      },
    });
  }, []);

  useEffect(() => {
    if (!isModalOpen) return () => {};
    setEditLineState({});
    setTargetIndex(0);
    setOriginIndex(0);
    setTimeout(() => {
      leftEditRef.current = new EditorView({
        state: EditorState.create({
          doc: '',
          extensions: [
            placeholder(leftPlaceholder),
            // EditorView.lineWrapping,
            lineNumbers(),
            events,
            change,
            highlightActiveLineGutter(),
            highlightActiveLine(),
            history(),
            keymap.of([...(defaultKeymap as any), ...(historyKeymap as any)]),
          ],
        }),
        parent: leftEditDomRef.current,
      });
      rightEditRef.current = new EditorView({
        state: EditorState.create({
          doc: '',
          extensions: [
            placeholder(rightPlaceholder),
            // EditorView.lineWrapping,
            lineNumbers(),
            events,
            change,
            highlightActiveLineGutter(),
            highlightActiveLine(),
            history(),
            keymap.of([...(defaultKeymap as any), ...(historyKeymap as any)]),
          ],
        }),
        parent: rightEditDomRef.current,
      });
    });

    return () => {
      leftEditRef.current?.destroy();
      rightEditRef.current?.destroy();
      setEditLineState({});
      setTargetIndex(0);
      setOriginIndex(0);
    };
  }, [isModalOpen]);

  const fieldsMap = useMemo(() => {
    const map: any = {};
    treeData?.forEach((child) => {
      if (!map[child.key]) {
        map[child.key] = child;
      }

      child?.columnList?.forEach((item) => {
        map[item.key] = item;
        item?.columnList?.forEach((item) => {
          map[item.key] = item;
        });
      });
    });
    return map;
  }, [treeData]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const originInfo = fieldsMap[values.origin];
      const originTexts = leftEditRef.current.state.doc.toJSON() || [];
      const targetTexts = rightEditRef.current.state.doc.toJSON() || [];
      const rightNumber = values.type === 1;
      const leftNumber = originInfo.other.dataType === 1;
      const originList =
        leftEditRef.current.scrollDOM.querySelectorAll('.cm-line');
      const targetList =
        rightEditRef.current.scrollDOM.querySelectorAll('.cm-line');
      const originList2 =
        leftEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');
      const targetList2 =
        rightEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');

      for (let i = 0; i < originList.length; i++) {
        originList[i].classList.remove('cm-activeLine');
        originList2[i + 1]?.classList.remove('cm-activeLineGutter');
      }
      for (let i = 0; i < targetList.length; i++) {
        targetList[i].classList.remove('cm-activeLine');
        targetList2[i + 1]?.classList.remove('cm-activeLineGutter');
      }

      leftEditRef.current.dispatch({
        selection: { anchor: originTexts.join(',').length },
      });
      rightEditRef.current.dispatch({
        selection: { anchor: targetTexts.join(',').length },
      });

      // 行数校验
      if (originTexts.length > 200 || targetTexts.length > 200) {
        message.error('最多添加200个');
        return;
      }

      if (originTexts.length !== targetTexts.length) {
        message.error('来源值与目标值的数量不一致');
        return;
      }

      const condition: string[] = [];
      let error;
      // let styleArr = [];
      let editLineState: any = {};
      let errorIndex: undefined | number = undefined;
      originTexts.forEach((item: string, index: number) => {
        const leftValue = leftNumber ? Number(item) ?? 0 : `'${item || ''}'`;
        const rightValue = rightNumber
          ? Number(targetTexts[index]) ?? 0
          : `'${targetTexts[index] || ''}'`;

        editLineState[index] = {
          left: false,
          right: false,
        };
        // 来源值校验
        if (leftNumber && Number.isNaN(leftValue)) {
          editLineState[index].left = true;
          error = true;
          if (errorIndex === undefined) {
            errorIndex = index;
          }
        }
        // 目标值校验
        if (rightNumber && Number.isNaN(rightValue)) {
          editLineState[index].right = true;
          error = true;
          if (errorIndex === undefined) {
            errorIndex = index;
          }
        }

        condition.push(
          `WHEN [${originInfo.name}] = ${leftValue} THEN ${rightValue}`,
        );
      });

      if (errorIndex !== undefined) {
        leftEditRef.current.scrollDOM.scrollTop = errorIndex * 20 - 100;
      }
      setEditLineState({ ...editLineState });
      setTimeout(() => {
        const targetList2 =
          rightEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');
        const originList2 =
          leftEditRef.current.scrollDOM.querySelectorAll('.cm-gutterElement');

        const originFromIndex = Number(originList2[1]?.innerHTML || 1) - 1;
        const targetFromIndex = Number(targetList2[1]?.innerHTML || 1) - 1;

        setTargetIndex(targetFromIndex);
        setOriginIndex(originFromIndex);
      }, 10);

      if (error) return false;

      const str = `CASE\n${condition.join('\n')}\nELSE ${
        rightNumber ? 0 : "'未映射'"
      } END`;
      onOk(str, values.type);
      setIsModalOpen(false);
    });
  };

  useImperativeHandle(ref, () => ({
    open: () => {
      form.resetFields();
      setIsModalOpen(true);
    },
  }));

  return (
    <Modal
      title={title}
      width={720}
      open={isModalOpen}
      closable={true}
      centered={true}
      destroyOnClose
      onCancel={() => setIsModalOpen(false)}
      onOk={handleOk}
    >
      <Form
        form={form}
        layout="inline"
        initialValues={{ type: initType }}
        className="lego-bi-form-inline"
      >
        <Form.Item
          name="origin"
          label={leftLabel}
          rules={[{ required: true, message: `请选择${leftLabel}` }]}
        >
          <TreeSelect
            showSearch
            treeData={treeData}
            style={{ width: '255px' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder={`请选择${leftLabel}`}
            treeDefaultExpandAll
            treeNodeFilterProp={'name'}
            popupClassName="TargetFormulaModalTree"
            fieldNames={{
              label: 'name',
              value: 'key',
              children: 'columnList',
            }}
          ></TreeSelect>
        </Form.Item>
        <Form.Item
          name="type"
          label={rightLabel}
          rules={[{ required: true, message: `请选择${rightLabel}` }]}
        >
          <Select options={options} style={{ width: '248px' }}></Select>
        </Form.Item>
      </Form>
      <div className={styles.headerBox}>
        <div>{leftHead}</div>
        <div>{rightHead}</div>
      </div>
      <div className={styles.editBox}>
        <div ref={leftEditDomRef} className="lego-tagfor-leftEditBox" />
        <div ref={rightEditDomRef} className="lego-tagfor-rightEditBox" />
      </div>
      <Button
        type="link"
        style={{ padding: '4px 0' }}
        onClick={() => {
          const originList =
            initType === 1
              ? ['北京', '上海', '天津', '海南', '珠海']
              : [
                  '广州市',
                  '郑州市',
                  '长沙市',
                  '西安市',
                  '成都市',
                  '重庆市',
                  '贵阳市',
                  '天津市',
                ];
          const targetList =
            initType === 1
              ? [1000, 2000, 3000, 4000, 5000]
              : [
                  '华南',
                  '华中',
                  '华中',
                  '华中',
                  '华西',
                  '华西',
                  '华西',
                  '华北',
                ];
          replaceText(leftEditRef.current, originList.join('\n'));
          replaceText(rightEditRef.current, targetList.join('\n'));
        }}
      >
        操作示例
      </Button>
      <style>{styleState}</style>
    </Modal>
  );
}

export default forwardRef(FormulaModal);
