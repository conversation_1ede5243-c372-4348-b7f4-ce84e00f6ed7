// @ts-nocheck
import {
  Button,
  Modal,
  Form,
  Input,
  Select,
  Tooltip,
  message,
  Dropdown,
  Space,
} from '@blmcp/ui';
import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useImperativeHandle,
  useMemo,
} from 'react';
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import './common.less';

import { EditorState } from '@codemirror/state';
import {
  EditorView,
  ViewUpdate,
  MatchDecorator,
  Decoration,
  ViewPlugin,
  DecorationSet,
  placeholder,
  keymap,
} from '@codemirror/view';
import {
  snippet,
  autocompletion,
  snippetCompletion,
} from '@codemirror/autocomplete';
import { history, historyKeymap, defaultKeymap } from '@codemirror/commands';
import { getCurrentPageId } from '@/pages/lego/utils';
import { DatasetItem } from '@/pages/lego/type';
import { isValidNewFieldName } from '@/pages/lego/utils/validate';
import * as API from '../../../../api';
import Search from '../Search';
import { SelectFields } from './SelectFields';
import fieldsEnum, {
  FieldsItemType,
  fieldsAllEnum,
  fieldsKeyWords,
  polymerization,
} from './fieldsEnum';
import styles from './style.module.less';
import RateFormulaModal from './Modal/RateFormulaModal';
import TargetFormulaModal from './Modal/TargetFormulaModal';

// 特殊空格列表
const spaceList = [
  '00a0',
  '180E',
  '2000',
  '2028',
  '2029',
  '202F',
  '205F',
  '3000',
  'FEFF',
  '200A',
  '2009',
  '2002',
  '2003',
  '2004',
  '2005',
  '2006',
  '2007',
  '2008',
  '200B',
  '200C',
  '200D',
  '2060',
];
// 构造正则表达式
let pattern = '';
spaceList.forEach((item, index) => {
  if (index !== 0) {
    pattern += `|(\\u${item})`;
  } else {
    pattern += `(\\u${item})`;
  }
});

// console.log(pattern);

interface CalculateFieldsProps {
  dataSourceId: number; // 数据集ID
  wdData: DatasetItem[]; // 维度列表
  _dlData: DatasetItem[]; // 指标列表
  disabled?: boolean;
  onOk: (params: {
    columnId: number;
    dataType: 0 | 1 | 2;
    isNew: boolean;
  }) => void; // 点击确定事件
  children: React.ReactNode;
}

// 指标数据转SQL数据
const handleIndexData = function (data: any, key: any): Array<FieldsItemType> {
  return data
    .filter((v) => v.columnId !== key)
    .map((item: any) => {
      return {
        name: item.title,
        key: item.title + item.columnId,
        doc: '',
        sql: `[${item.title}]`,
        fieldDesc: item.fieldDesc,
      };
    });
};
// 分组指标数据转SQL数据
const handleGroupIndexData = function (
  data: any,
  key: any,
): Array<FieldsItemType> {
  return data.map((group) => {
    return {
      ...group,
      columnList: group.columnList
        .filter((v) => v.columnId !== key)
        .map((item: any) => {
          const { title, columnId, fieldDesc, ...other } = item;
          return {
            name: title,
            key: title + columnId,
            doc: '',
            sql: `[${title}]`,
            fieldDesc,
            other,
            columnId,
          };
        }),
    };
  });
};

export default React.forwardRef(function (
  {
    wdData,
    _dlData,
    dataSourceId,
    onOk,
    disabled,
    children,
  }: CalculateFieldsProps,
  ref,
) {
  const curEditColumnId = useRef<any>(null);
  const curEditIsAggr = useRef<boolean>(null);
  const RateFormulaModalRef = useRef<any>(null);
  const TargetFormulaModalRef = useRef<any>(null);
  const Target2FormulaModalRef = useRef<any>(null);

  // 编辑态 字段详情
  const [filedInfo, setFiledInfo] = useState(null);

  const TargetFormulaData = useMemo(() => {
    const wd = wdData.map((v) => ({
      other: { ...v },
      name: v.title,
      key: v.title,
    }));
    const dl = handleGroupIndexData(_dlData, curEditColumnId.current).map(
      (v) => {
        return {
          ...v,
          key: v.category,
          name: v.category,
          disabled: true,
        };
      },
    );
    return [
      {
        key: '维度',
        name: '维度',
        disabled: true,
        columnList: wd,
      },
      {
        key: '指标',
        name: '指标',
        disabled: true,
        columnList: dl,
      },
    ];
  }, [wdData, _dlData]);

  const dlData = useMemo(() => {
    const result = [];

    _dlData.forEach((it) => {
      result.push(...it.columnList);
    });
    return result;
  }, [_dlData]);

  // const wdData = useMemo(() => {
  //   return _wdData.filter((v) => v.columnId !== curEditColumnId.current);
  // }, [_wdData, curEditColumnId]);
  // const dlData = useMemo(() => {
  //   return _dlData.filter((v) => v.columnId !== curEditColumnId.current);
  // }, [_dlData, curEditColumnId]);

  // const typeName = TypeName[type];
  const [isModalOpen, setIsModalOpen] = useState(false);

  /** 输入SQL类 */
  // const [html, setHtml] = useState('AVG(<span>表达式</span>) * MIN(<span>表达式</span>)');
  const editRef = useRef<HTMLDivElement>();
  const viewRef = useRef<any>(null);
  const selectForRef = useRef<any>(null);
  // 公式说明
  const [cuFormula, setFormula] = useState<FieldsItemType>({
    sql: '',
    name: '',
    doc: '',
    key: '',
  });
  // 语法校验信息
  const [errorInfo, setErrorInfo] = useState('');
  // 校验loading
  const [errorInfoLoading, setErrorInfoLoading] = useState(false);
  // 确定按钮态
  const [okButtonLoading, setOkButtonLoading] = useState(false);

  // 转换sql
  const transFromSql = useCallback(
    (sql: string) => {
      const keyMap: { [key: string]: DatasetItem } = {};
      dlData.forEach((v) => {
        keyMap[v.title] = v;
      });
      wdData.forEach((v) => {
        keyMap[v.title] = v;
      });
      return sql.replace(/\[[^\]]*\]/g, function (key: string) {
        const item = keyMap[key.replace(/\[|\]/g, '')] || {};
        return `[${item.columnId}]`;
      });
    },
    [dlData, wdData],
  );

  // 关键字搜索
  const keyWordPlugin = useCallback(() => {
    const indexDatas = [
      ...dlData.map((v) => v.title),
      ...wdData.map((v) => v.title),
      ...fieldsKeyWords,
    ].sort((a, b) => b.length - a.length);
    // const keywords = [];
    const placeholderMatcher = new MatchDecorator({
      regexp: new RegExp(`${indexDatas.join('|')}|'`, 'gi'),
      decoration: function (match, view, pos) {
        const lineText = view.state.doc.toJSON().join() || '';
        const [matchText] = match;
        if (
          matchText === "'" ||
          // keywords.includes(matchText) ||
          fieldsKeyWords.includes(matchText) ||
          (lineText[pos - 1] === '[' &&
            lineText[pos + matchText.length] === ']')
        ) {
          return Decoration.mark({
            attributes: {
              style: 'color: #FF9C2A;',
            },
          });
        } else {
          Decoration.mark({});
        }
      },
    });

    return ViewPlugin.fromClass(
      class {
        decorations: DecorationSet;
        constructor(view: EditorView) {
          this.decorations = placeholderMatcher.createDeco(view);
        }
        update(update: ViewUpdate) {
          this.decorations = placeholderMatcher.updateDeco(
            update,
            this.decorations,
          );
        }
      },
      {
        decorations: (v) => v.decorations,
      },
    );
  }, [dlData, wdData]);

  // 输入提示-函数
  const funHelpWordPlugin = useCallback((context: any) => {
    const keyWords = ['LIKE', 'IN', 'AND', 'OR'];
    let word = context.matchBefore(/\w*/);
    if (word.from === word.to && !context.explicit) return null;
    const options = fieldsAllEnum.map((v) =>
      snippetCompletion(v.sql, {
        label: v.key,
        type: 'function',
        detail: '函数',
      }),
    );
    keyWords.forEach((v: string) => {
      options.push(
        snippetCompletion(v, {
          label: v,
          type: 'keyword',
          detail: '关键字',
        }),
      );
    });
    return {
      from: word.from,
      options: options,
    };
  }, []);

  // 输入提示-数据集
  const indexHelpWordPlugin = useCallback(
    (context: any) => {
      let word = context.matchBefore(/[\u4e00-\u9fa5]*|\w*/);
      if (word?.from === word?.to && !context.explicit) return null;
      return {
        from: word.from,
        options: [
          ...wdData
            .filter((v) => v.columnId !== curEditColumnId.current)
            .map((v) => ({
              label: v.title,
              type: 'keyword',
              apply: `[${v.title}]`,
              detail: '维度',
            })),
          ...dlData
            .filter((v) => v.columnId !== curEditColumnId.current)
            .map((v) => ({
              label: v.title,
              type: 'keyword',
              apply: `[${v.title}]`,
              detail: '指标',
            })),
        ],
      };
    },
    [wdData, dlData],
  );

  useEffect(() => {
    if (!isModalOpen) return () => {};
    let startState = EditorState.create({
      doc: '',
      extensions: [
        history(),
        keymap.of([...defaultKeymap, ...historyKeymap]),
        placeholder('请输入公式'),
        // lineWrapping(true),
        EditorView.lineWrapping,
        keyWordPlugin(),
        autocompletion({ override: [indexHelpWordPlugin, funHelpWordPlugin] }),
      ],
    });
    setTimeout(() => {
      viewRef.current = new EditorView({
        state: startState,
        parent: editRef.current,
      });
    });

    return () => viewRef.current?.destroy();
  }, [dlData, wdData, isModalOpen]);

  // 插入
  const insertText = useCallback((text: string, isTemplate?: boolean) => {
    const view = viewRef.current!;
    if (!view) return;

    const { state } = view;

    const [range] = state.selection.ranges || [];
    let texts = '';
    try {
      texts = state.doc.toJSON().join();
    } catch (e) {
      texts = '';
    }
    const segments = texts
      .split(/([\u4e00-\u9fa5\d\w]+|[^\u4e00-\u9fa5\d\w\s])/)
      .filter(Boolean);
    let sum = 0;
    let from = range.from;
    let to = range.to;
    const index = range.to;

    for (let i = 0; i < segments.length; i++) {
      if (sum <= index && index <= (sum += segments[i].length)) {
        // 左侧或右侧有【】（）偏移一位
        let current = segments[i];
        let startOffset = sum - segments[i].length;
        let lastOffset = sum;
        if (/\[|\(/.test(current[0])) {
          current = current.slice(1);
          startOffset += 1;
        }
        if (fieldsKeyWords.includes(current)) {
          from = startOffset;
          to = lastOffset;
        }
        break;
      }
    }

    view.focus();

    if (isTemplate) {
      snippet(text)(
        {
          state,
          dispatch: view.dispatch,
        },
        {
          label: text,
          detail: text,
        },
        from,
        to,
      );
    } else {
      view.dispatch({
        changes: {
          from: from,
          to: to,
          insert: text,
        },
        selection: {
          anchor: from + text.length,
        },
      });
    }
  }, []);

  // 公式插入处理
  const handleFormulaInsert = function (
    item: FieldsItemType,
    isTemplate?: boolean,
  ) {
    insertText(item.sql, isTemplate);
    if (fieldsAllEnum.find((f) => f.key === item.key)) {
      setFormula(item);
    }
  };

  // 文本整体替换
  const replaceText = useCallback((text: string) => {
    if (viewRef.current) {
      const allText = viewRef.current.state.doc.toJSON().join();
      viewRef.current.dispatch({
        changes: {
          from: 0,
          to: allText.length,
          insert: text,
        },
        selection: {
          anchor: text.length,
        },
      });
      viewRef.current.focus();
    }
  }, []);

  /**表单类 */
  const [form] = Form.useForm();
  // const fieldName = Form.useWatch('fieldName', form);
  // 校验sql
  const verifySql = function (callback?: () => void) {
    // 将nbsp空格转换为普通空格，否则后端校验会报错
    const sql = viewRef.current.state.doc
      .toString()
      ?.replace(new RegExp(pattern, 'g'), ' ');
    const dataType = form.getFieldValue('dataType');

    if (!sql) {
      setErrorInfo('表达式不能为空');
      return false;
    }

    if (sql.length > 10000) {
      setErrorInfo('当前公式超出最大长度，最多支持10000字符');
      return false;
    }

    if (dataType === undefined) {
      setErrorInfo('请选择字段类型');
      return false;
    }

    // sql 前端校验
    const regex =
      /(['"])(.*?)\1|[a-zA-Z]+|\d+|\[[^\]]*\]|\(|\)|\+|\-|\*|\/|<>|!=|\<=|\>=|\<|\>|\=/g;
    const transSQl = transFromSql(sql);
    const ast = transSQl.toLocaleUpperCase().match(regex) || [];

    let webVerify = true;
    let webMessage = '';
    // const getAstItem = (_i: number, type = 'pre') => {
    //   let index = _i;
    //   while (ast[index] === '(' || ast[index] === ')') {
    //     if (type === 'pre') {
    //       index--;
    //     } else {
    //       index++;
    //     }
    //   }
    //   return ast[index];
    // };
    const getWdInfo = (item: string) => {
      const columnId = Number(item.replace(/\[|\]/g, ''));
      return (
        wdData.find((v) => v.columnId === columnId) ||
        dlData.find((v) => v.columnId === columnId)
      );
    };
    const getFnContent = (index: number) => {
      let start = index;
      let startNum = 0;
      let content: string[] = [];
      for (let i = start + 1; i < ast.length; i++) {
        if (ast[i] === '(') {
          startNum++;
          continue;
        }
        if (ast[i] === ')') {
          startNum--;
          if (startNum === 0) {
            break;
          }
          continue;
        }
        content.push(ast[i]);
      }
      return content;
    };
    // const whileList = ['COUNT', 'COUNTD', 'IF', 'CASE', 'WHEN', 'THEN', 'ELSE'];
    const polyList = polymerization.map((v) => v.key);
    // const blackKeyword = fieldsAllEnum
    //   .map((v) => v.key)
    //   .filter((v) => !whileList.includes(v));
    for (let index = 0; index < ast.length; index++) {
      const item = ast[index];
      if (item === '(' || item === ')') continue;
      if (
        polyList.includes(item) &&
        getFnContent(index).some((s) => polyList.includes(s))
      ) {
        webVerify = false;
        webMessage = '聚合函数使用错误，聚合函数内不支持再次聚合';
        break;
      }

      // 判断右侧值是否合法
      if (/[\<\>\=\!=]/.test(item)) {
        const rightValue: any = ast[index + 1] && ast[index + 1];
        if (rightValue) {
          if (ast[index - 1] && ast[index - 1].startsWith('[')) {
            const rightInfo = getWdInfo(rightValue);
            const info = getWdInfo(ast[index - 1]);
            if (info && rightInfo && info.dataType !== rightInfo.dataType) {
              webVerify = false;
              webMessage = '两端数据类型不一致不能进行对等或比较';
              break;
            } else if (
              !rightInfo &&
              info &&
              info.dataType === 0 &&
              !isNaN(rightValue)
            ) {
              webVerify = false;
              webMessage = '维度不能和数值进行对等或比较';
              break;
            } else if (
              !rightInfo &&
              info &&
              info.dataType !== 0 &&
              isNaN(rightValue)
            ) {
              webVerify = false;
              webMessage = '数值不能和字符串进行对等或比较';
              break;
            }
          }
        }
      }

      // LIKE IN 判断
      if (/^(LIKE|IN)/.test(item)) {
        break;
        // 左侧需为数据集
        // const leftValue: any = ast[index - 1] && ast[index - 1];
        // const leftInfo = getWdInfo(leftValue);
        // if (!leftValue || !leftInfo || leftInfo.dataType === 1) {
        //   webVerify = false;
        //   webMessage = 'LIKE 语句前面需为维度';
        //   break;
        // }
      }

      if (
        dataType === 1 &&
        !item.startsWith('[') &&
        /[^a-zA-Z\u4E00-\u9FA5\d'"\<\>\=\!=]/.test(item)
      ) {
        if (ast[index - 1] && ast[index - 1].startsWith('[')) {
          const info = getWdInfo(ast[index - 1]);
          if (info && info.dataType === 0) {
            webVerify = false;
            webMessage = '维度不能直接进行运算';
            break;
          }
        }
        if (ast[index + 1] && ast[index + 1].startsWith('[')) {
          const info = getWdInfo(ast[index + 1]);
          if (info && info.dataType === 0) {
            webVerify = false;
            webMessage = '维度不能直接进行运算';
            break;
          }
        }
      }
    }

    if (!webVerify) {
      setErrorInfo(webMessage);
      return false;
    }

    setErrorInfoLoading(true);
    const fieldName = form.getFieldValue('fieldName');
    API.verifySql({
      reportId: getCurrentPageId(),
      dataSourceId,
      fieldName,
      showExpression: sql,
      oriExpression: transSQl,
      dataType,
      columnId: curEditColumnId.current,
      isAggr: curEditIsAggr.current,
    })
      .then((res) => {
        if (res.code === 1 && res.data) {
          setErrorInfo('语法校验通过');
          if (typeof callback === 'function') {
            callback();
          }
        } else {
          setErrorInfo(res.msg);
          setOkButtonLoading(false);
        }
      })
      .catch((e) => {
        setErrorInfo(e?.msg || '接口出错请重试');
        setOkButtonLoading(false);
      })
      .finally(() => {
        setErrorInfoLoading(false);
      });
  };
  // 提交
  const handleOk = function () {
    setOkButtonLoading(true);
    // 验证SQL
    const verify = verifySql(() => {
      const sql = viewRef.current.state.doc
        .toString()
        ?.replace(new RegExp(pattern, 'g'), ' ');
      const fieldName = form.getFieldValue('fieldName');
      const dataType = form.getFieldValue('dataType') || 0;
      form
        .validateFields()
        .then(() => {
          API.saveSql({
            dataType,
            reportId: getCurrentPageId(),
            dataSourceId,
            fieldName,
            columnId: curEditColumnId.current,
            showExpression: sql,
            oriExpression: transFromSql(sql),
            isAggr: curEditIsAggr.current,
          })
            .then((res) => {
              if (res.code === 1) {
                setIsModalOpen(false);
                onOk({
                  columnId: res?.data?.id || curEditColumnId.current,
                  dataType,
                  isNew: !curEditColumnId.current,
                });
              }
            })
            .finally(() => {
              setOkButtonLoading(false);
            });
        })
        .catch((e) => {
          console.log('e', e);
          setOkButtonLoading(false);
        });
    });
    if (verify === false) {
      setOkButtonLoading(false);
    }
  };

  // 修改显示
  useImperativeHandle(ref, () => {
    return {
      update(columnId: number) {
        setErrorInfo('');
        curEditColumnId.current = columnId;
        API.getSql({
          columnId,
          reportId: getCurrentPageId(),
          dataSourceId,
        }).then((res) => {
          setFormula(polymerization[0]);
          setIsModalOpen(true);
          form.setFieldsValue({
            fieldName: res.data.fieldName,
            dataType: res.data.dataType,
          });
          setFiledInfo(res.data);
          curEditIsAggr.current = res.data.isAggr;
          // 模态框每次要重新创建需要一定时间
          setTimeout(() => {
            replaceText(res.data.showExpression || '');
          });
        });
      },
    };
  });

  // 下拉菜单
  const DropdownItem = [
    {
      key: '1',
      label: (
        <div className={styles.dropdownItem}>
          <p>率值公式</p>
          <span>计算比率类指标(完单率、补贴率等)</span>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div className={styles.dropdownItem}>
          <p>目标值设定</p>
          <span>基于城市判断KPI、返佣比例等</span>
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <div className={styles.dropdownItem}>
          <p>维度值映射</p>
          <span>多个值进行映射，如区域划分、运力属性等</span>
        </div>
      ),
    },
  ];

  return (
    <span
      onClick={(e: any) => {
        e.stopPropagation();
        e.preventDefault();
        return false;
      }}
    >
      <Tooltip placement="left" title={disabled ? '' : '新建计算字段'}>
        <span
          onClick={() => {
            if (disabled) {
              return;
            }
            if (dataSourceId === undefined) {
              message.info('请先选择数据集');
              return;
            }
            setFiledInfo(null);
            setFormula(polymerization[0]);
            setErrorInfo('');
            form.resetFields();
            replaceText('');
            if (selectForRef.current) {
              selectForRef.current.reset();
            }
            curEditColumnId.current = null;
            setIsModalOpen(true);
            return false;
          }}
        >
          <PlusOutlined
            style={{
              color: disabled ? 'rgba(0, 0, 0, 0.3)' : '#366cfe',
              cursor: disabled ? 'auto' : 'pointer',
            }}
          />
          {children}
        </span>
      </Tooltip>
      <Modal
        title={`${curEditColumnId.current ? '修改' : '新建'}计算字段`}
        width={1080}
        open={isModalOpen}
        closable={true}
        centered={true}
        destroyOnClose
        okButtonProps={{ loading: okButtonLoading }}
        onOk={handleOk}
        onCancel={() => setIsModalOpen(false)}
      >
        <div className={styles.calculateFieldsHeader}>
          <Form className="lego-bi-calculate-form" layout="inline" form={form}>
            <Form.Item
              label={'字段名称'}
              name="fieldName"
              rules={[
                { required: true, message: '名称不能为空' },
                ({}) => ({
                  validator(_, value: string) {
                    if (!value) return Promise.resolve();
                    if (value.length > 20) {
                      return Promise.reject(new Error('长度超过20个汉字'));
                    }
                    if (!isValidNewFieldName(value)) {
                      return Promise.reject(new Error('名称只支持中英文'));
                    }
                    // if (!isValidName(value)) {
                    //   return Promise.reject(
                    //     new Error('名称使用的符号不符合规范'),
                    //   );
                    // }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input
                disabled={!!curEditColumnId.current}
                style={{ width: '240px' }}
                autoComplete={'off'}
                placeholder="请输入名称"
              />
            </Form.Item>
            <Form.Item label="字段类型" name="dataType">
              <Select
                disabled={filedInfo?.usedFlag === 1}
                style={{ width: '180px' }}
                options={[
                  { value: 0, label: '文本' },
                  { value: 1, label: '数值' },
                  // { value: 2, label: '日期' },
                ]}
                placeholder="请选择字段类型"
              />
            </Form.Item>
          </Form>
          <Dropdown
            menu={{
              items: DropdownItem,
              onClick: ({ key }) => {
                if (key === '1') {
                  RateFormulaModalRef.current?.open();
                } else if (key === '2') {
                  TargetFormulaModalRef.current?.open();
                } else {
                  Target2FormulaModalRef.current?.open();
                }
              },
            }}
          >
            <a
              onClick={(e) => e.preventDefault()}
              style={{ lineHeight: '32px' }}
            >
              <Space>
                快捷公式
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        </div>
        <div className={styles.calculateFieldsBody}>
          <div className={styles.funBox}>
            <Search
              placeholder="搜索公式"
              description={'找不到公式'}
              data={fieldsAllEnum.map((v) => ({
                ...v,
                label: v.name,
                value: v.key,
              }))}
              onChange={(v) =>
                handleFormulaInsert(v as unknown as FieldsItemType, true)
              }
            />
            <SelectFields
              ref={selectForRef}
              title="函数"
              data={fieldsEnum}
              group
              onClick={(v) => handleFormulaInsert(v, true)}
              disabledPopover={true}
            />
          </div>
          <div className={styles.selectFieldsBox}>
            <Search
              placeholder="搜索字段"
              description={'找不到字段'}
              data={handleIndexData(
                [...wdData, ...dlData],
                curEditColumnId.current,
              ).map((v) => ({
                ...v,
                label: v.name,
                value: v.key,
              }))}
              onChange={(v) =>
                handleFormulaInsert(v as unknown as FieldsItemType, false)
              }
            />
            <SelectFields
              style={{ width: 'calc(50% - 5px)', marginRight: '10px' }}
              title="维度"
              data={handleIndexData(wdData, curEditColumnId.current)}
              onClick={(v) => handleFormulaInsert(v, false)}
            />
            <SelectFields
              style={{ width: 'calc(50% - 5px)' }}
              title="指标"
              data={handleGroupIndexData(_dlData, curEditColumnId.current)}
              onClick={(v) => handleFormulaInsert(v, false)}
            />
          </div>
          <div className={styles.formulaBox + ' lego-bi-scroll-hide'}>
            <div className={styles.headBox}>公式</div>
            <div
              ref={editRef}
              className={styles.editBox}
              placeholder="请输入公式"
            />
            <div style={{ textAlign: 'right', padding: '10px' }}>
              <Button onClick={verifySql} loading={errorInfoLoading}>
                语法校验
              </Button>
            </div>
            <div style={{ minHeight: '30px' }}>
              {errorInfo && (
                <div
                  title={errorInfo}
                  className={
                    errorInfo === '语法校验通过'
                      ? styles.successinfo
                      : styles.errorinfo
                  }
                >
                  {errorInfo}
                </div>
              )}
            </div>
          </div>
          <div className={styles.docBox}>
            <div className={styles.headBox}>{cuFormula.name}</div>
            <div className={styles.docInfo}>{cuFormula.doc}</div>
          </div>
        </div>
      </Modal>
      <RateFormulaModal
        ref={RateFormulaModalRef}
        treeData={handleGroupIndexData(_dlData, curEditColumnId.current).map(
          (v) => {
            return {
              ...v,
              key: v.category,
              name: v.category,
              disabled: true,
            };
          },
        )}
        onOk={(str) => {
          replaceText(str);
          form.setFieldsValue({
            dataType: 1,
          });
        }}
      ></RateFormulaModal>
      <TargetFormulaModal
        ref={TargetFormulaModalRef}
        initType={1}
        title="目标值设定"
        leftLabel="来源字段"
        rightLabel="目标值格式"
        leftHead="来源值"
        rightHead="目标值"
        leftPlaceholder={'请输入要映射的来源值\n一行一个来源值\n最多添加200个'}
        rightPlaceholder={'请输入要映射的目标值\n一行一个目标值\n最多添加200个'}
        treeData={TargetFormulaData}
        onOk={(str, type) => {
          replaceText(str);
          form.setFieldsValue({
            dataType: type,
          });
        }}
      ></TargetFormulaModal>
      <TargetFormulaModal
        ref={Target2FormulaModalRef}
        initType={0}
        title="维度值映射"
        leftLabel="来源字段"
        rightLabel="映射值格式"
        leftHead="维度值"
        rightHead="映射值"
        leftPlaceholder={'请输入要映射的维度值\n一行一个维度值\n最多添加200个'}
        rightPlaceholder={'请输入要映射的映射值\n一行一个映射值\n最多添加200个'}
        treeData={TargetFormulaData}
        onOk={(str, type) => {
          replaceText(str);
          form.setFieldsValue({
            dataType: type,
          });
        }}
      ></TargetFormulaModal>
    </span>
  );
});
