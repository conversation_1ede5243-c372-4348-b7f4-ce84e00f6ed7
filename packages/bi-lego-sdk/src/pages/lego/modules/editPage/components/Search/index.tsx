import { Icon, Select, Empty } from '@blmcp/ui';
import React, { useState } from 'react';
// @ts-expect-error
import { ReactComponent as SearchIcon } from './icon/search.svg';
import './style.less';
interface DataItem {
  value: string;
  label: string;
}

interface SearchProps {
  data: Array<DataItem>;
  placeholder?: string;
  onChange: (value: DataItem) => void;
  description: string;
}

export default function ({
  data,
  placeholder = '请选择',
  onChange,
  description,
}: SearchProps) {
  const [searchValue, setSearchValue] = useState<string>();
  const [value, setValue] = useState<string | null>();
  // 保存搜索的值
  const handleSearch = (newValue: string) => {
    setSearchValue(newValue);
  };
  // 选择下拉后清空搜索、选中的值
  const handleChange = (newValue: string) => {
    onChange(data.find((v) => v.value === newValue)!);
    setValue(null);
    setSearchValue('');
  };
  // 搜索配置
  const filterOption = function (keyword: string, item: DataItem) {
    return (
      item.label.includes(keyword) ||
      item.value.includes(keyword.toLocaleUpperCase())
    );
  };

  return (
    <div className="bi-lego-search">
      <Icon component={SearchIcon} />
      <Select
        style={{ width: '100%' }}
        showSearch
        value={value}
        placeholder={placeholder}
        defaultActiveFirstOption={false}
        suffixIcon={null}
        filterOption={filterOption}
        notFoundContent={
          searchValue ? (
            <div className="bi-lego-search-empty">
              <Empty
                imageStyle={{ height: '35px' }}
                description={description}
              ></Empty>
            </div>
          ) : null
        }
        onSearch={handleSearch}
        onChange={handleChange}
        options={searchValue ? data : []}
      />
    </div>
  );
}
