import { Modal, Select, TreeSelect, Form } from '@blmcp/ui';
import { useState, forwardRef, useImperativeHandle, useMemo } from 'react';

const countList = [
  {
    label: '除（/）',
    value: '/',
  },
  {
    label: '乘（*）',
    value: '*',
  },
  {
    label: '加（+）',
    value: '+',
  },
  {
    label: '减（-）',
    value: '-',
  },
];

interface TreeDataItem {
  name: string;
  key: string;
  columnList: TreeDataItem[];
}

interface FormulaModalProps {
  // 下拉树数据
  treeData: TreeDataItem[];
  // 确定回调
  onOk: (str: string) => void;
}

function FormulaModal(props: FormulaModalProps, ref: any) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const { treeData, onOk } = props;

  const fieldsMap = useMemo(() => {
    const map: any = {};
    treeData?.forEach((child) => {
      child?.columnList?.forEach((item) => {
        map[item.key] = item;
      });
    });
    return map;
  }, [treeData]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const leftInfo = fieldsMap[values.left];
      const rightInfo = fieldsMap[values.right];

      let leftValue = `[${leftInfo.name}]`;
      let rightValue = `[${rightInfo.name}]`;
      if (!leftInfo.other.isAggr) {
        leftValue = `SUM([${leftInfo.name}])`;
      }
      if (!rightInfo.other.isAggr) {
        rightValue = `SUM([${rightInfo.name}])`;
      }

      onOk(`${leftValue} ${values.center} ${rightValue}`);
      setIsModalOpen(false);
    });
  };

  useImperativeHandle(ref, () => ({
    open: () => {
      form.resetFields();
      setIsModalOpen(true);
    },
  }));

  return (
    <Modal
      title="率值公式"
      width={560}
      open={isModalOpen}
      closable={true}
      centered={true}
      destroyOnClose
      onCancel={() => setIsModalOpen(false)}
      onOk={handleOk}
    >
      <Form form={form} layout="inline" initialValues={{ center: '/' }}>
        <Form.Item
          name="left"
          rules={[{ required: true, message: '请选择指标' }]}
        >
          <TreeSelect
            showSearch
            treeData={treeData}
            style={{ width: '204px' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="请选择指标"
            treeDefaultExpandAll
            treeNodeFilterProp={'name'}
            popupClassName="TargetFormulaModalTree"
            fieldNames={{
              label: 'name',
              value: 'key',
              children: 'columnList',
            }}
          ></TreeSelect>
        </Form.Item>
        <Form.Item name="center">
          <Select options={countList} style={{ width: '96px' }}></Select>
        </Form.Item>
        <Form.Item
          name="right"
          rules={[{ required: true, message: '请选择指标' }]}
        >
          <TreeSelect
            showSearch
            treeData={treeData}
            style={{ width: '204px' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="请选择指标"
            treeDefaultExpandAll
            treeNodeFilterProp={'name'}
            popupClassName="TargetFormulaModalTree"
            fieldNames={{
              label: 'name',
              value: 'key',
              children: 'columnList',
            }}
          ></TreeSelect>
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default forwardRef(FormulaModal);
