import { IPublicTypeProjectSchema } from '@alilc/lowcode-types';
import { isLegoDev } from '@/utils/common';
import {
  addBrandComponent,
  getCurrentPageId,
  walkTree,
} from '@/pages/lego/utils';
import DefaultPageSchema from './defaultPageSchema.json';
import djDefaultPageSchema from './daijiaDefaultPageSchema.json';
import wycDefaultPageSchema from './wycDefaultPageSchema.json';
import config from '@/tool/config';

const schemaMap = {
  other: DefaultPageSchema,
  djCityFleet: djDefaultPageSchema,
  spCityFleet: wycDefaultPageSchema,
};

const generateProjectSchema = (
  pageSchema: any,
  i18nSchema: any,
): IPublicTypeProjectSchema => {
  const { material } = window.AliLowCodeEngine || {};
  return {
    componentsTree: [JSON.parse(JSON.stringify(pageSchema))],
    componentsMap: material.componentsMap as any,
    version: '2.6.0',
    i18n: i18nSchema,
  };
};

export const getDefaultProjectSchema =
  async (): Promise<IPublicTypeProjectSchema> => {
    const pageSchema =
      schemaMap[config.get('sdkConfig.filterArea')] || schemaMap.spCityFleet;

    const Schema = generateProjectSchema(pageSchema, {});

    if (isLegoDev()) {
      addBrandComponent(Schema);
    }

    const reportId = getCurrentPageId();
    walkTree(Schema.componentsTree, (node) => {
      node.props = node.props || {};
      node.props.uuid = reportId;
    });

    return Schema;
  };
