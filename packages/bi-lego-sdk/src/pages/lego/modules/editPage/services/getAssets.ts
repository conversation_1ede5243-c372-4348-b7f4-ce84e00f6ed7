declare global {
  interface Window {
    mainDomain: string;
    envVar: string;
  }
}
import config from '@/tool/config';
import { cdnPrefix } from '@/utils/common';

function transferCdn(cdn) {
  if (cdn.includes('://')) {
    return cdn;
  } else {
    return cdnPrefix() + cdn;
  }
}

export default function () {
  return {
    packages: [
      {
        package: 'echarts',
        version: '5.5.0',
        library: 'echarts',
        urls: [
          `${cdnPrefix()}/yueyue/npm/umd/blmcpCharts/pre2/250313_0325144323/echarts.min.js`,
        ],
        await: true,
        priority: 2,
      },
      {
        package: '@alifd/layout',
        version: '2.0.7',
        library: 'AlifdLayout',
        // urls: [`http://localhost:3334/view.js`],
        urls: [
          `${cdnPrefix()}/yueyue/npm/umd/leopard-lego-layout/pre2/250814_0813162749/AlifdLayout.js`,
          `${cdnPrefix()}/yueyue/npm/umd/leopard-lego-layout/pre2/250814_0813162749/AlifdLayout.css`,
        ],
        editUrls: [
          `${cdnPrefix()}/yueyue/npm/umd/leopard-lego-layout/pre2/250814_0813162749/view.js`,
          `${cdnPrefix()}/yueyue/npm/umd/leopard-lego-layout/pre2/250814_0813162749/view.css`,
        ],
      },
      {
        package: 'leopard-web-qbi',
        version: '0.1.0',
        library: 'BizComps',
        urls: [
          transferCdn(config.get('sdkConfig.materialJs')),
          transferCdn(config.get('sdkConfig.materialCss')),
        ],
      },
    ],
    components: [
      {
        exportName: 'AlifdLayoutMeta',
        npm: {
          package: '@alifd/layout',
          version: '2.0.7',
        },
        // url: `http://localhost:3334/meta.js`,
        url: `${cdnPrefix()}/yueyue/npm/umd/leopard-lego-layout/pre2/250814_0813162749/meta.js`,
      },
      {
        exportName: 'LeopardWebQbiMeta',
        npm: {
          package: 'leopard-web-qbi',
          version: '0.1.0',
        },
        url: transferCdn(config.get('sdkConfig.materialMeta')),
      },
    ],
    sort: {
      groupList: ['精选组件', '原子组件', '低代码组件'],
      categoryList: [
        '基础元素',
        '布局容器类',
        '表格类',
        '表单详情类',
        '帮助类',
        '对话框类',
        '业务类',
        '通用',
        '引导',
        '信息输入',
        '信息展示',
        '信息反馈',
      ],
    },
    groupList: ['精选组件', '原子组件', '低代码组件'],
    ignoreComponents: {},
  };
}
