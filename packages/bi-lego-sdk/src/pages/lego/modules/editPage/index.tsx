// @ts-nocheck
import React, { useEffect } from 'react';
import { legoInit } from '@blmlc/lego-init';
// import { project, config } from '@alilc/lowcode-engine';

import { clearLocalPageId, getCurrentPageId } from '../../utils';
import { store } from '../../hooks/useComponent';

import icons from '@/pages/lego/libraryMaterials/Icons';
import createAssets from './services/getAssets';
import { editorInit } from './plugins/plugin-editor-init/pluginEditorInit.ts';
import ComponentPanelPlugin from './plugins/plugin-component-panel';
import SaveSamplePlugin from './plugins/plugin-save-sample';
import HeadSamplePlugin from './plugins/plugin-head-sample';
import ComponentIconPlugin from './plugins/plugin-component-icon';
// import HandleCopySchemaPlugin from './plugins/plugin-handle-copy';
import './global.less';
import useSetter from '../../libraryMaterials/setter';
import './components/AddCalculateFields';
import { unRegisterFunction } from './plugins/plugin-head-sample/event';
import './info.css';
import event from '@/tool/event';
import { store as reportStore } from '@/tool/report';
import { cdnPrefix, isLegoDev } from '@/utils/common';
import { filterComponents } from '@/pages/lego/config';
// import ComponentEmpty from '@/pages/lego/components/ComponentEmpty';

function findParent(_node: HTMLElement, className) {
  let node = _node;
  let index = 0;
  if (!_node) return null;
  while (node && !node?.classList?.contains(className) && index < 20) {
    node = node.parentNode;
    index++;
  }
  if (node?.classList.contains(className)) {
    return node;
  }
}

function isInViewport(element, container) {
  const containerRect = container.getBoundingClientRect();
  const elementRect = element.getBoundingClientRect();

  // 检查元素是否在容器的边界内
  return (
    elementRect.top >= containerRect.top &&
    elementRect.left >= containerRect.left &&
    elementRect.bottom <= containerRect.bottom &&
    elementRect.right <= containerRect.right
  );
}

const Lowcode = (props) => {
  const { plugins: lowCodePlugins = [], handleSchema = null } = props;
  const extraEnvironment = [
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/css/antd-umdV5.15.3-440b813236.css`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/js/antd-umdV1.11.10-2bf27bc00d.js`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/js/antdIcons-umdV5.5.1-05b7122c56.js`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/css/blmcpUi-umdV1.1.21-3164c60f39.css`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/js/blmcpUi-umdV1.1.21-b15876bc61.js`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/css/blmBusinessComponents-umdV0.0.68-5bdcc48ea9.css`,
    `${cdnPrefix()}/yueyue/admin/static_umd/release/cp/js/blmBusinessComponents-umdV0.0.68-c443472c19.js`,
  ];
  const registerLegoPlugins = async (plugins: any) => {
    plugins.delete('PreviewSamplePlugin');
    // await plugins.register(HandleCopySchemaPlugin, {}, { override: true });
    await plugins.register(ComponentIconPlugin, {}, { override: true });
    await plugins.register(ComponentPanelPlugin, { icons }, { override: true });
    await plugins.register(HeadSamplePlugin, {}, { override: true });
    await plugins.register(SaveSamplePlugin, {}, { override: true });

    for (let i = 0; i < (lowCodePlugins || []).length; i++) {
      await plugins.register(lowCodePlugins[i], {}, { override: true });
    }
  };

  const initCode = async () => {
    const defaultExtra = [
      {
        type: 'jsText',
        content: 'window.blmRequest=parent.blmRequest;',
      },
    ];

    legoInit({
      dom: 'lce-container',
      projectName: 'legoBI',
      params: {
        deviceClassName: 'lego_deviceClassName',
        realTimeHandleSelectNode(node, { observed, ...ohter } = {}) {
          // 筛选区域不处理
          if (
            filterComponents.includes(node.schema.children?.[0]?.componentName)
          ) {
            return true;
          }
          const iframe = document.getElementById(
            'lego-iframe-simulatorRenderer',
          );
          const legoFilterDom =
            iframe?.contentWindow?.document?.querySelector?.(
              '.legoFilterDefaultRow',
            );
          const clientHeight = legoFilterDom?.clientHeight || 0;
          if (
            observed &&
            observed.top < -(observed.height - clientHeight - 20)
          ) {
            return false;
          }

          // tab 组件内部判断
          const nodeDom = node?.getDOMNode?.();
          const tabDom = findParent(nodeDom, 'xtab-item-wrapper');
          if (tabDom && !isInViewport(nodeDom, tabDom)) {
            return false;
          }
        },
        customizeIgnoreSelectors() {
          return []; // 解决编辑input组件忽略点击问题
        },
        extraEnvironment: ([] as string[]).concat(
          defaultExtra,
          props.getExtraEnvironment?.() || extraEnvironment,
        ),
        filterComponents,
        filterDisDragNum: isLegoDev() ? 4 : 3,
      },
      editorInitConfig: {
        assets: createAssets(),
        initFn: editorInit(handleSchema),
      },
      registerPlugins: registerLegoPlugins,
    });
  };

  useEffect(() => {
    store.clear();

    (async () => {
      useSetter();
      initCode();
      unRegisterFunction();
    })();

    event.dispatch('init');
    const { project, config } = window.AliLowCodeEngine || {};
    // config.set('faultComponent', ComponentEmpty);
    // config.set('notFoundComponent', ComponentEmpty);
    return function () {
      project?.removeDocument(project?.currentDocument as any);
      clearLocalPageId();
      store.clear();
      unRegisterFunction();
    };
  }, []);

  useEffect(() => {
    if (props.customAgreement) {
      reportStore.merge(getCurrentPageId(), {
        customAgreement: props.customAgreement,
      });
    }
  }, [props.customAgreement]);

  useEffect(() => {
    return event.on('common:savePage.init', () => {
      if (props.customAgreement) {
        reportStore.merge(getCurrentPageId(), {
          customAgreement: props.customAgreement,
        });
      }
    });
  }, []);

  return (
    <div id="lce-container" className={`legoReport${getCurrentPageId()}`}></div>
  );
};
export default Lowcode;
