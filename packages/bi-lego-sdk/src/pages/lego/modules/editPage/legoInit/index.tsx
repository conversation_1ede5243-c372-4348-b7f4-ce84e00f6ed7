import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@alilc/lowcode-datasource-fetch-handler';
// import EditorInitPlugin from './plugins/plugin-editor-init';
import UndoRedoPlugin from './plugins/plugin-undo-redo';
import InjectPlugin from '@alilc/lowcode-plugin-inject';
// import ComponentPanelPlugin from './plugins/plugin-component-panel';
// import SettingsPanelPlugin from './plugins/plugin-settings-panel';
// import SaveSamplePlugin from './plugins/plugin-save-sample';
// import PreviewSamplePlugin from './plugins/plugin-preview-sample';
// import CustomSetterSamplePlugin from './plugins/plugin-custom-setter-sample';
// import SetRefPropPlugin from '@alilc/lowcode-plugin-set-ref-prop';
// import HeadSamplePlugin from './plugins/plugin-head-sample';
// import lowcodePlugin from './plugins/plugin-lowcode-component';
// import './global.less';

interface InitProps {
  dom: string;
  projectName: string;
  params: any;
  registerPlugins?: (plugins: any) => void;
}

const registerPlugins = async (
  projectName: string,
  registerPlugins?: Function,
) => {
  const { plugins, editor } = window.AliLowCodeEngine;
  await plugins.register(InjectPlugin);
  // await plugins.register(EditorInitPlugin, {
  //   scenarioName: projectName || 'lego',
  //   displayName: '',
  // });

  // await plugins.register(HeadSamplePlugin);
  // await plugins.register(ComponentPanelPlugin);
  // 注册自定义设置(属性)面板
  // const doSettingsPanelPlugin = SettingsPanelPlugin(editor);
  // await plugins.register(doSettingsPanelPlugin);
  // 注册回退/前进
  await plugins.register(UndoRedoPlugin);
  // await plugins.register(SetRefPropPlugin);
  // await plugins.register(SaveSamplePlugin);
  // await plugins.register(PreviewSamplePlugin);
  // await plugins.register(CustomSetterSamplePlugin);
  // await plugins.register(lowcodePlugin);
  registerPlugins && registerPlugins(plugins, editor);
};

const initCode = async (
  dom: string,
  projectName: string,
  params: Object,
  registerPlugin?: Function,
) => {
  const { init } = window.AliLowCodeEngine;
  await registerPlugins(projectName, registerPlugin);

  init(document.getElementById(dom)!, {
    locale: 'zh-CN',
    enableCondition: true,
    enableCanvasLock: true,
    // 默认绑定变量
    supportVariableGlobally: true,
    disableDefaultSettingPanel: true, // 禁止默认的设置面板
    requestHandlersMap: {
      fetch: createFetchHandler(),
    },
    ...params,
  });
};

export default (props: InitProps) => {
  const { dom = '', projectName = '', params = {}, registerPlugins } = props;
  initCode(dom, projectName, params, registerPlugins);
};
