/**
 *
 * Setter 底层采用formilyjs 作为引擎，通过配置项来渲染表单域。
 *
 */
// import { Input, Select } from '@blmcp/ui';
import {useState, useEffect} from "react";
import { Title, observer, Editor, obx, globalContext, engineConfig, makeObservable } from '@alilc/lowcode-editor-core';
import { SettingsMain } from './main';
import styles from './index.less';
interface SetterPanelProps {
  title: string;
  engineEditor: Editor
}

const SetterPanel = ({ title = '筛选', engineEditor }: SetterPanelProps) => {
  const main = new SettingsMain(engineEditor);

  useEffect(() => {
    const { settings } = main;
    const editor = engineEditor;
    console.log('%c --- self this.main ---', 'background:brown;color:#fff', main);
    console.log('%c --- self editor ---', 'background:blue;color:#fff', editor);
  }, [engineEditor])
  return (
    <div className={styles['setter-wrap']}>
      <div>{title}</div>
      <div className={styles['data-source-setter-wrap']}>
        <div className={styles.panel}>
          <div>维度</div>
        </div>
        <div className={styles.panel}>
          <div>数据集</div>
        </div>
      </div>
    </div>
  );
};

export default SetterPanel;
