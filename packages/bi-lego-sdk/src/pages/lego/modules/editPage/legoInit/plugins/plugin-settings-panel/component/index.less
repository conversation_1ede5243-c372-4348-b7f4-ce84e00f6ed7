.setter-wrap {
  display: block;
}

.data-source-setter-wrap {
  display: flex;
  width: 100%;
  padding: 10px;
  border-width: 1px;
  border-style: solid;
  border-color: #ebeef5;
  z-index: 2;
}
.panel {
  flex: 1;
}
.title-panel {
  height: 38px;
  line-height: 18px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  padding: 10px;
}
.title {
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  color: #303133;
  margin-bottom: 18px;
}
.search-panel {
  margin-top: 10px;
}
.data-source-detail {
  display: block;
  margin-top: 10px;
  overflow: auto;
}
.collapse {
  background: #fff;
  font-size: 12px;
  font-weight: normal;
}
.collapse-header {
  display: flex;
  justify-content: space-between;
}
.dim-item {
  width: 160px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  align-self: stretch;
  border: 1px solid #ebeef5;
  z-index: 3;
  margin-top: 5px;
  margin-bottom: 5px;
  cursor: move;
}
.index-item {
  width: 160px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  align-self: stretch;
  border: 1px solid #ebeef5;
  z-index: 3;
  margin-top: 5px;
  margin-bottom: 5px;
}
:global {
  #rightArea-legoSettingsPane {
    overflow: auto;
  }
}
