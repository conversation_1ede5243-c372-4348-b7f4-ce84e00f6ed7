import React, { Component } from 'react';
import { observer, Editor } from '@alilc/lowcode-editor-core';
import { SettingsMain } from './main';
import { SettingsPane } from './settings-pane';
import { intl } from '../locale/index';
interface ISettingsPrimaryPaneProps {
  title: string;
  engineEditor: Editor;
  config: any;
}

@observer
export class SettingsPrimaryPane extends Component<ISettingsPrimaryPaneProps, { shouldIgnoreRoot: boolean }> {
  private main = new SettingsMain(this.props.engineEditor);

  render() {
    const { settings } = this.main;
    console.log('%c --- self this.main ---', 'background:brown;color:#fff', this.main);
    if (!settings) {
      // 未选中节点，提示选中 或者 显示根节点设置
      return (
        <div className="lc-settings-main">
          <div className="lc-settings-notice">
            <p>{intl('Please select a node in canvas')}</p>
          </div>
        </div>
      );
    }
    const { items } = settings;
    return (
      <div classNmae="testZt" onClick={this.clickEvent}>
        {
          <SettingsPane target={items[0]} key={items[0].id} usePopup={false} />
        }
      </div>
    )
  }
}
