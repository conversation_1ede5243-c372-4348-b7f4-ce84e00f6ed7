// import SetterPanel from '@/pages/lego/libraryMaterials/setter/index';
// import SetterPanel from './component/index';
import { SettingsPrimaryPane } from './component/settings-primary-pane';
import { IPublicModelPluginContext } from '@alilc/lowcode-types';

// 注册属性设置面板
export const SettingsPanelPlugin = (editor: any) => {
  const fun = (ctx: IPublicModelPluginContext) => {
    return {
      async init() {
        const { skeleton, config } = ctx;
        // 注册属性面板
        skeleton.add({
          area: 'rightArea',
          name: 'legoSettingsPane',
          type: 'Panel',
          content: (
            <div>
              <SettingsPrimaryPane
                engineEditor={editor}
              />
            </div>
          ),
          props: {
            align: 'left',
            description: <div>自定义属性面板</div>,
            ignoreRoot: true,
          },
          panelProps: {
            ...(config.get('defaultSettingPanelProps') || {}),
          },
        });
      },
    };
  }
  fun.pluginName = 'SettingsPanelPlugin';

  return fun;
};
export default SettingsPanelPlugin;
