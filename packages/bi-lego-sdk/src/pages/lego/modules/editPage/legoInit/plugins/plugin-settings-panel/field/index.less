@x-gap: 12px;
@y-gap: 8px;

.lc-settings-content > .lc-field:first-child > .lc-field-head {
  border-top: none !important;
}

.lc-field {
  .lc-field-head {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .lc-field-title {
      display: flex;
      align-items: center;
    }
    .lc-field-icon {
      transform-origin: center;
      transition: transform 0.1s;
    }
  }

  &.lc-inline-field {
    display: flex;
    align-items: center;
    margin: 12px;

    > .lc-field-head {
      width: 70px;
      margin-right: 1px;
      .lc-title-label {
        width: 70px;
        word-break: break-all;
      }
    }
    > .lc-field-body {
      flex: 1;
      min-width: 0;
      display: flex;
      align-items: center;
    }
  }

  &.lc-block-field, &.lc-accordion-field, &.lc-entry-field {
    display: block;

    > .lc-field-head {
      height: 32px;
      display: flex;
      align-items: center;
      font-weight: 500;
      background: var(--color-block-background-shallow, rgba(31,56,88,.06));
      border-top: 1px solid var(--color-line-normal,rgba(31,56,88,.1));
      border-bottom: 1px solid var(--color-line-normal,rgba(31,56,88,.1));
      color: var(--color-title);
      padding: 0 12px;
      user-select: none;

      > .lc-field-icon {
        color: var(--color-icon-normal, #8f9bb3);
      }
    }

    > .lc-field-body {
      padding: 12px;

      .lc-inline-field {
        margin: 12px 0;

        &:first-child {
          margin-top: 0;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  &.lc-entry-field {
    margin-bottom: 6px;

    > .lc-field-head {
      cursor: pointer;
    }
  }

  .lc-setter-actions {
    display: flex;
    align-items: center;
  }

  &.lc-block-field {
    position: relative;
    >.lc-field-body>.lc-block-setter>.lc-setter-actions {
      position: absolute;
      right: 10px;
      top: 0;
      height: 32px;
    }
  }

  &.lc-accordion-field {
    position: relative;

    > .lc-field-head {
      cursor: pointer;
    }

    &.lc-field-is-collapsed {
      margin-bottom: 6px;
    }

    &.lc-field-is-collapsed {
      > .lc-field-head .lc-field-icon {
        transform: rotate(180deg);
      }
      > .lc-field-body {
        display: none;
      }
    }
  }
}
