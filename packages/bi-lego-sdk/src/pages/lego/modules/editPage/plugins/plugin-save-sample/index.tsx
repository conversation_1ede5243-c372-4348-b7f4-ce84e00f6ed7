import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { Button, Modal } from '@blmcp/ui';
import './index.less';
import { useState, useEffect } from 'react';
import { publishReport } from '@/pages/lego/api';
import {
  getCurrentPageId,
  LegoReportIdKey,
  walkerComponents,
  getProjectSchema,
} from '@/pages/lego/utils';
import { copyTemplateClick } from '@/pages/lego/utils/common';
import { isLegoDev, routerBase } from '@/utils/common';

import {
  sceneActionStartMonitor,
  sceneActionEndMonitor,
} from '@/utils/eventTracking';

// 保存功能示例
const SaveSamplePlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { skeleton, project, event, config } = ctx;

      const Publish = function () {
        const [disable, setDisable] = useState(true);
        const [loading, setloading] = useState(false);
        const listenInCommonChange = function () {
          walkerComponents(getProjectSchema(), (i, o, k) => {
            if (k[i.componentName].componentType === 4) return true;
            return false;
          })
            .then(() => {
              setDisable(true);
            })
            .catch(() => {
              setDisable(false);
            });
        };

        useEffect(() => {
          const unUpdatePage = event.on(
            'common:updatePage',
            listenInCommonChange,
          );
          const unsavePageInit = event.on('common:savePage.init', () => {
            listenInCommonChange();
            project?.currentDocument?.history.onChangeState(
              listenInCommonChange,
            );
          });
          return function () {
            unUpdatePage();
            unsavePageInit();
          };
        }, []);

        const doPublish = (num = 1) => {
          event.emit('savePage.release');
          setloading(true);
          const _db = function () {
            publishReport({
              reportId: getCurrentPageId(),
              id: getCurrentPageId(),
            })
              .then(() => {
                const storeKey = 'lego-bi-update-reportName';
                window.localStorage.setItem(storeKey, Date.now().toString());
                window.localStorage.removeItem(storeKey);
                const href = `/${routerBase()}/legoBI/view?reportId=${getCurrentPageId()}&publish=1`;
                location.href = href;
                sessionStorage[LegoReportIdKey] = '';
                sceneActionEndMonitor({
                  sceneId: 'legoBI-savePage-warn',
                  uniqueId: 0,
                  maxTime: 6000 * 3,
                });
              })
              .finally(() => {
                setloading(false);
              });
          };
          if (
            (num < 100 && config.get('isSavePage')) ||
            // @ts-expect-error
            (window.top.proxy || window.top).__lego_checkTextSafe
          ) {
            setTimeout(() => {
              doPublish(num++);
            }, 100);
          } else {
            _db();
          }
        };

        return (
          <Button
            className="publish"
            id="publishId"
            type="primary"
            loading={loading}
            disabled={disable}
            onClick={() => {
              sceneActionStartMonitor({
                sceneId: 'legoBI-savePage-warn',
                uniqueId: 0,
                maxTime: 6000 * 3,
              });
              // 哈勃环境并且是模板的发布需要二次确认
              if (isLegoDev() && config.get('isTemplate')) {
                Modal.confirm({
                  title: '确认操作',
                  content:
                    '模板发布之后，将不可再次编辑，且新增的计算字段将转化为公共字段，是否继续发布？',
                  okText: '确定',
                  cancelText: '取消',
                  onOk: () => {
                    doPublish();
                  },
                  onCancel: () => {},
                });
              } else {
                setTimeout(() => {
                  doPublish();
                }, 200);
              }
            }}
          >
            发布
          </Button>
        );
      };

      const cancelEdit = skeleton.add({
        name: 'cancelEdit',
        area: 'topArea',
        type: 'Widget',
        props: {
          align: 'right',
        },
        content: (
          <Button
            className="cancelEdit"
            onClick={() => {
              sessionStorage[LegoReportIdKey] = '';
              location.href = `/${routerBase()}/legoBI/list?listKey=${
                isLegoDev() ? (config.get('isTemplate') ? 4 : 5) : 1
              }`;
            }}
          >
            取消编辑
          </Button>
        ),
      });

      if (isLegoDev()) {
        skeleton.add({
          name: 'copyTemplate',
          area: 'topArea',
          type: 'Widget',
          props: {
            align: 'right',
          },
          content: (
            <Button
              id="legoCopyTemplateButton"
              onClick={() => {
                copyTemplateClick(
                  { id: getCurrentPageId() },
                  2,
                  config.get('isTemplate') ? '模板' : '报告',
                );
              }}
            ></Button>
          ),
        });
      }

      skeleton.add({
        name: 'publish',
        area: 'topArea',
        type: 'Widget',
        props: {
          align: 'right',
        },
        content: <Publish />,
      });
      cancelEdit?.disable?.();
      project.onSimulatorRendererReady(() => {
        cancelEdit?.enable?.();
      });
    },
  };
};
SaveSamplePlugin.pluginName = 'SaveSamplePlugin';
SaveSamplePlugin.meta = {
  dependencies: ['EditorInitPlugin'],
};
export default SaveSamplePlugin;
