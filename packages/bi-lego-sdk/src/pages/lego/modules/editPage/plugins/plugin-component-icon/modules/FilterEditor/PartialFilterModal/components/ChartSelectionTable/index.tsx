import { useState, useCallback, useEffect } from 'react';
import { Table, Select, Tooltip, Spin, BLMIconFont } from '@blmcp/ui';
import { queryDataSourceDetail } from '@/pages/lego/api/filterEditor';
import { ReactComponent as TimeDateTypeIcon } from '@/assets/lego/timeDateType.svg';
import { ReactComponent as NumDateTypeIcon } from '@/assets/lego/numDateType.svg';
import { ReactComponent as TextDateTypeIcon } from '@/assets/lego/textDateType.svg';
import type { ColumnsType } from '@blmcp/ui';

import './index.less';

interface ChartData {
  id: string;
  title: string;
  componentName: string;
  dataSource: string;
  dataSourceId: number;
}

interface FieldOption {
  value: string;
  label: string;
  dataType: number;
  isAggr: number;
  title: string;
  key: string;
  columnId: any;
  biType: any;
}

interface DataSourceCache {
  [dataSourceId: number]: {
    dimensionList: FieldOption[];
    measureList: FieldOption[];
  };
}

interface ChartSelectionTableProps {
  conditionType:
    | 'DatePickerFilter'
    | 'RangeOfIntervalsFilter'
    | 'InputFilter'
    | 'ListFilter'
    | null;
  selectedCharts: string[];
  selectedFields: Record<string, string>;
  onSelectionChange: (
    selectedCharts: string[],
    selectedFields: Record<string, string>,
    selectedChartsItem: any[],
    selectedFieldsInfo: Record<
      string,
      {
        title: string;
        key: string;
        dataType: number;
        isAggr: number;
        columnId: any;
        biType: any;
      }
    >,
  ) => void;
  chartComList: any[];
}

const ChartSelectionTable = ({
  conditionType,
  selectedCharts,
  selectedFields,
  onSelectionChange,
  chartComList,
}: ChartSelectionTableProps) => {
  // 数据源缓存
  const [dataSourceCache, setDataSourceCache] = useState<DataSourceCache>({});
  // 加载状态
  const [loadingDataSources, setLoadingDataSources] = useState<Set<number>>(
    new Set(),
  );

  // 获取数据源详情
  const fetchDataSourceDetail = useCallback(
    async (dataSourceId: number) => {
      if (
        dataSourceCache[dataSourceId] ||
        loadingDataSources.has(dataSourceId)
      ) {
        return;
      }

      setLoadingDataSources((prev) => new Set(prev).add(dataSourceId));

      try {
        const response = await queryDataSourceDetail({ dataSourceId });
        if (response?.code === 1 && response?.data) {
          const { dimensionList = [], measureList = [] } = response.data;

          // 处理维度数据
          const dimensionOptions: FieldOption[] = dimensionList.map(
            (item: any) => ({
              value: item.columnId,
              label: item.title,
              dataType: item.dataType,
              isAggr: item.isAggr,
              columnId: item.columnId,
              biType: item.biType,
              title: item.title,
              key: item.key,
            }),
          );

          // 处理指标数据 - 需要遍历所有层级的 columnList
          const measureOptions: FieldOption[] = [];
          measureList.forEach((group: any) => {
            if (group.columnList && Array.isArray(group.columnList)) {
              group.columnList.forEach((item: any) => {
                measureOptions.push({
                  value: item.columnId,
                  label: item.title,
                  dataType: item.dataType || 1, // 指标默认为数字类型
                  isAggr: item.isAggr,
                  columnId: item.columnId,
                  biType: item.biType,
                  title: item.title,
                  key: item.key,
                });
              });
            }
          });

          // 合并所有字段选项
          const allOptions = {
            dimensionList: dimensionOptions,
            measureList: measureOptions,
          };

          setDataSourceCache((prev) => ({
            ...prev,
            [dataSourceId]: allOptions,
          }));
        }
      } catch (error) {
        console.error('获取数据源详情失败:', error);
      } finally {
        setLoadingDataSources((prev) => {
          const newSet = new Set(prev);
          newSet.delete(dataSourceId);
          return newSet;
        });
      }
    },
    [dataSourceCache, loadingDataSources],
  );

  // 预加载回填数据需要的数据源详情
  useEffect(() => {
    if (selectedCharts.length > 0 && chartComList.length > 0) {
      // 获取所有选中图表对应的数据源ID（去重）
      const dataSourceIds = new Set<number>();
      selectedCharts.forEach((chartId) => {
        const chart = chartComList.find((c) => c.id === chartId);
        if (chart?.dataSourceId) {
          dataSourceIds.add(chart.dataSourceId);
        }
      });

      // 批量获取数据源详情
      dataSourceIds.forEach((dataSourceId) => {
        fetchDataSourceDetail(dataSourceId);
      });
    }
  }, [selectedCharts, chartComList, fetchDataSourceDetail]);

  // 辅助函数：根据选中的图表和字段生成完整的数据
  const generateCompleteData = useCallback(
    (
      newSelectedCharts: string[],
      newSelectedFields: Record<string, string>,
    ) => {
      // 生成选中图表的完整信息
      const selectedChartsItem = newSelectedCharts
        .map((chartId) => chartComList.find((chart) => chart.id === chartId))
        .filter(Boolean);

      // 生成选中字段的完整信息
      const selectedFieldsInfo: Record<
        string,
        {
          title: string;
          key: string;
          dataType: number;
          isAggr: number;
          columnId: any;
          biType: any;
        }
      > = {};

      Object.entries(newSelectedFields).forEach(([chartId, fieldValue]) => {
        const chart = chartComList.find((c) => c.id === chartId);
        if (chart?.dataSourceId && dataSourceCache[chart.dataSourceId]) {
          const allFields = [
            ...dataSourceCache[chart.dataSourceId].dimensionList,
            ...dataSourceCache[chart.dataSourceId].measureList,
          ];
          const fieldInfo = allFields.find(
            (field) => field.value === fieldValue,
          );
          if (fieldInfo) {
            selectedFieldsInfo[chartId] = {
              title: fieldInfo?.title,
              dataType: fieldInfo?.dataType,
              isAggr: fieldInfo?.isAggr,
              columnId: fieldInfo?.columnId,
              key: fieldInfo?.key,
              biType: fieldInfo?.biType,
            };
          }
        }
      });

      return { selectedChartsItem, selectedFieldsInfo };
    },
    [chartComList, dataSourceCache],
  );

  // 根据数据类型获取图标
  const getDataTypeIcon = useCallback((dataType: number) => {
    switch (dataType) {
      case 0: // 文本
        return <TextDateTypeIcon style={{ marginRight: 8 }} />;
      case 1: // 数字
        return <NumDateTypeIcon style={{ marginRight: 8 }} />;
      case 2: // 时间
        return <TimeDateTypeIcon style={{ marginRight: 8 }} />;
    }
  }, []);

  // 处理单个图表选择
  const handleChartSelect = useCallback(
    (chartId: string, checked: boolean) => {
      let newSelectedCharts: string[];
      let newSelectedFields = { ...selectedFields };

      if (checked) {
        newSelectedCharts = [...selectedCharts, chartId];

        // 获取对应的数据源ID并加载数据
        const chart = chartComList.find((item) => item.id === chartId);
        if (chart?.dataSourceId) {
          fetchDataSourceDetail(chart.dataSourceId);
        }
      } else {
        newSelectedCharts = selectedCharts.filter((id) => id !== chartId);
        // 移除对应的字段选择
        delete newSelectedFields[chartId];
      }

      const { selectedChartsItem, selectedFieldsInfo } = generateCompleteData(
        newSelectedCharts,
        newSelectedFields,
      );
      onSelectionChange(
        newSelectedCharts,
        newSelectedFields,
        selectedChartsItem,
        selectedFieldsInfo,
      );
    },
    [
      selectedCharts,
      selectedFields,
      onSelectionChange,
      chartComList,
      fetchDataSourceDetail,
      generateCompleteData,
    ],
  );

  // 根据筛选器类型过滤字段选项
  const getFilteredFieldOptions = useCallback(
    (dataSourceId: number, conditionType: string | null) => {
      const allOptions = dataSourceCache[dataSourceId] || [];

      if (!conditionType) return [];
      // 维度：dimensionList， 指标：measureList
      switch (conditionType) {
        case 'DatePickerFilter': // 时间筛选器只能选时间字段
          return allOptions?.dimensionList?.filter(
            (option) => option.dataType === 2,
          );
        case 'RangeOfIntervalsFilter': // 区间筛选器只能展示非聚合指标字段
          return allOptions?.measureList?.filter((option) => !option.isAggr);
        case 'InputFilter': // 文本筛选器只能展示非时间维度字段
          return allOptions?.dimensionList?.filter(
            (option) => option.dataType === 0,
          );
        case 'ListFilter': // 列表筛选器也只能展示非时间维度字段
          return allOptions?.dimensionList?.filter(
            (option) => option.dataType !== 2,
          );
      }
    },
    [dataSourceCache],
  );

  // 处理字段选择
  const handleFieldSelect = useCallback(
    (chartId: string, fieldValue: string) => {
      const newSelectedFields = {
        ...selectedFields,
        [chartId]: fieldValue,
      };
      const { selectedChartsItem, selectedFieldsInfo } = generateCompleteData(
        selectedCharts,
        newSelectedFields,
      );
      onSelectionChange(
        selectedCharts,
        newSelectedFields,
        selectedChartsItem,
        selectedFieldsInfo,
      );
    },
    [selectedCharts, selectedFields, onSelectionChange, generateCompleteData],
  );

  const columns: ColumnsType<ChartData> = [
    {
      title: '名称',
      dataIndex: 'title',
      key: 'title',
      minWidth: 200,
      ellipsis: true,
    },
    {
      title: '数据集',
      dataIndex: 'dataSourceName',
      key: 'dataSourceName',
      minWidth: 200,
      ellipsis: true,
    },
    {
      title: '配置项',
      key: 'field',
      render: (_, record: ChartData) => {
        const isSelected = selectedCharts.includes(record.id);
        const isLoading = loadingDataSources.has(record.dataSourceId);

        const filteredOptions =
          getFilteredFieldOptions(record.dataSourceId, conditionType) || [];

        // 为选项添加图标
        const optionsWithIcons = filteredOptions?.map((option) => ({
          value: option.value,
          label: (
            <div
              title={option.label}
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {getDataTypeIcon(option.dataType)}
              <span
                style={{
                  flex: 1,
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}
              >
                {option.label}
              </span>
            </div>
          ),
          searchLabel: option.label,
        }));

        return (
          <Select
            placeholder="请选择"
            showSearch
            optionFilterProp="searchLabel"
            value={selectedFields[record.id]}
            onChange={(value: string) => handleFieldSelect(record.id, value)}
            disabled={!isSelected}
            loading={isLoading}
            style={{ width: 268 }}
            options={optionsWithIcons}
            notFoundContent={isLoading ? <Spin size="small" /> : '暂无数据'}
            virtual={false}
            getPopupContainer={(triggerNode: any) => triggerNode.parentElement}
          />
        );
      },
      width: 300,
    },
  ];

  // 配置 rowSelection
  const rowSelection = {
    type: 'checkbox' as const,
    selectedRowKeys: selectedCharts,
    onSelect: (record: ChartData, selected: boolean) => {
      handleChartSelect(record.id, selected);
    },
    onSelectAll: (selected: boolean) => {
      if (selected) {
        // 全选
        const allChartIds = chartComList.map((chart) => chart.id);
        const { selectedChartsItem, selectedFieldsInfo } = generateCompleteData(
          allChartIds,
          selectedFields,
        );
        onSelectionChange(
          allChartIds,
          selectedFields,
          selectedChartsItem,
          selectedFieldsInfo,
        );
      } else {
        // 取消全选
        const { selectedChartsItem, selectedFieldsInfo } = generateCompleteData(
          [],
          {},
        );
        onSelectionChange([], {}, selectedChartsItem, selectedFieldsInfo);
      }
    },
    getCheckboxProps: () => ({
      disabled: !conditionType,
    }),
    renderCell: (
      _checked: boolean,
      _record: ChartData,
      _index: number,
      originNode: React.ReactNode,
    ) => {
      if (!conditionType) {
        return <Tooltip title="请先选择筛选器类型">{originNode}</Tooltip>;
      }
      return originNode;
    },
  };

  return (
    <div className="chart-selection-table">
      <div className="table-header">
        <div className="selection-actions"></div>
      </div>

      <Table
        columns={columns}
        dataSource={chartComList}
        rowKey="id"
        pagination={false}
        size="small"
        className="chart-table"
        tableLayout="auto"
        rowSelection={rowSelection}
      />
    </div>
  );
};

export default ChartSelectionTable;
