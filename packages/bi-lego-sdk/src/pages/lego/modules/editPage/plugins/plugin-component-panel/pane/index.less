.lowcode-component-panel {
  height: 100%;
  border-top: 1px solid transparent;
  display: flex;
  flex-direction: row;
  align-items: center;

  >.header {
    margin: 12px 16px;
    flex-shrink: 0;
    flex-grow: 0;

    .search {
      width: 100%;
    }
  }

  >.tabs {
    flex: 1;
    overflow: hidden;
  }

  >.empty {
    display: flex;
    flex: auto;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;

    img {
      width: 100px;
      height: 100px;
    }

    .content {
      line-height: 2;
    }
  }

  >.filtered-content {
    overflow-y: overlay;
    overflow-x: hidden;
  }

  .lego-materiel-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
      .lego-materiel-title {
        background: rgba(37, 52, 79, 0.05);
        border-color: transparent;
        border-radius: 6px;
      }
    }

    .lego-materiel-title {
      padding: 5px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .name {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: normal;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;

        .next-icon-arrow-down {
          font-size: 8px;
          margin-left: 4px;
        }
      }

      .icon {
        width: 16px;
        height: 16px;
        margin: 0 1px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
        }
      }
    }

    .lego-materiel-content {
      position: absolute;
      top: 36px;
      left: 50%;
      z-index: 100;
      translate: -50% 0;
      background: transparent;
      padding-top: 12px;

      .contentBox {
        background: #ffffff;
        border-radius: 6px;
        box-shadow: 0px 0px 16px 0 rgba(0, 0, 0, 0.15);
        padding: 5px;
        max-height: calc(100vh - 100px);
        overflow-y: auto;
        display: grid;

        &.columns-2 {
          grid-template-columns: 94px 94px;
        }

        .snippet {
          padding: 10px 10px 10px 5px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-direction: row;

          .name {
            width: auto;
            font-family: PingFang SC;
            font-size: 13px;
            font-weight: normal;
            line-height: 20px;
            letter-spacing: 0em;
            color: rgba(0, 0, 0, 0.9);
            margin-left: 5px;
          }
        }

        // &::before {
        //   content: '';
        //   width: 0;
        //   height: 0;
        //   position: absolute;
        //   top: -12px;
        //   left: 50%;
        //   translate: -50% 0;
        //   border: 6px solid #000;
        //   border-style: solid dashed dashed;
        //   /* transparent表示透明，第一个代表上三角，第二个代表左右三角，第三个代表下三角 */
        //   border-color: transparent transparent #fff;
        // }
      }
    }

    .card {
      cursor: pointer;
      padding: 0;
    }
  }

  .lego-materiel-box-single {
    cursor: pointer;

    .snippet {
      padding: 5px 10px;
      height: 100%;
    }
  }

  .snippet {
    .name {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: rgba(0, 0, 0, 0.9);
    }

    .icon {
      width: 16px;
      height: 16px;
      margin: 0 1px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
      }
    }
  }
}
