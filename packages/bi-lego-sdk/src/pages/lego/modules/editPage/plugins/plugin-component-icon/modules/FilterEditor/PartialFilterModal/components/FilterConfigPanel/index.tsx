import { useCallback } from 'react';
import { Radio, Alert } from '@blmcp/ui';
import { FilterCondition } from '../../index';
import ChartSelectionTable from '../ChartSelectionTable';
import './index.less';

interface FilterConfigPanelProps {
  condition?: FilterCondition;
  onUpdateCondition: (id: string, updates: Partial<FilterCondition>) => void;
  chartComList: any;
}

const FilterConfigPanel = ({
  condition,
  onUpdateCondition,
  chartComList,
}: FilterConfigPanelProps) => {
  // 处理筛选器类型变化
  const handleTypeChange = useCallback(
    (e: any) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          componentName: e.target.value,
          // 切换类型时清空已选择的图表和字段
          selectedCharts: [],
          selectedFields: {},
          selectedChartsItem: [],
          selectedFieldsInfo: {},
        });
      }
    },
    [condition, onUpdateCondition],
  );

  // 处理图表选择变化
  const handleChartSelectionChange = useCallback(
    (
      selectedCharts: string[],
      selectedFields: Record<string, string>,
      selectedChartsItem: any[],
      selectedFieldsInfo: Record<
        string,
        {
          title: string;
          key: string;
          dataType: number;
          isAggr: number;
          columnId: any;
          biType: any;
        }
      >,
    ) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          selectedCharts,
          selectedFields,
          selectedChartsItem,
          selectedFieldsInfo,
        });
      }
    },
    [condition, onUpdateCondition],
  );

  return condition ? (
    <div className="filter-config-panel-context">
      <div className="config-section">
        <div className="section-title">筛选器类型</div>
        <Radio.Group
          value={condition?.componentName}
          onChange={handleTypeChange}
          className="filter-type-radio"
        >
          <Radio value="DatePickerFilter">时间筛选器</Radio>
          <Radio value="RangeOfIntervalsFilter">区间筛选器</Radio>
          <Radio value="InputFilter">文本筛选器</Radio>
          <Radio value="ListFilter">列表筛选器</Radio>
        </Radio.Group>
      </div>

      <div className="config-section-table">
        <div className="section-title">关联图表及字段</div>
        <ChartSelectionTable
          conditionType={condition?.componentName}
          selectedCharts={condition.selectedCharts}
          selectedFields={condition.selectedFields}
          onSelectionChange={handleChartSelectionChange}
          chartComList={chartComList}
        />
      </div>
    </div>
  ) : (
    <div className="filter-config-panel-context">
      <div className="empty-config">
        <div className="empty-config-png"></div>
        <div className="empty-config-text">暂无数据</div>
      </div>
    </div>
  );
};

export default FilterConfigPanel;
