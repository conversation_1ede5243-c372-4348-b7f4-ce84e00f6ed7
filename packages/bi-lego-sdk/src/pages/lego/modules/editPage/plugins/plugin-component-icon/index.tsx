import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import ExportData from './modules/ExportData';
import FilterEditor from './modules/FilterEditor';
import { Tooltip } from '@blmcp/ui';

const funcs = [
  {
    name: '导出数据',
    Component: ExportData,
    noWrapper: true,
  },
  {
    name: '配置',
    Component: FilterEditor,
    init: FilterEditor.init,
  },
];
const createComponent = (option) => (props) => {
  return (
    <>
      <Tooltip title={option.name} overlayClassName="createComponent-tips">
        <span></span>
        <option.Component {...props} />
      </Tooltip>
    </>
  );
};
// 保存功能示例
const DiyComponentIconPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { config } = ctx;
      const diyActions = config.get('diyActions') ?? [];
      funcs.forEach((m) => {
        diyActions.push(createComponent(m));
        if (m.init) {
          m.init(ctx);
        }
      });
      config.set('diyActions', diyActions);
    },
  };
};
DiyComponentIconPlugin.pluginName = 'DiyComponentIconPlugin';
export default DiyComponentIconPlugin;
