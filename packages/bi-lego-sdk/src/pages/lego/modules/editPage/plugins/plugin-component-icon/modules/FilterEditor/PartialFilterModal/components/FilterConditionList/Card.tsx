import { useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import type { XYCoord } from 'dnd-core';
import type { FC } from 'react';

const dragType = 'TabSettingType';
interface CardProps {
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  dragType?: string;
  deleteItem: () => void;
  inputChange: (value: string) => void;
  editTipsItem: (index: number, value: string) => void;
}
interface DragItem {
  index: number;
  type: string;
}
export const Card: FC<CardProps> = (props) => {
  const { index, moveCard } = props;
  const ref = useRef<HTMLDivElement>(null);

  const [{}, drop] = useDrop<DragItem, void, Record<string, unknown>>({
    accept: dragType,
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: dragType,
    item: { index },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drop(ref);
  const opacity = isDragging ? 0 : 1;

  return (
    <div ref={preview}>
      <div ref={ref} style={{ opacity }}>
        <div ref={drag}>{props.children}</div>
      </div>
    </div>
  );
};
