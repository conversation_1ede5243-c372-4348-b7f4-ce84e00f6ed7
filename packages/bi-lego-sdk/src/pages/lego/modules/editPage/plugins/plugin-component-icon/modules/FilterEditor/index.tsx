import { EditOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { getProjectSchema, walkerComponents } from '@/pages/lego/utils';
import { queryDataSourceList } from '@/pages/lego/api/filterEditor';
import PartialFilterModal from './PartialFilterModal';
import { store as componentStore } from '@/pages/lego/hooks/useComponent';

interface FilterItemProps {
  id?: string;
  componentName: string;
  props: any;
}

export const getSetting = function (node: any): FilterItemProps[] {
  const schema = node.schema;
  const setting: any = [];
  const rowSchema = schema.children?.[0] || {};
  rowSchema.children?.forEach((v) => {
    const child = v.children?.[0];
    const filterLinkComponents = child.props?.filterLinkComponents || [];
    if (child && child.componentName !== 'SearchButton') {
      setting.push({
        id: child.id,
        componentName: child.componentName,
        props: {
          ...(child.props || {}),
          filterLinkComponents: filterLinkComponents.filter(
            (f) =>
              f.datasetId === componentStore.get(f.componentId)?.dataSourceId,
          ),
        },
      });
    }
  });
  return setting;
};

const setSetting = function (node: any, items: FilterItemProps[]) {
  const uuid = node.getPropValue('uuid');
  const reportId = node.getPropValue('reportId');
  const links = items.reduce((acc: any, cur) => {
    acc.push(
      ...cur.props.filterLinkComponents.map((v) => ({
        componentId: v.componentId,
        datasetId: v.datasetId,
      })),
    );
    return acc;
  }, []);
  const rowNode = node._children?.children?.[0];
  const schema = rowNode.schema;
  schema.props = {
    uuid,
    reportId,
    isPartialContainerFilter: true,
    className: 'PartialContainerFilter-row',
  };
  schema.children = items
    .map((item) => {
      return {
        componentName: 'FDCell',
        props: {
          uuid,
          reportId,
        },
        children: [
          {
            id: item.id,
            componentName: item.componentName,
            props: {
              uuid,
              reportId,
              partialContainerFilterId: node.id,
              ...item.props,
            },
          },
        ],
      };
    })
    .concat([
      {
        componentName: 'FDCell',
        props: {
          uuid,
          reportId,
          // @ts-expect-error
          isDefaultFilter: true,
        },
        children: [
          {
            id: undefined,
            componentName: 'SearchButton',
            props: {
              uuid,
              reportId,
              partialContainerFilterId: node.id,
              isDefaultFilter: true,
              filterLinkComponents: links,
            },
          },
        ],
      },
    ]);

  rowNode.replaceWith(schema);
};

export default function FilterEditor(props) {
  // 条件设置弹窗状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 图表数据list
  const [chartComList, setChartComList] = useState([]);
  // 弹窗回填内容
  const [initialConditions, setInitialConditions] = useState([]);

  // 当前组件节点
  const node = props.node?._children?.children?.[0];

  if (!node || node.componentName !== 'PartialContainerFilter') return null;

  // 组件配置项
  // const componentProps = node.schema.props || {};

  // 将 filterList 转换为 FilterCondition 格式用于回填
  const convertToFilterConditions = (
    filterItems: FilterItemProps[],
    chartList: any[] = [],
  ) => {
    return filterItems.map((item) => {
      // 当前选中图表和维度
      const filterLinkComponents = item.props?.filterLinkComponents || [];
      // 当前选中的图表id
      const selectedCharts = filterLinkComponents.map(
        (link: any) => link.componentId,
      );

      // 根据 selectedCharts 从 chartList(所有图表数据源) 中找到对应的图表信息
      const selectedChartsItem = selectedCharts
        .map((chartId: string) =>
          chartList.find((chart) => chart.id === chartId),
        )
        .filter(Boolean);

      // 从 filterLinkComponents 中提取字段信息
      const selectedFields: Record<string, string> = {};
      const selectedFieldsInfo: Record<string, any> = {};
      filterLinkComponents.forEach((link: any) => {
        if (link?.componentId && link?.key) {
          selectedFields[link?.componentId] = link?.columnId;
          selectedFieldsInfo[link?.componentId] = {
            title: link?.title || link?.key,
            key: link?.key,
            dataType: link?.dataType || 0,
            isAggr: link?.isAggr || 0,
            columnId: link?.columnId,
            biType: link?.biType,
          };
        }
      });

      return {
        id:
          item.id ||
          Date.now().toString(36) + Math.random().toString(36).substring(2),
        title: item.props?.title || '查询条件',
        componentName: item.componentName as
          | 'DatePickerFilter'
          | 'RangeOfIntervalsFilter'
          | 'InputFilter'
          | 'ListFilter'
          | null,
        selectedCharts,
        selectedFields,
        selectedChartsItem,
        selectedFieldsInfo,
        isValid: true,
        errorMessage: [],
        otherProps: item.props,
      };
    });
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  // 当前页面图表list
  const handleGetChartComList = () => {
    // 筛选器列表, 已有的条件需要回填
    const filterList = getSetting(node);
    let chartArr: any[] = [];
    queryDataSourceList().then((res) => {
      if (res?.code === 1) {
        walkerComponents(getProjectSchema(), (item, meta, metaMap) => {
          // 数据集是否设置完毕
          if (meta?.compliance) {
            const dataSourceName = res.data.find(
              (v: any) => v.id === item?.props?.dataSetConfig?.dataSourceId,
            )?.name;
            chartArr.push({
              ...meta,
              dataSourceId: item?.props?.dataSetConfig?.dataSourceId,
              dataSourceName,
              id: item.id,
            });
          }
        });
        setChartComList(chartArr);
        // 当 chartComList 加载完成后，更新 initialConditions，回填图表信息
        setInitialConditions(convertToFilterConditions(filterList, chartArr));
      }
    });
  };
  const handleOpenModal = () => {
    setIsModalOpen(true);
    handleGetChartComList();
  };
  // 局部筛选器保存函数
  const onSave = (conditions: any) => {
    // 筛选器列表, 已有的条件需要回填
    // const filterList = getSetting(node);
    const newFilters = conditions.map((item: any) => ({
      componentName: item?.componentName,
      props: {
        ...(item.otherProps || {}),
        title: item.title,
        filterLinkComponents: item.selectedChartsItem?.map(
          (selectItem: any) => ({
            componentId: selectItem?.id,
            bindingChartIds: [selectItem?.elementId],
            datasetId: selectItem?.dataSourceId,
            ...item.selectedFieldsInfo[selectItem?.id],
            isAggr: item.selectedFieldsInfo[selectItem?.id]?.isAggr ? 1 : 0,
          }),
        ),
      },
    }));

    setSetting(node, [...newFilters]);
  };

  return (
    <div className="lc-borders-action">
      <EditOutlined
        onClick={() => {
          handleOpenModal();
        }}
      />
      <PartialFilterModal
        isModalOpen={isModalOpen}
        handleCloseModal={handleCloseModal}
        onSave={onSave}
        chartComList={chartComList}
        initialData={initialConditions}
      ></PartialFilterModal>
    </div>
  );
}

FilterEditor.init = function (ctx: any) {
  const { config: engineConfig } = window.AliLowCodeEngine || {};
  engineConfig.set('handleCopyNode', (node) => {
    const cuNode = node._children?.children?.[0];
    const schema = cuNode.schema;
    if (cuNode?.componentName === 'PartialContainerFilter') {
      walkerComponents(schema, (component) => {
        component.props = {
          ...(component.props || {}),
          partialContainerFilterId: cuNode.id,
        };
      });
    }
    cuNode.replaceWith(schema);
  });
};
