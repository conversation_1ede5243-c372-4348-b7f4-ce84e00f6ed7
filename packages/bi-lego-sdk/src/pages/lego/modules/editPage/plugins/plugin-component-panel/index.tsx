// @ts-nocheck
import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import './index.less';
import { useMemo } from 'react';
import ComponentsPane from './pane';

const ComponentList = (props) => {
  const Components = useMemo(
    () => window.LeopardWebQbiMeta.components.map((v) => v.componentName),
    [],
  );

  // Components.push('NextText')

  const componentsPane = useMemo(() => {
    return (
      <ComponentsPane
        config={{
          pluginKey: 'a',
          type: 'PanelDock',
          props: {
            align: 'left',
            icon: 'zujianku',
          },
        }}
        icons={props.icons}
        disabled={props?.disabled}
        showComponents={Components}
      />
    );
  }, [Components, props?.disabled]);

  return (
    <div className="component_list" id="__ComponentListGuide">
      {componentsPane}
    </div>
  );
};

const ComponentPanelPlugin = (
  ctx: IPublicModelPluginContext,
  { icons }: any,
) => {
  return {
    async init() {
      const { skeleton, project } = ctx;
      // 注册组件面板
      const componentsPane = skeleton.add({
        area: 'topArea',
        // type: 'PanelDock',
        type: 'Widget',
        name: 'componentsPane',
        // content: ComponentsPane,
        content: (props) => (
          <ComponentList ctx={ctx} {...props} icons={icons} />
        ),
        contentProps: {},
        props: {
          align: 'center',
          // icon: 'zujianku',
          description: <div>组件库1</div>,
        },
      });
      componentsPane?.disable?.();
      project.onSimulatorRendererReady(() => {
        componentsPane?.enable?.();
      });
    },
  };
};
ComponentPanelPlugin.pluginName = 'ComponentPanelPlugin';
ComponentPanelPlugin.meta = {
  preferenceDeclaration: {
    title: '保存插件配置',
    properties: [
      {
        key: 'icons',
        type: 'object',
        description: '用于存储图标',
      },
    ],
  },
};
export default ComponentPanelPlugin;
