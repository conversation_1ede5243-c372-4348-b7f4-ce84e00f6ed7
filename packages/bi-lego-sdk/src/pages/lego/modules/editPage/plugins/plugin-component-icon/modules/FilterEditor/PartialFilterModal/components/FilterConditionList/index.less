.filter-condition-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .condition-header {
    display: flex;
    justify-content: start;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    color: #86909c;
    .header-title {
      margin-right: 4px;
    }
  }

  .condition-list {
    flex: 1;
    overflow-y: auto;

    .condition-item {
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        background-color: #f2f3f5;
      }
      &.active {
        background-color: #e8f3ff;
      }

      .condition-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .condition-input {
          flex: 1;
          margin-right: 8px;
        }

        .condition-name-wrapper {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .error-icon {
            color: #ff7d00;
            font-size: 14px;
            margin-right: 4px;
          }

          .condition-name {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            cursor: text;
            max-width: 120px;
            text-overflow: ellipsis;
            line-height: 14px;
          }
        }

        .edit-btn {
          color: #1d2129;
          font-size: 14px;

          &:hover {
            background-color: #e5e6eb;
          }
        }
      }

      // &:hover .delete-btn {
      //   opacity: 1;
      // }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #8c8c8c;
      font-size: 14px;
      padding-bottom: 11px;
      .empty-state-png {
        width: 64px;
        height: 64px;
        background-image: url('@/assets/lego/noDataPartial.png');
        margin: 8px auto;
      }
      .empty-state-text {
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
      }
    }
  }
}
.condition-name-tips{
  z-index: 100000 !important;
}
