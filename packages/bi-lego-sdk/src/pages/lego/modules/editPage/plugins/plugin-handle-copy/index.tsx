import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { walkerComponents } from '@/pages/lego/utils';
// 保存功能示例
const HandleCopySchemaPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { config } = ctx;
      config.set('handleCopySchema', (schema) => {
        console.log('schema', schema);
        if (schema.componentName === 'PartialContainerFilter') {
          walkerComponents(schema, (component) => {
            component.props = {
              ...(component.props || {}),
              partialContainerFilterId: schema.id,
            };
          });
        }
      });
    },
  };
};
HandleCopySchemaPlugin.pluginName = 'HandleCopySchemaPlugin';
export default HandleCopySchemaPlugin;
