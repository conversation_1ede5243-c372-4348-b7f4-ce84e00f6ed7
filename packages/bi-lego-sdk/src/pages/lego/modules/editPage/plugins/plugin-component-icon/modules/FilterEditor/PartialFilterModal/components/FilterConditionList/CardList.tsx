import { TopDnDProvider } from '@/pages/lego/libraryMaterials/setter/TopDnDProvider';
import { Card } from './Card';

export const CardList = ({ value, moveCard, children }) => {
  return (
    <TopDnDProvider>
      {value?.map((item, index: number) => {
        return (
          <Card key={item.key} item={item} index={index} moveCard={moveCard}>
            {children(item)}
          </Card>
        );
      })}
    </TopDnDProvider>
  );
};
