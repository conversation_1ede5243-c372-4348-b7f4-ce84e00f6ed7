body {
  font-family:
    PingFangSC-Regular,
    Roboto,
    Helvetica Neue,
    Helvetica,
    Tahoma,
    Arial,
    PingFang SC-Light,
    Microsoft YaHei;
  font-size: 12px;

  * {
    box-sizing: border-box;
  }
}

body,
#lce-container {
  height: 100%;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -webkit-user-select: none;
  -webkit-user-drag: none;
  -webkit-text-size-adjust: none;
  -webkit-touch-callout: none;
  -webkit-font-smoothing: antialiased;

  #engine {
    width: 100%;
    height: 100%;
  }
}

html {
  min-width: 1024px;
}

.save-sample {
  width: 80px;
  height: 30px;
  background-color: #5584ff;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.load-assets {
  width: 100px;
  height: 30px;
  background-color: #5584ff;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.lc-left-area {
  display: none !important;
}

.lc-right-area {
  width: 400px !important;
  margin-left: 0px !important;
}

.lego_deviceClassName {
  .lc-resize-side {

    &.e,
    &.w {
      &:after {
        border: 0;
        background: transparent;
      }
    }

    &.n,
    &.s {
      &:after {
        border: 0;
        background: transparent;
      }
    }
  }

  .lc-borders-actions {
    .lc-borders-action {
      background: transparent;
      color: inherit;
      width: 22px;
    }
  }

  .dragging {
    background: transparent !important;
  }
}

.lc-workbench .lc-top-area {
  padding: 0 10px;
  margin-bottom: 1px;
}

.lc-tip {
  border-radius: 6px;
  padding: 5px 10px;
  background: rgba(42, 48, 72, 0.7);
  font-family: PingFang SC;
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  color: #ffffff;

  .lc-arrow {
    width: 20px;
    height: 10px;

    &:after {
      border: 10px solid transparent;
      border-top-color: rgba(42, 48, 72, 0.7);
    }
  }
}

// 组件 选中样式
.lc-bem-tools-XTab,.lc-bem-tools-PartialContainerFilter{
  .lc-borders.s,.lc-borders.se{
    display: none !important;
  }
}
.lc-bem-tools-parent-PartialContainerFilter{
  .lc-borders-actions{
    display: none;
  }
}

.lc-field-body{
  flex-direction: column;
  .lc-field-body{
    padding: 0;
  }
}

.AnalysisConfigSetter-box{
  .lc-field-body{
    padding: 0 10px 0 0 !important;
  }
}

.lc-field-body>div{
  width: 100%;
}

.createComponent-tips{
  z-index: 1000 !important;
}