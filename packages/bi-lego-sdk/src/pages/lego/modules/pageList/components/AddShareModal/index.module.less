.transfer_num {
  float: right;
  font-size: 13px;
  color: rgba(0, 0, 0, 60%);
}

.transfer_title {
  width: 255px;
}

.lego-noData {
  margin: 74px 28px;

  &_img {
    background-image: url("../../../../../../assets/img/lego-noData.png");
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
  }

  &_text {
    color: rgba(0, 0, 0, 60%);
  }
}

.transferAlert {
  background: #e8f2ff;
  border-radius: 6px;
  padding: 4px 8px;
  margin-bottom: 20px;

  &-icon {
    color: #2761f3 !important;
    font-size: 13px;
    padding-right: 4px;
  }
}

.lego-noDataTransfer {
  &_img {
    background-image: url("../../../../../../assets/img/lego-noData.png");
    width: 100px;
    height: 100px;
    margin: 10px auto;
  }

  &_text {
    color: rgba(0, 0, 0, 60%);
    text-align: center;
  }
}

.AddShareModal-Transfer{
  :global {
    .ant-transfer-list{
      max-width: 325px;
      min-width: 325px;
    }
  }
}