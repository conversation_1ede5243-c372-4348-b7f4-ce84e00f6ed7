// @ts-nocheck
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Select, SelectProps, Spin } from '@blmcp/ui';

const ViewMoreSelect = forwardRef((props: SelectProps, ref) => {
  const { getListData, showSearch = false, onSearch, ...other } = props;
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState([]);
  const [searchData, setSearchData] = useState<any>({});
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 0,
    pageSize: 50,
  });

  useImperativeHandle(ref, () => ({
    // 提供一个外部调用重新请求的方法
    rQuery: () => {
      handleInit(searchData);
    },
    // 调用重置
    reset: () => {
      setListData([]);
      setSearchData({});
      setPagination({
        total: 0,
        pageNum: 0,
        pageSize: 50,
      });
    },
  }));

  /**
   * 获取初始化数据
   */
  const handleInit = async (newSearchData?: any) => {
    const obj = {
      ...newSearchData,
      pageNum: 1,
      pageSize: 50,
    };
    try {
      const res = await getListData(obj);
      if (res?.code === 1 && res?.data?.items) {
        setListData(res.data.items);
        setPagination({
          pageSize: 50,
          pageNum: 1,
          total: Number(res.data?.totalNum),
        });
      }
    } catch (err) {
      console.log(err);
    }
  };

  /**
   * 动态请求数据
   */
  const fetchMoreData = async (
    newPagination: any,
    newListData: any,
    newSearchData?: any,
  ) => {
    if (loading) return;
    setLoading(true);
    const obj = {
      ...newSearchData,
      pageNum: newPagination.pageNum + 1,
      pageSize: newPagination.pageSize,
    };
    try {
      const res = await getListData(obj);
      if (res?.code === 1 && res?.data?.items) {
        setListData([...newListData, ...res.data.items]);
        setPagination({
          pageSize: newPagination.pageSize,
          pageNum: Number(res.data?.pageNum),
          total: Number(res.data?.totalNum),
        });
        setLoading(false);
      }
    } catch (err) {
      setLoading(false);
    }
  };

  /**
   * 下拉框滚动
   * @param e dom元素
   */
  const handleScroll = (e) => {
    const { target } = e;
    if (target.scrollTop + target.clientHeight + 1 >= target.scrollHeight) {
      if (pagination.total <= listData.length && listData.length > 0) return;
      fetchMoreData(pagination, listData, searchData);
    }
  };

  /**
   * 触发搜索
   * @param value
   */
  const handleOnSearch = async (value) => {
    if (typeof onSearch === 'function') {
      const result = onSearch(value);
      setSearchData(result);
      handleInit(result);
    }
  };

  useEffect(() => {
    // handleInit(searchData);
  }, []);

  return (
    <Select
      allowClear
      onPopupScroll={handleScroll}
      loading={loading}
      {...(showSearch && {
        showSearch,
        onSearch: handleOnSearch,
        filterOption: false,
      })}
      onDropdownVisibleChange={(type) => {
        if (type) {
          setSearchData({});
          handleInit({
            pageName: '',
          });
        }
      }}
      style={{ width: '320px' }}
      {...other}
    >
      {listData.map((item, index) => (
        <Select.Option
          key={index}
          value={item.pageKey}
          label={item.name}
          {...item.option}
        >
          {item.name}
        </Select.Option>
      ))}
      {/* 添加下拉时的loading */}
      {loading && (
        <Select.Option
          disabled
          style={{ textAlign: 'center' }}
          value={'loading'}
        >
          <Spin spinning={loading} />
        </Select.Option>
      )}
    </Select>
  );
});
export default ViewMoreSelect;
