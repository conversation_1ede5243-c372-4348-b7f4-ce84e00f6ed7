// @ts-nocheck
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import { Skeleton } from '@blmcp/ui';
import { LoadingOutlined } from '@ant-design/icons';
import { Alert } from '@blmcp/ui';
import { throttle } from 'lodash-es';
import {
  sceneActionStartMonitor,
  sceneActionEndMonitor,
} from '@/utils/eventTracking';
import { copyTemplate, queryReportPageList } from '@/pages/lego/api';
import { ReactComponent as ReportIcon } from '@/assets/lego/report-icon.svg';
import { ReactComponent as ViewIcon } from '@/assets/lego/view.svg';
import { ReactComponent as EditIcon } from '@/assets/lego/edit.svg';
// import { ReactComponent as Img1Icon } from '@/assets/lego-template/img1.svg';
// import { ReactComponent as Img2Icon } from '@/assets/lego-template/img2.svg';
// import { ReactComponent as Img3Icon } from '@/assets/lego-template/img3.svg';
// import Img1Icon from './icon/page1.svg';
// import Img2Icon from './icon/page2.svg';
// import Img3Icon from './icon/page3.svg';
// import Img4Icon from './icon/page4.svg';
// import Img5Icon from './icon/page5.svg';
// import Img6Icon from './icon/page6.svg';
// import Img7Icon from './icon/page7.svg';
// import Img8Icon from './icon/page8.svg';
// import Img9Icon from './icon/page9.svg';
// import Img10Icon from './icon/page10.svg';
// import Img11Icon from './icon/page11.svg';
// import Img12Icon from './icon/page12.svg';

const icoList = [
  require(`./icon/page1.png`),
  require(`./icon/page2.png`),
  require(`./icon/page3.png`),
  require(`./icon/page4.png`),
  require(`./icon/page5.png`),
  require(`./icon/page6.png`),
  require(`./icon/page7.png`),
  require(`./icon/page8.png`),
  require(`./icon/page9.png`),
  require(`./icon/page10.png`),
  require(`./icon/page11.png`),
  require(`./icon/page12.png`),
];

import styles from './index.module.less';
import event from '@/tool/event';
import config from '@/tool/config';

interface TemplateProps {
  pageListPermission: boolean;
}

const editTemplate = throttle(
  async (id, setEditId, reportName) => {
    setEditId(id);
    try {
      const res = await copyTemplate(id);
      const routerBase = config.get('basicInfo.userBaseRouter');
      if (res?.code === 1) {
        event.dispatch('templateCopy', {
          reportId: id,
          reportName,
          copyId: res?.data?.reportId,
        });
        setTimeout(() => {
          window.open(
            `/${routerBase}/legoBI/edit?reportId=${res?.data?.reportId}&overallLayoutShow=false`,
            '_blank',
          );
        });
      }
      sceneActionEndMonitor({
        sceneId: 'legoBI-copyTemplate-info',
        uniqueId: 0,
        maxTime: 6000,
      });
    } catch (error) {
      setEditId('');
    }

    setEditId('');
  },
  2000,
  { trailing: false },
);

export const Template = ({ pageListPermission }: TemplateProps) => {
  const { data, run, loading } = useRequest(queryReportPageList, {
    manual: true,
    onFinally: () => {
      event.dispatch('loaded');
    },
  });
  const routerBase = config.get('basicInfo.userBaseRouter');

  const [editId, setEditId] = useState('');

  useEffect(() => {
    run({
      type: 3,
      pageSize: 100, // 分页阈值 必填
      pageNum: 1, // 当前页数 必填});
    });
  }, [run]);

  useEffect(() => {
    event.dispatch('init');
  }, []);

  const copyItemTemplate = (id: string, reportName: string) => () => {
    sceneActionStartMonitor({
      sceneId: 'legoBI-copyTemplate-info',
      uniqueId: 0,
      maxTime: 6000,
    });
    editTemplate(id, setEditId, reportName);
  };

  const list = data?.data?.items ?? [];

  return (
    <div className={styles['template-container']}>
      {pageListPermission ? (
        <Alert
          style={{ marginLeft: '20px', marginTop: '5px', marginRight: '20px' }}
          message="编辑报告后，请到“我创建的“页签下查看"
          type="info"
          showIcon
          closable
        />
      ) : null}
      <div className={styles['template-wrap']}>
        {loading ? (
          <Skeleton></Skeleton>
        ) : (
          <>
            {list.map((itemList, index) => {
              return (
                <div className={styles['card']} key={itemList.id}>
                  <p
                    className={styles['name']}
                    onClick={() => {
                      // 数据乐高_自助报告_详情点击数
                      event.dispatch('templateView', {
                        reportId: itemList.id,
                        reportName: itemList.reportName,
                        reportType: 'template',
                      });
                      window.open(
                        `/${routerBase}/legoBI/view?reportId=${
                          itemList.id
                        }&publish=${itemList.status || 0}`,
                        // '_self',
                      );
                    }}
                  >
                    <ReportIcon className={styles['icon']} />
                    <span className={styles['title']}>
                      {itemList.reportName}
                    </span>
                  </p>

                  <p
                    className={styles['img-wrap']}
                    onClick={() => {
                      // 点击查看按钮埋点
                      event.dispatch('templateView', {
                        reportId: itemList.id,
                        reportName: itemList.reportName,
                        reportType: 'template',
                      });
                      window.open(
                        `/${routerBase}/legoBI/view?reportId=${
                          itemList.id
                        }&publish=${itemList.status || 0}`,
                        // '_self',
                      );
                    }}
                  >
                    <img src={icoList[index % 12]}></img>
                  </p>

                  <p className={styles['footer']}>
                    <div
                      className={styles['btn']}
                      onClick={() => {
                        // 点击查看按钮埋点
                        event.dispatch('templateView', {
                          reportId: itemList.id,
                          reportName: itemList.reportName,
                          reportType: 'template',
                        });
                        window.open(
                          `/${routerBase}/legoBI/view?reportId=${
                            itemList.id
                          }&publish=${itemList.status || 0}`,
                          // '_self',
                        );
                      }}
                    >
                      <ViewIcon className={styles['operation-icon']} />
                      查看
                    </div>

                    {itemList.editable !== 0 ? (
                      <div
                        className={styles['btn']}
                        style={{ borderLeft: '1px solid #E7E8EB' }}
                        onClick={copyItemTemplate(
                          itemList.id,
                          itemList.reportName,
                        )}
                      >
                        {editId === itemList.id ? (
                          <LoadingOutlined
                            spin
                            className={styles['operation-icon']}
                          />
                        ) : (
                          <EditIcon className={styles['operation-icon']} />
                        )}
                        编辑
                      </div>
                    ) : null}
                  </p>
                </div>
              );
            })}
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </>
        )}
      </div>
    </div>
  );
};
