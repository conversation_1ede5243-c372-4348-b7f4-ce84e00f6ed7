// .table {
//   flex-grow: 1;
//   height: 0;
// }
// .SearchOutlinedIcon {
//   color: rgba(0, 0, 0, 0.3);
// }
// .legoList-search {
//   padding: 0px 15px;
//   display: flex;
//   justify-content: space-between;
// }
// :global {
//   .ant-table-container {
//     height: calc(100vh - 125px);
//     overflow-y: auto;
//   }
// }
// .commonTable {
//   :global {
//     .ant-modal-body {
//       height: calc(100vh - 100px);
//       overflow-y: auto;
//     }
//   }
// }

.successStatus {
  width: 46px;
  height: 20px;
  border-radius: 4px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  color: #22981c;
  background: rgba(34, 152, 28, 0.05);
}
.defaultStatus {
  width: 46px;
  height: 20px;
  border-radius: 4px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  background: rgba(37, 52, 79, 0.05);
}

.operation {
  &-item:last-child {
    margin: 0;
  }
  &-item {
    padding: 0px !important;
    color: #2761f3;
    cursor: pointer;
    margin-right: 10px;
  }
  &-itemShare {
    padding: 0px !important;
    color: #2761f3;
    cursor: pointer;
  }
}
