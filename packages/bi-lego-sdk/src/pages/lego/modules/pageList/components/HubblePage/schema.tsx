/* share-url-hooks-disable-file */
// @ts-nocheck
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Space,
  Checkbox,
} from '@blmcp/ui';
import { throttle } from 'lodash-es';
import {
  copyTemplate,
  pageListData,
  publishReportState,
  revokeTemplate,
  offlineTemplate,
} from '@/pages/lego/api/hubble';
import { deleteReport } from '@/pages/lego/api';
import { copyTemplateClick } from '@/pages/lego/utils/common';

import ViewSelect from './viewSelect';
import styles from './index.module.less';
import config from '@/tool/config';
import { useState, useEffect } from 'react';

const status = {
  0: {
    t: '未发布',
    c: 'default',
  },
  1: {
    t: '已发布',
    c: 'success',
  },
  2: {
    t: '测试中',
    c: 'warning',
  },
  3: {
    t: '放量中',
    c: 'warning',
  },
  4: {
    t: '已回撤',
    c: 'default',
  },
  5: {
    t: '已全量',
    c: 'success',
  },
  6: {
    t: '已下线',
    c: 'default',
  },
};

const CustomContent = React.forwardRef((props, ref) => {
  const [value, setValue] = useState('');
  const [msg, setMsg] = useState(
    '即将回撤下线报告，sp后台将不可见；新增的计算字段转化为公共字段不支持回撤，是否继续',
  );
  // 使用useImperativeHandle暴露状态给父组件
  React.useImperativeHandle(ref, () => ({
    getValue: () => value,
  }));

  const onChange = (e) => {
    const val = e.target.value;
    if (val === '0') {
      setMsg(
        '即将回撤下线报告，模板回撤到未发布状态；新增的计算字段转化为公共字段不支持回撤，是否继续',
      );
    } else if (val === '1') {
      setMsg(
        '即将回撤下线报告，模板回撤到已发布状态；新增的计算字段转化为公共字段不支持回撤，是否继续',
      );
    }
    setValue(val);
  };

  return (
    <div style={{ textAlign: 'center' }}>
      <Radio.Group value={value} onChange={onChange}>
        <Radio value="0">未发布</Radio>
        <Radio value="1">已发布</Radio>
      </Radio.Group>
      <div style={{ marginTop: '12px', fontSize: '12px' }}>{msg}</div>
    </div>
  );
});

export default function (
  ref: any,
  type: number,
  { openSelectBrandModal, defaultBrandName = '约约出行' },
) {
  const customContentRef = React.useRef('');
  const [form] = Form.useForm();
  const routerBase = config.get('basicInfo.manageBaseRouter');
  const sdkConfig = config.get('sdkConfig');
  const {
    templateDatasetAuth,
    templateViewType,
    templateEditBtn,
    templateExportBtn,
  } = sdkConfig;

  const plainOptions = [
    { label: '公共模板展示', value: 'showInList' },
    { label: '外链形式', value: 'showInLink' },
  ];

  // 删除报告
  const deleteReportClick = throttle(
    (record: any) => {
      Modal.confirm({
        title: '您确认要删除吗？',
        okText: '确定',
        cancelText: '取消',
        autoFocusButton: null,
        content: '删除后，当前报告将无法恢复',
        onOk: () => {
          deleteReport({ reportId: record.id, id: record.id, type }).then(
            (res) => {
              if (res && res?.code === 1) {
                // 删除成功，重新调用列表接口
                message.success('删除成功');
                ref?.current?.refreshTableList({}, false);
              }
            },
          );
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 发布自己的模板到公共模板上
  const publishTemplateClick = throttle(
    (record: any) => {
      Modal.confirm({
        title: '确认操作',
        content: '即将把报告发布成模板，发布后将创建模板副本，是否继续？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          copyTemplate({ reportId: record.id, source: 4 }).then((res) => {
            if (res?.code === 1) {
              message.success('发布成功');
              ref?.current?.refreshTableList({}, false);
            }
          });
        },
        onCancel: () => {},
      });
    },
    1000,
    { trailing: false },
  );

  // 上线、下线、回撤 弹窗
  const publishStatusClick = throttle(
    (record: any, templateStatus: number, info = '操作成功') => {
      const map: any = {
        // 1: '即将把报告发布成模板，发布后将创建模板副本，是否继续',
        2: `即将把模板同步至品牌【${defaultBrandName}】(${
          config.get('basicInfo.defaultTenantId') || 1
        })，是否继续`,
        4: '即将回撤模板，模状态将变更为未发布；新增的计算字段转化为公共字段不支持回撤，是否继续',
        5: '即将把模板放量至所有品牌，请谨慎操作',
        6: '即将下线报告，sp后台将不可见，该操作不可逆，是否继续',
      };
      Modal.confirm({
        title: '确认操作',
        content: map[templateStatus],
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          let request = publishReportState;
          const params = {
            reportId: record.id,
            templateStatus,
          };
          if (templateStatus === 4) {
            // 回撤
            request = revokeTemplate;
            params.targetStatus = 0;
          } else if (templateStatus === 6) {
            //下线
            request = offlineTemplate;
          }
          request(params).then((res) => {
            if (res?.code === 1) {
              // 发布成功，重新调用列表接口
              message.success(info);
              ref?.current?.refreshTableList({}, false);
            }
          });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 上线
  const templateOnlineClick = throttle(
    (
      record: any,
      formVal: any,
      selectListInfo,
      templateStatus: number,
      info = '操作成功',
    ) => {
      // let selectList = [...new Set(selectListInfo.map((i) => i.valueId))];
      const { dataAuthMode, delegateWay, pageMarkStr, editable, exportable } =
        formVal;
      // 当“模板展示形式”选项被隐藏时，默认选择“公共模板展示”
      let showInList = 0; // 公共模板展示  1-不展示 0-展示
      let showInLink = 0; // 是否外链形式: 0-不展示 1-展示
      if (templateViewType) {
        showInList = delegateWay?.includes('showInList') ? 0 : 1;
        showInLink = delegateWay?.includes('showInLink') ? 1 : 0;
      }
      const map: any = {
        // 1: '即将把报告发布成模板，发布后将创建模板副本，是否继续',
        2: `即将把模板同步至品牌【${defaultBrandName}】(${
          config.get('basicInfo.defaultTenantId') || 1
        })，是否继续`,
        4: '即将回撤下线报告，sp后台将不可见，该操作不可逆，是否继续',
        5: '即将把模板放量至所有品牌，请谨慎操作',
        6: '即将下线报告，sp后台将不可见，该操作不可逆，是否继续',
      };
      Modal.confirm({
        title: '确认操作',
        content: showInList === 1 ? map[5] : map[templateStatus],
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          const pageMarks = pageMarkStr?.split(',') || [];
          const params = {
            reportId: record.id,
            templateStatus, // 模板状态
            dataAuthMode,
            showInList,
            showInLink,
            pageMarks,
            editable,
            exportable,
          };
          publishReportState(params)
            .then((res) => {
              if (res?.code === 1) {
                // 成功之后调用鉴权接口
                // 测网传dev， 正网环境传pre
                let typeURL = 'dev';
                let hostnameURL = window.location.hostname ?? '';
                const list = [
                  'hubble-pre.yueyuechuxing.cn',
                  'hubble-pre2.yueyuechuxing.cn',
                  'hubble.yueyuechuxing.cn',
                ];
                if (list.includes(hostnameURL)) {
                  typeURL = 'pre';
                }
                // 重新调用列表接口
                ref?.current?.refreshTableList({}, false);
              }
            })
            .catch((e) => {
              console.log(e);
            });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 测试中撤回
  const doBackTestData = throttle(
    (record: any, templateStatus: number, info = '操作成功') => {
      Modal.confirm({
        title: '请选择回撤阶段',
        content: <CustomContent ref={customContentRef} />,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            const targetStatus = customContentRef.current?.getValue();
            if (!targetStatus) {
              message.error('请选择回撤阶段');
              reject();
            } else {
              const params = {
                reportId: record.id,
                templateStatus: record.templateStatus,
                targetStatus: targetStatus,
              };
              revokeTemplate(params).then((res) => {
                if (res?.code === 1) {
                  // 发布成功，重新调用列表接口
                  message.success(info);
                  ref?.current?.refreshTableList({}, false);
                  resolve();
                }
              });
            }
          });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 校验规则
  const validateEnglishAndLength = (_, value) => {
    if (!value) {
      return Promise.reject(new Error('请输入报告唯一key'));
    } else if (value.length > 100) {
      // 检查是否超过100字符
      return Promise.reject(new Error('仅限输入100字符'));
    } else if (!/^[a-zA-Z]*$/.test(value)) {
      // 检查是否只包含英文字符
      return Promise.reject(new Error('只能输入英文字符'));
    }
    return Promise.resolve();
  };
  const OnLineForm = ({ record }) => {
    useEffect(() => {
      form.setFieldsValue({
        ...record,
        pageMarkStr: record?.pageMarks?.join(',') || '',
      });
    }, [record, form]);
    return (
      <>
        <Form
          form={form}
          colon={false}
          labelAlign={'left'}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{
            dataAuthMode: 0,
            pageMarkStr: '',
            editable: 0,
            exportable: 1,
          }}
        >
          {templateDatasetAuth && (
            <Form.Item
              name="dataAuthMode"
              label="数据集鉴权"
              extra="配置模板是否进行数据集权限校验，常规场景统一需要鉴权"
              rules={[{ required: true, message: '请选择数据集鉴权' }]}
            >
              <Radio.Group>
                <Radio value={0}>鉴权</Radio>
                <Radio value={1}>不鉴权</Radio>
              </Radio.Group>
            </Form.Item>
          )}
          {templateViewType && (
            <Form.Item
              name="delegateWay"
              label="模板展示形式"
              extra="配置公共模板展示的位置"
              rules={[{ required: true, message: '请选择下放形式' }]}
              initialValue={['showInList', 'showInLink']} // 默认选中所有选项
            >
              <Checkbox.Group
                options={plainOptions}
                onChange={(checkedValues) => {
                  // 当取消勾选"是否外链形式"时，清空报告唯一key的值
                  if (!checkedValues.includes('showInLink')) {
                    form.setFieldValue({ pageMarkStr: '' });
                  }
                  // 当取消勾选"公共模板展示"时，重置“编辑按钮”选项的值
                  if (!checkedValues.includes('showInList')) {
                    form.setFieldValue({ editable: 0 });
                  }
                }}
              />
            </Form.Item>
          )}
          <Form.Item
            noStyle
            shouldUpdate={(prev, current) =>
              prev.delegateWay !== current.delegateWay
            }
          >
            {({ getFieldValue }) =>
              // 只有当勾选了"是否外链形式"时才显示报告唯一key输入框
              getFieldValue('delegateWay')?.includes('showInLink') ? (
                <Form.Item
                  name="pageMarkStr"
                  label="报告唯一key"
                  extra="配置菜单资源关联的模板名称，仅支持输入英文字符"
                  rules={[
                    { required: true, validator: validateEnglishAndLength },
                  ]}
                >
                  <Input placeholder="请输入报告唯一key" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          {templateEditBtn && (
            <Form.Item
              noStyle
              shouldUpdate={(prev, current) =>
                prev.delegateWay !== current.delegateWay
              }
            >
              <Form.Item
                name="editable"
                label="编辑按钮"
                extra="配置模板下放给SP之后，是否支持SP编辑公共模板里的内容"
                rules={[{ required: true, message: '请选择是否展示编辑按钮' }]}
              >
                <Radio.Group>
                  <Radio value={1}>展示</Radio>
                  <Radio value={0}>不展示</Radio>
                </Radio.Group>
              </Form.Item>
            </Form.Item>
          )}
          {templateExportBtn && (
            <Form.Item
              name="exportable"
              label="导出按钮"
              extra="配置模板下放给SP之后，是否支持SP导出公共模板里的内容"
              rules={[{ required: true, message: '请选择是否展示导出按钮' }]}
            >
              <Radio.Group>
                <Radio value={1}>展示</Radio>
                <Radio value={0}>不展示</Radio>
              </Radio.Group>
            </Form.Item>
          )}
        </Form>
      </>
    );
  };
  // 模板上线弹窗
  const templateOnlineModal = throttle(
    async (record: any) => {
      let selectListInfo: any = [];
      Modal.confirm({
        title: '模板上线',
        icon: null,
        width: '560px',
        closable: true,
        content: <OnLineForm record={record} />,
        okText: '提交上线',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            form
              .validateFields()
              .then((values) => {
                templateOnlineClick(
                  record,
                  form.getFieldsValue(),
                  selectListInfo,
                  2,
                  '发布成功，请前往SP后台进行测试',
                );
                // 重置表单字段到初始值
                form.resetFields();
                resolve();
              })
              .catch((info) => {
                console.log('请完善表单信息:', info);
                reject();
              });
          });
        },
        onCancel: () => {
          // 重置表单字段到初始值
          form.resetFields();
        },
      });
    },
    1000,
    { trailing: false },
  );

  return {
    // 模板页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'hubble-qbi-legoList',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'q',
        placeholder: type === 1 ? '搜索报告' : '搜索模板',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chart-manager/queryReportPageList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: type === 1 ? '报告名称' : '模板名称',
          dataIndex: 'reportName',
          align: 'left',
          // width: 220,
          render: (text, record) => (
            <a
              target="_blank"
              href={`/${routerBase}/legoBI/view?reportId=${record.id}&publish=${
                record.status || 0
              }`}
            >
              {record.reportName}
            </a>
          ),
        },
        {
          title: '创建者',
          dataIndex: 'createUserName',
          align: 'left',
          width: 90,
        },
        {
          title: '修改者',
          dataIndex: 'updateUserName',
          align: 'left',
          width: 90,
        },
        {
          title: type === 1 ? '报告状态' : '模板状态',
          align: 'left',
          width: 100,
          render: (text, record) => (
            <Space size="middle">
              <span
                className={
                  styles[status[record.templateStatus || record.status].c]
                }
              >
                {/*{status[record.templateStatus || record.status].t}*/}
                {type !== 1 &&
                record.templateStatus === 2 &&
                record.showInList === 1
                  ? '已上线'
                  : status[record.templateStatus || record.status].t}
              </span>
            </Space>
          ),
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'left',
          width: 170,
        },
        {
          title: '最近修改时间',
          dataIndex: 'updateTime',
          align: 'left',
          width: 170,
        },
        {
          title: '公共模板是否展示',
          align: 'left',
          show: type !== 1,
          render: (text, record) => (
            <span>{record.showInList ? '否' : '是'}</span>
          ),
        },
        {
          title: '是否外链形式',
          // dataIndex: 'showInLink',
          align: 'left',
          show: type !== 1,
          render: (text, record) => (
            <span>{record.showInLink ? '是' : '否'}</span>
          ),
        },
        {
          title: '报告唯一key',
          align: 'left',
          show: type !== 1,
          width: 170,
          render: (text, record) => (
            <span className={styles['pageMarks']}>
              {record?.pageMarks?.join(',')}
            </span>
          ),
        },
        {
          title: '编辑按钮是否展示',
          dataIndex: 'editable',
          align: 'left',
          show: type !== 1,
          render: (text, record) => (
            <span>{record.editable ? '是' : '否'}</span>
          ),
        },
        {
          title: '导出按钮是否展示',
          dataIndex: 'exportable',
          align: 'left',
          show: type !== 1,
          render: (text, record) => (
            <span>{record.exportable ? '是' : '否'}</span>
          ),
        },
        {
          title: '是否鉴权',
          dataIndex: 'dataAuthMode',
          align: 'left',
          show: type !== 1,
          render: (text, record) => (
            // 后端设计和别的是否XX是反的......
            <span>{record.dataAuthMode ? '否' : '是'}</span>
          ),
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 273,
          fixed: 'right',
          render: (
            text: any,
            // record: { status: number; id: any; actionList: number[] },
            record,
          ) => (
            <div className={styles['operation']}>
              {(type === 1 && (
                // 如果是自己创建的
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(
                        `/${routerBase}/legoBI/edit?reportId=${record.id}`,
                      );
                    }}
                  >
                    编辑
                  </Button>
                  {
                    <Button
                      type="link"
                      onClick={() =>
                        copyTemplateClick(record, !!record.status ? 3 : 2)
                      }
                    >
                      复制报告
                    </Button>
                  }
                  {!!record.status && (
                    <Button
                      type="link"
                      onClick={() => publishTemplateClick(record)}
                    >
                      发布模板
                    </Button>
                  )}
                  <Button onClick={() => deleteReportClick(record)} type="link">
                    删除
                  </Button>
                </>
              )) || (
                <>
                  {[0].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => {
                        window.open(
                          `/${routerBase}/legoBI/edit?reportId=${record.id}`,
                        );
                      }}
                    >
                      编辑
                    </Button>
                  )}
                  <Button
                    type="link"
                    onClick={() => copyTemplateClick(record, 0, '模板')}
                  >
                    复制模板
                  </Button>
                  {[1].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => {
                        // 当模板上线弹窗没有选择项时，直接弹出确认操作框
                        if (
                          templateDatasetAuth ||
                          templateViewType ||
                          templateEditBtn ||
                          templateExportBtn
                        ) {
                          templateOnlineModal(record);
                        } else {
                          templateOnlineClick(record, {}, [], 2);
                        }
                      }}
                    >
                      模板上线
                    </Button>
                  )}

                  {[1, 2].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => {
                        if (record.templateStatus === 2) {
                          doBackTestData(record);
                        } else {
                          publishStatusClick(record, 4);
                        }
                      }}
                    >
                      回撤
                    </Button>
                  )}
                  {[0, 1, 4].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => deleteReportClick(record)}
                    >
                      删除
                    </Button>
                  )}
                  {[2, 3].includes(record.templateStatus) &&
                    record.showInList !== 1 && (
                      <Button
                        type="link"
                        onClick={() => {
                          openSelectBrandModal(record);
                        }}
                      >
                        模板放量
                      </Button>
                    )}
                  {[3].includes(record.templateStatus) &&
                    record.showInList !== 1 && (
                      <Button
                        type="link"
                        onClick={() => publishStatusClick(record, 5)}
                      >
                        模板全量
                      </Button>
                    )}
                  {[3, 5].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => publishStatusClick(record, 6)}
                    >
                      下线
                    </Button>
                  )}
                </>
              )}
            </div>
          ),
        },
      ],
    },
  };
}
