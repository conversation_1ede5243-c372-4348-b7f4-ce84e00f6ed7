:global {
  .ant-form-item .ant-form-item-extra {
    font-size: 12px;
  }
  .exact-search-area{
    position: relative;
    .hb-search-list-templateState-select{
      position: absolute;
      left: 235px;
      .ant-select{
        margin-left: 10px;
        width: 150px;
      }
    }
  }
}

.success {
  width: 46px;
  height: 20px;
  border-radius: 4px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  color: #22981c;
  background: rgba(34, 152, 28, 0.05);
}
.default {
  width: 46px;
  height: 20px;
  border-radius: 4px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  background: rgba(37, 52, 79, 0.05);
}
.warning {
  width: 46px;
  height: 20px;
  border-radius: 4px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  color: #ed7b2f;
  background: #fef3e6;
}

.operation {
  :global {
    .ant-btn-link{
      padding: 0px !important;
      color: #2761f3;
      cursor: pointer;
      &:last-child{
        margin-right: 0 !important;
      }
    }
  }
}

.commonTable {
  :global {
    .ant-pagination-options {
      margin-right: 10px;
    }
    .ant-table-cell .ant-btn-link.BLMButton_Antd {
      margin-right: 12px;
    }
    .template-page-container .table-area .table-area-pagination {
      padding: 0;
    }
  }
}

.brandChange {
  padding: 10px 0;
  :global {
    .brandChange-span,
    .ant-select {
      display: inline-block;
      vertical-align: middle;
    }
    .brandChange-span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      width: 120px;
      padding-left: 10px;
    }
    .ant-select {
      width: 240px;
    }
  }
}
