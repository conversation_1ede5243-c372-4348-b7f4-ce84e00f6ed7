// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import { Button, Modal } from '@blmcp/ui';
import {
  BLMTemplatePage,
  TemplatePageSchema,
} from '@blmcp/peento-businessComponents';
import { message } from '@blmcp/ui';

import { throttle } from 'lodash-es';
import request from '@/utils/request';
import { clearLocalPageId } from '@/pages/lego/utils';
import { deleteReport, addShares, exchangeOwner } from '../../../../api';
import UserTransfer from '../AddShareModal/index';
import { schemaEdit } from './schemaEdit';
import { schemaShare } from './schemaShare';
import styles from './index.module.less';
import event from '@/tool/event';
import config from '@/tool/config';

// console.log(1);
interface CommonTableProps {
  tabKey?: string;
  pageListPermission: boolean;
}

const deleteItem = throttle(
  async (id, tabKey, editRef, shareRef) => {
    Modal.confirm({
      title: '您确认要删除吗？',
      okText: '确定',
      cancelText: '取消',
      autoFocusButton: null,
      content: '删除后，当前报告将无法恢复',
      onOk: () => {
        deleteReport({ reportId: id, type: Number(tabKey) }).then((res) => {
          if (res && res?.code === 1) {
            // 删除成功，重新调用列表接口
            message.success('删除成功');
            // 删除完重新调用接口
            if (tabKey === '1') {
              editRef?.current?.refreshTableList({}, false);
            } else {
              shareRef?.current?.refreshTableList({}, false);
            }
          }
        });
      },
    });
  },
  1000,
  { trailing: false },
);

const CommonTable = ({ tabKey, pageListPermission }: CommonTableProps) => {
  const editRef = useRef<null | HTMLElement>(null);
  const shareRef = useRef<null | HTMLElement>(null);
  const routerBase = config.get('basicInfo.userBaseRouter');
  // 分享&编辑弹窗
  const [modalView, setModalView] = useState({
    visible: false,
    type: '',
    reportId: Number,
  });
  // 穿梭框内选中的值
  const [selectedKeys, setSelectedKeys] = useState({});

  // 点击删除报告
  const handleDelete = (id: number) => {
    deleteItem(id, tabKey, editRef, shareRef);
  };

  // 弹窗确认
  const handleOk = () => {
    if (
      (modalView.type === 'share' &&
        (selectedKeys?.originShareList.length ||
          selectedKeys?.targetKeys?.length)) ||
      (modalView.type === 'transfer' && Number(selectedKeys?.targetKeys))
    ) {
      const searchParamsShare = {
        reportId: modalView.reportId,
        shareList: selectedKeys?.targetKeys ?? [],
        originShareList: selectedKeys?.originShareList,
      };
      const searchParamsTransfer = {
        reportId: modalView.reportId,
        transferId: selectedKeys?.targetKeys,
      };
      const queryApi = modalView.type === 'share' ? addShares : exchangeOwner;
      const searchParams =
        modalView.type === 'share' ? searchParamsShare : searchParamsTransfer;
      queryApi(searchParams).then((res) => {
        if (res && res?.code === 1) {
          if (modalView.type === 'share') {
            message.success('分享成功');
          } else {
            // 编辑内转让
            message.success('转让成功');
          }
          if (modalView.type === 'transfer') {
            // 仅转让需要重新调用
            editRef?.current?.refreshTableList({}, false);
          }
        }
      });
      setModalView({ ...modalView, visible: false, type: '' });
    } else {
      message.error('请选择人员');
    }
  };

  // 关闭报告弹窗
  const handleCancel = () => {
    setModalView({ ...modalView, visible: false, type: '' });
  };
  useEffect(() => {
    const handleStorageChange = (ev) => {
      if (ev.key === 'lego-bi-update-reportName' && ev.newValue) {
        editRef?.current?.refreshTableList({}, false);
      }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);
  return (
    <div className={styles['commonTable']}>
      {tabKey === '1' ? (
        <BLMTemplatePage
          request={request}
          ref={editRef}
          schema={
            schemaEdit({ setModalView, handleDelete }) as TemplatePageSchema
          }
          tableActionsRight={
            <a
              target="_blank"
              href={`/${routerBase}/legoBI/edit?overallLayoutShow=false`}
            >
              <Button
                type="primary"
                onClick={() => {
                  clearLocalPageId();
                  event.dispatch('templateAdd');
                }}
              >
                新建报告
              </Button>
            </a>
          }
          height={'calc(100vh - 126px)'}
          onBeforeFetch={(res) => {
            return {
              pageNum: res.pageNum,
              pageSize: res.pageSize,
              type: Number(tabKey),
              searchKey: res.searchKey,
            };
          }}
        />
      ) : (
        <BLMTemplatePage
          request={request}
          ref={shareRef}
          schema={
            schemaShare({ setModalView, handleDelete }) as TemplatePageSchema
          }
          height={
            pageListPermission ? 'calc(100vh - 126px)' : 'calc(100vh - 66px)'
          }
          onBeforeFetch={(res) => {
            return {
              pageNum: res.pageNum,
              pageSize: res.pageSize,
              type: Number(tabKey),
              searchKey: res.searchKey,
            };
          }}
        />
      )}
      {/* 分享报告弹窗 start */}
      {modalView.visible && (
        <Modal
          title={modalView.type === 'share' ? '分享报告' : '转让报告'}
          open={modalView.visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={modalView.type === 'share' ? 720 : 560}
          style={{ maxHeight: '70vh' }}
          closable={true}
          maskClosable={false}
        >
          <UserTransfer
            modalView={modalView}
            onModalOk={(e: any) => setSelectedKeys(e)}
            tabsType={tabKey}
          />
        </Modal>
      )}
    </div>
  );
};

export default CommonTable;
