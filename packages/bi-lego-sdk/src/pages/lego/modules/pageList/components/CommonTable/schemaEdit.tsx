// @ts-nocheck
import { Button, Space, Tag, Tooltip } from '@blmcp/ui';
import styles from './index.module.less';
import config from '@/tool/config';
import event from '@/tool/event';

export const schemaEdit = ({ setModalView, handleDelete }) => {
  const reportOperateConfig = config.get('sdkConfig.reportOperateConfig');
  const routerBase = config.get('basicInfo.userBaseRouter');
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'qbi-legoListEdit',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'searchKey',
        placeholder: '搜索报告',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chart-manager/queryReportPageList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'left',
          // width: 304,
          render: (text, record) => (
            <a
              target="_blank"
              href={`/${routerBase}/legoBI/view?reportId=${record.id}&publish=${
                record.status || 0
              }`}
              onClick={() => {
                // 数据乐高_自助报告_详情点击数
                event.dispatch('templateView', {
                  reportId: record.id,
                  reportType: 'myEdit',
                });
              }}
            >
              {record.reportName}
            </a>
          ),
        },
        {
          title: '创建者',
          dataIndex: 'createUserName',
          align: 'left',
          // width: 140,
        },
        {
          title: '修改者',
          dataIndex: 'updateUserName',
          align: 'left',
          // width: 140,
        },
        {
          title: '发布状态',
          align: 'left',
          // width: 80,
          render: (text, record) => (
            <Space size="middle">
              {record.status === 1 ? (
                <span className={styles['successStatus']}>已发布</span>
              ) : (
                <span className={styles['defaultStatus']}>未发布</span>
              )}
            </Space>
          ),
        },
        {
          title: '最近打开时间',
          dataIndex: 'openTime',
          align: 'left',
          // width: 174,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 195,
          fixed: 'right',
          render: (
            text: any,
            // record: { status: number; id: any; actionList: number[] },
            record,
          ) => (
            <div className={styles['operation']}>
              {(reportOperateConfig.includes('transfer') && (
                <Button
                  type="link"
                  style={{ margin: '0 10px 0 0' }}
                  className={styles['operation-item']}
                  disabled={!record.actionList.includes(3)}
                  onClick={() => {
                    event.dispatch('templateTransfer', {
                      reportId: record.id,
                      reportType: 'list',
                    });

                    setModalView({
                      visible: true,
                      type: 'transfer',
                      reportId: record?.id,
                    });
                  }}
                >
                  转让
                </Button>
              )) ||
                null}
              {(reportOperateConfig.includes('edit') && (
                <Button
                  className={styles['operation-item']}
                  type="link"
                  style={{ margin: '0 10px 0 0' }}
                  disabled={!record.actionList.includes(1)}
                  onClick={() => {
                    window.open(
                      `/${routerBase}/legoBI/edit?reportId=${record.id}&overallLayoutShow=false`,
                    );
                    event.dispatch('templateEdit', {
                      reportId: record.id,
                      reportType: 'list',
                    });
                  }}
                >
                  编辑
                </Button>
              )) ||
                null}
              {(reportOperateConfig.includes('share') && (
                <Tooltip
                  title={
                    !record.actionList.includes(2)
                      ? record.status === 1
                        ? '当前报告已转让，不能分享'
                        : '当前报告未发布'
                      : ''
                  }
                >
                  <Button
                    type="link"
                    className={styles['operation-item']}
                    style={{ margin: '0 10px 0 0' }}
                    disabled={!record.actionList.includes(2)}
                    onClick={() => {
                      event.dispatch('templateShare', {
                        reportId: record.id,
                        reportType: 'list',
                      });
                      setModalView({
                        visible: true,
                        type: 'share',
                        reportId: record?.id,
                      });
                    }}
                  >
                    分享
                  </Button>
                </Tooltip>
              )) ||
                null}
              <Button
                type="link"
                className={styles['operation-item']}
                disabled={!record.actionList.includes(4)}
                onClick={() => handleDelete(record.id)}
              >
                删除
              </Button>
            </div>
          ),
        },
      ],
    },
  };
  return schema;
};
