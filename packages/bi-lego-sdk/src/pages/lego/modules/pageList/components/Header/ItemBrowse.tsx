// @ts-nocheck
import { Tooltip } from '@blmcp/ui';
import { useEffect, useRef, useState } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
// @ts-expect-error
import { ReactComponent as ListIcon } from '@/assets/lego-template/list.svg';
// @ts-expect-error
import { ReactComponent as RightArrowIcon } from '@/assets/lego-template/right-arrow.svg';
// @ts-expect-error
import styles from './index.module.less';

interface ItemBrowseProps {
  url: string;
  text: string; // item.pageName
}

export const ItemBrowse = ({ url, text }: ItemBrowseProps) => {
  const [tooltipEnable, setTooltipEnable] = useState(false);
  const textRef = useRef<HTMLSpanElement>(null);
  // tooltipEnable 判断
  useEffect(() => {
    const node: HTMLSpanElement | null = textRef?.current;
    const resizeObserver = new ResizeObserver((entries) => {
      const scrollWidth = node?.scrollWidth ?? 0;
      const offsetWidth = node?.offsetWidth ?? 0;
      if (scrollWidth > offsetWidth) {
        setTooltipEnable(true);
      } else {
        setTooltipEnable(false);
      }
    });
    if (node) {
      resizeObserver.observe(node);
    }

    return () => {
      if (node) {
        resizeObserver.unobserve(node);
      }
    };
  }, [setTooltipEnable]);

  return (
    <Tooltip
      title={text}
      mouseLeaveDelay={0}
      open={tooltipEnable ? undefined : false}
    >
      <a
        target="_blank"
        ignore="true"
        href={`${window.location.origin}${url}`}
        rel="opener"
      >
        <div className={styles['browsing-item']} ref={textRef}>
          <ListIcon className={styles.icon} /> {text}
          <RightArrowIcon className={styles['right-icon']} />
        </div>
      </a>
    </Tooltip>
  );
};
