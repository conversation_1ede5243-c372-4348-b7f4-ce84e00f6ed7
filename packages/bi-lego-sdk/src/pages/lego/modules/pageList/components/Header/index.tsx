// @ts-nocheck
import { useRequest } from 'ahooks';
import { Empty, Skeleton } from '@blmcp/ui';
import { getBrowsingHistory_HotContent } from '@/pages/lego/api/template';

import LegoNoDataImg from '@/assets/img/new_empty.png';
import { ItemBrowse } from './ItemBrowse';

import styles from './index.module.less';

export const Header = () => {
  const { data, error, loading } = useRequest<
    { browsing: { name: string; id: string }[] },
    []
  >(getBrowsingHistory_HotContent);

  return (
    <>
      <div className={styles['browsing']}>
        <div className={styles['browsing-title']}>我的浏览记录</div>

        <div>
          {loading ? (
            <Skeleton></Skeleton>
          ) : data?.data?.length > 0 ? (
            <div className={styles['browsing-list']}>
              {data?.data?.map?.((item, index) => {
                return (
                  <ItemBrowse
                    key={item.pathName + item.url}
                    text={item.pathName}
                    url={item.url}
                  />
                );
              })}
            </div>
          ) : (
            <Empty
              image={LegoNoDataImg}
              imageStyle={{ height: 90, marginTop: '30px' }}
              description="暂无数据"
            ></Empty>
          )}
        </div>
      </div>

      <div className={'legoList-content'}></div>
    </>
  );
};
