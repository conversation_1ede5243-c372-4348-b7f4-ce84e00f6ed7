// @ts-nocheck
import { useEffect, useState } from 'react';
import { Select, Spin } from '@blmcp/ui';
import { functionListData, pageListData } from '@/pages/lego/api/hubble';

let options = [];
const ViewSelect = (props) => {
  const { ...other } = props;
  const [optionsData, setOptionsData] = useState([]);
  const [loading, setLoading] = useState(false);

  const queryData = async () => {
    setLoading(true);
    const queryPageListData = pageListData({ pageSize: 9999, pageNum: 1 });
    const queryFKListData = functionListData({});
    await Promise.all([queryPageListData, queryFKListData])
      .then((res) => {
        const optionList: any = [];
        if (res[0] && res[0]?.code === 1 && res[0]?.data?.items) {
          res[0].data.items?.forEach((item) => {
            optionList.push({
              ...item,
              type: 'PK', // 页面是“PK”， 按钮“FK”
              label: `${item?.name}(${item?.pageKey})`,
              value: `${item?.pageKey}-PK`,
              valueId: item?.pageKey ?? '',
            });
          });
        }
        if (res[1] && res[1]?.code === 1 && res[1]?.data) {
          res[1].data?.forEach((item) => {
            optionList.push({
              ...item,
              type: 'FK', // 页面是“PK”， 按钮“FK”
              label: `${item?.name}(${item?.resourceKey})`,
              value: `${item?.resourceKey}-${item.name}`,
              valueId: item?.resourceKey ?? '',
            });
          });
        }
        setOptionsData(optionList);
        options = optionList;
        setLoading(false);
      })
      .catch((e) => {
        console.log(e);
        setLoading(false);
      });
  };

  useEffect(() => {
    if (!options.length) {
      queryData();
    } else {
      setOptionsData(options);
    }
  }, []);

  return (
    <Select
      allowClear
      mode="multiple"
      loading={loading}
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
      style={{ width: '320px' }}
      {...other}
    >
      {optionsData.map((item, index) => (
        <Select.Option
          key={index}
          {...item}
          value={item.value}
          label={item.label}
        >
          {item.label}
        </Select.Option>
      ))}
      {/* 添加下拉时的loading */}
      {loading && (
        <Select.Option
          disabled
          style={{ textAlign: 'center' }}
          value={'loading'}
        >
          <Spin spinning={loading} />
        </Select.Option>
      )}
    </Select>
  );
};

export default ViewSelect;
