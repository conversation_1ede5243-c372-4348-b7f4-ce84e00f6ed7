// @ts-nocheck
import { useEffect, useRef, useState } from 'react';
import {
  BLMTemplatePage,
  TemplatePageSchema,
} from '@blmcp/peento-businessComponents';
import { Modal, Button, Select, Spin, message } from '@blmcp/ui';
import request from '@/utils/request';
import { clearLocalPageId, getAllConfigList } from '@/pages/lego/utils';
import {
  getTemplateIncreaseQuantityList,
  publishReportState,
} from '@/pages/lego/api/hubble';
import schema from './schema';
import styles from './index.module.less';
import config from '@/tool/config';

interface CommonTableProps {
  tabKey: number;
  brandList: any[];
  defaultBrandName?: string;
}

const CommonTable = ({
  tabKey,
  brandList,
  defaultBrandName,
}: CommonTableProps) => {
  const tableRef = useRef<null | HTMLElement>(null);
  const currentRowKey = useRef('');
  const [selectBrands, setSelectBrands] = useState<number[]>([]);
  // const [brandList, setBrandList] = useState<any[]>([]);
  const [brandModalOpen, setBrandModalOpen] = useState(false);
  const routerBase = config.get('basicInfo.manageBaseRouter');
  const cuTemplateState = useRef(null);

  const openSelectBrandModal = (record: any) => {
    currentRowKey.current = record.id;
    setBrandModalOpen(true);
    // 先清空后获取关联
    setSelectBrands([]);

    getTemplateIncreaseQuantityList({ reportId: record.id })
      .then((res: any) => {
        setSelectBrands(
          (res?.data?.releaseTenants || [{ tenantId: 1 }]).map(
            (v: any) => v.tenantId,
          ),
        );
      })
      .catch(() => {
        setSelectBrands([1]);
      });
  };

  // 放量
  const setIncreaseQuantityBrand = () => {
    Modal.confirm({
      title: '确认操作',
      content: '即将把模板同步给已选的品牌，是否继续',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        publishReportState({
          reportId: currentRowKey.current,
          templateStatus: 3,
          modifiedTenants: selectBrands.map((v) => {
            const find = brandList.find((item) => item.value === v);
            return {
              tenantId: v,
              tenantName: find.label,
            };
          }),
        }).then((res) => {
          if (res.code === 1) {
            setBrandModalOpen(false);
            message.success('放量成功');
            tableRef?.current?.refreshTableList({}, false);
          }
        });
      },
    });
  };

  const getSchemaObj = () => {
    let schemaObj = schema(tableRef, tabKey, {
      openSelectBrandModal,
      defaultBrandName,
    });
    schemaObj.tableConfig.tableColumns =
      schemaObj?.tableConfig?.tableColumns?.filter((item) => {
        // 过滤掉show属性为false的对象
        if (!item.hasOwnProperty('show') || item?.show) {
          return item;
        }
      });
    return schemaObj;
  };

  useEffect(() => {
    const handleStorageChange = (ev) => {
      if (ev.key === 'lego-bi-update-reportName' && ev.newValue) {
        tableRef?.current?.refreshTableList({}, false);
      }
    };
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className={styles['commonTable']}>
      <BLMTemplatePage
        request={request}
        ref={tableRef}
        schema={getSchemaObj()}
        height={'calc(100vh - 185px)'}
        onBeforeFetch={(res) => {
          return {
            pageNum: res.pageNum,
            pageSize: res.pageSize,
            type: tabKey,
            q: res.q,
            releaseType: cuTemplateState.current,
          };
        }}
        tableActionsRight={
          <>
            <a
              target="_blank"
              href={`/${routerBase}/legoBI/edit?ownType=${
                tabKey === 1 ? 0 : 3
              }`}
            >
              <Button
                type="primary"
                onClick={() => {
                  clearLocalPageId();
                }}
              >
                新建{tabKey === 1 ? '报告' : '模板'}
              </Button>
            </a>
            {tabKey === 4 && (
              <div className="hb-search-list-templateState-select">
                <span>模板状态</span>
                <Select
                  allowClear
                  options={[
                    { label: '待发布', value: 0 },
                    { label: '已发布', value: 1 },
                    { label: '测试中', value: 2 },
                    { label: '放量中', value: 3 },
                    { label: '已回撤', value: 4 },
                    { label: '已全量', value: 5 },
                    { label: '已下线', value: 6 },
                  ]}
                  onChange={(val) => {
                    cuTemplateState.current = val;
                    tableRef?.current?.refreshTableList(
                      {
                        releaseType: val,
                        type: tabKey,
                      },
                      true,
                    );
                  }}
                ></Select>
              </div>
            )}
          </>
        }
      />
      {/* 放量组件 */}
      <Modal
        title="品牌放量"
        open={brandModalOpen}
        onOk={setIncreaseQuantityBrand}
        onCancel={() => setBrandModalOpen(false)}
      >
        <Spin spinning={false}>
          <div className={styles['brandChange']}>
            <span className="brandChange-span">放量品牌</span>
            <Select
              showSearch
              mode="multiple"
              placeholder="请选择"
              filterOption={(inputValue: string, option: any) =>
                option.label.indexOf(inputValue) > -1
              }
              value={selectBrands}
              options={brandList.map((v) => ({
                ...v,
                label: `${v.label}（${v.value}）`,
              }))}
              onChange={(value) => {
                setSelectBrands(value);
              }}
            />
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default CommonTable;
