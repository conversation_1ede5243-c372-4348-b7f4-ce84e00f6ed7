import { Tag } from '@blmcp/ui';
import dayjs from 'dayjs';
import {
  EXPORT_STATUS,
  APPROVAL_STATUS,
  APPROVAL_STATUS_LIST,
  APPROVAL_STYLE,
  EXPORT_LEGO_STATUS_LIST,
  APPROVAL_STATUS_STYLE,
} from './dict';
import './index.less';

export const getSchema = ({ btnSlot, operationWidth }) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'exportRecordsList',

    // 泛搜配置
    extensiveSearchConfig: {
      // 是否有泛搜能力
      hasExtensiveSearch: true,
      // 是否有操作按钮
      hasButtons: true,
      // 泛搜表单schema，同formily表单schema
      schema: {
        type: 'object',
        properties: {
          // 图表名称
          configName: {
            title: '图表名称',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '',
              allowClear: true,
            },
          },
          // 申请时间，可选范围近一年
          '[startDate,endDate]': {
            title: '申请时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker.RangePicker', // 使用的组件
            'x-component-props': {
              allowClear: true,
              placeholder: { [0]: '起始日期', [1]: '结束日期' },
              format: 'YYYY-MM-DD',
              disabledDate: (current) => {
                // 只能选择今天（含）往前推365天内的日期
                const today = dayjs().endOf('day');
                const start = dayjs().subtract(364, 'day').startOf('day');
                return current && (current < start || current > today);
              },
            },
          },
          // 审批状态
          approvalStatus: {
            title: '审批状态',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '全部',
              allowClear: true,
              style: { width: '120px' },
            },
            enum: APPROVAL_STATUS_LIST,
          },
          // 数据处理状态
          exportStatus: {
            title: '数据处理状态',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '全部',
              allowClear: true,
              style: { width: '120px' },
            },
            enum: EXPORT_LEGO_STATUS_LIST,
          },
        },
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chartexport/getExportRecordsV2',
      // 请求方式
      method: 'post',
      // 表格列配置
      tableColumns: [
        {
          title: '图表名称',
          key: 'elementTitle',
          dataIndex: 'elementTitle',
          width: 200,
        },
        {
          title: '报告名称',
          key: 'reportTitle',
          dataIndex: 'reportTitle',
          width: 400,
        },
        {
          title: '申请理由',
          key: 'description',
          dataIndex: 'description',
          width: 300,
        },
        {
          title: '查询条件',
          key: 'condition',
          dataIndex: 'condition',
          width: 300,
          render: (val) => {
            if (Array.isArray(val)) {
              return (
                <div>
                  {val.map((item, index) => (
                    <div key={index}>{item}</div>
                  ))}
                </div>
              );
            }
            return val || '- -';
          },
        },
        {
          title: '导出申请时间',
          key: 'createTime',
          dataIndex: 'createTime',
          width: 200,
          render: (val) => {
            return (val && dayjs(val).format('YYYY-MM-DD HH:mm:ss')) || '- -';
          },
        },
        {
          title: '审批状态',
          key: 'auditState',
          dataIndex: 'auditState',
          fixed: 'right',
          width: 100,
          render: (val) => (
            <Tag color={APPROVAL_STYLE[val]}>
              {APPROVAL_STATUS[val] || '无需审批'}
            </Tag>
          ),
        },
        {
          title: '数据处理状态',
          key: 'status',
          dataIndex: 'status',
          width: 150,
          fixed: 'right',
          render: (val) => (
            <div className="approval-status">
              <i
                className={`status-ball ${APPROVAL_STATUS_STYLE[val] || ''}`}
              />
              <span>{EXPORT_STATUS[val] || '- -'}</span>
            </div>
          ),
        },
        {
          title: '操作',
          key: 'condition',
          align: 'center',
          fixed: 'right',
          width: operationWidth || 150,
          render: (_, record) => {
            return btnSlot ? btnSlot(record) : null;
          },
        },
      ],
    },
  };
  return schema;
};
