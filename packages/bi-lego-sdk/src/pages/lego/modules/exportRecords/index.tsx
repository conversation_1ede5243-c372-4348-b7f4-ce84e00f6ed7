import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import {
  BLMTemplatePage,
  TemplatePageSchema,
  ITemplatePageRef,
} from '@blmcp/peento-businessComponents';
import { getSchema } from './schema';
import request from '@/utils/request';
import './index.less';
export interface ExportRecordsProps {
  btnSlot?: (record?: any) => React.ReactNode;
  extraParams?: any;
  height?: any;
  operationWidth?: Number;
}
export interface ExportRecordsRef {
  refreshTableList: (...args: any[]) => void;
}

const ExportRecords = forwardRef<ExportRecordsRef, ExportRecordsProps>(
  (props, ref) => {
    const exportRecordsPageRef = useRef<ITemplatePageRef>({});

    useImperativeHandle(ref, () => ({
      refreshTableList: (...args: any[]) => {
        exportRecordsPageRef.current?.refreshTableList?.(...args);
      },
    }));
    const handleBeforeFetch = (res) => {
      return { ...res, ...props.extraParams };
    };
    return (
      <>
        {/* @ts-ignore */}
        <BLMTemplatePage
          request={request}
          ref={exportRecordsPageRef}
          schema={
            getSchema({
              btnSlot: props?.btnSlot,
              operationWidth: props?.operationWidth,
            }) as TemplatePageSchema
          }
          onBeforeFetch={handleBeforeFetch}
          height={props.height}
        />
        <p className="export-tips">审批成功后：下载入口会保留7天，请及时下载</p>
      </>
    );
  },
);

export default ExportRecords;
