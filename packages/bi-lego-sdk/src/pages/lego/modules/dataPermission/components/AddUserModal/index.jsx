// 数据集权限授权弹窗
import React, {
  forwardRef,
  useState,
  useEffect,
  useImperativeHandle,
  useRef,
  useCallBack,
} from 'react';
import {
  Modal,
  Steps,
  Input,
  Space,
  Radio,
  Button,
  message,
  Tree,
  Spin,
} from '@blmcp/ui';
import { SearchOutlined } from '@ant-design/icons';
// import { number } from 'echarts';
import {
  queryUserListForAuthor,
  queryDomainAndDatasetsInfo,
  addDataSetsToUser,
} from '../../../../api';
import styles from './index.module.less';

const AddUserModal = ({
  modalOpenChange,
  modalOpen,
  userItem,
  permissionRef,
  updatePermissionSelected,
  permissionUserDataInit,
  permissionDataSetInit,
  userDataLoading,
  dataSetLoading,
}) => {
  const steps = [
    {
      title: '选择被授权人',
      // content: 'First-content',
    },
    {
      title: '配置数据集权限',
      // content: 'Second-content',
    },
  ];
  // 当前步骤
  const [current, setCurrent] = useState(0);
  // 授权人
  const [permissionUserData, setPermissionUserData] = useState(
    permissionUserDataInit ?? [],
  );
  // 当前选中授权人
  const [permissionUserDataSelected, setPermissionUserDataSelected] = useState({
    id: -1,
    userName: '',
  });
  // 数据集
  const [permissionDataSet, setPermissionDataSet] = useState(
    permissionDataSetInit ?? [],
  );
  // 当前选中数据集
  const [permissionDataSetSelected, setPermissionDataSetSelected] = useState(
    updatePermissionSelected,
  );
  // 授权人搜索框
  const [userInput, setUserInput] = useState('');
  // 数据集搜索框
  const [dataSetInput, setDataSetInput] = useState('');
  // radioMap
  const radioDataMap = {};

  const next = () => {
    if (permissionUserDataSelected.id !== -1) {
      setCurrent(current + 1);
    } else {
      message.error('请先选择被授权人');
    }
  };

  const prev = () => {
    setCurrent(current - 1);
  };

  const items = steps.map((item) => ({ key: item.title, title: item.title }));

  // 弹窗确认事件
  const handleUserModalOK = () => {
    if (permissionDataSetSelected?.length) {
      addDataSetsToUser({
        id: permissionUserDataSelected.id,
        datasets: permissionDataSetSelected,
      }).then((res) => {
        if (res && res.code === 1) {
          if (userItem.id !== -1) {
            message.success('修改成功');
          } else {
            message.success('添加成功');
          }
          // 添加&修改成功后重新刷新列表
          permissionRef?.current?.refreshTableList({}, false);
        }
      });
      modalOpenChange(false);
    } else {
      message.error('请选择数据集权限');
    }
  };

  // 授权人改变函数
  const handleUserChange = (e) => {
    const selectedItemValue = radioDataMap[e.target.value];
    setPermissionUserDataSelected({
      id: e.target.value,
      userName: selectedItemValue.userName,
    });
  };
  // 数据集改变函数
  const handleDataSetChange = (e, node) => {
    const checkedLeafKeys = node.checkedNodes
      .filter((item) => !item.children)
      .map((item) => item.id);
    setPermissionDataSetSelected(checkedLeafKeys);
  };

  // 弹窗内搜索函数
  const handleSearchClick = (e) => {
    // 授权人
    if (current === 0) {
      queryUserListForAuthor({ q: e, pageNum: 1, pageSize: 100 }).then(
        (res) => {
          if (res && res.data) {
            setPermissionUserData(res.data.items ?? []);
          }
        },
      );
    } else {
      // 数据集
      queryDomainAndDatasetsInfo({ q: e }).then((res) => {
        if (res && res.data) {
          setPermissionDataSet(res.data ?? []);
        }
      });
    }
  };
  useEffect(() => {
    // 如果有用户id,则自动跳到第二步，且第一步锁定授权人不能再选
    if (userItem.id !== -1) {
      setCurrent(1);
      setPermissionUserDataSelected({
        id: userItem.id,
        userName: userItem.userName,
      });
    }
  }, [userItem]);
  useEffect(() => {
    setPermissionDataSet(permissionDataSetInit);
    setPermissionUserData(permissionUserDataInit);
  }, [permissionUserDataInit, permissionDataSetInit]);
  useEffect(() => {
    setPermissionDataSetSelected(updatePermissionSelected);
  }, [updatePermissionSelected]);

  return (
    <>
      <div className={styles['permissionModal']}>
        <Steps current={current} items={items} />
        <div className={styles['userModal-content']}>
          {current === 1 && (
            <div className={styles['userModal-content_selected']}>
              {`已选人员：${permissionUserDataSelected.userName}`}
            </div>
          )}
          <div className={styles['userModal-content_tree']}>
            <div style={{ paddingRight: 10 }}>
              <Input
                prefix={
                  <SearchOutlined className={styles.SearchOutlinedIcon} />
                }
                style={{ marginBottom: '10px' }}
                value={current === 0 ? userInput : dataSetInput}
                placeholder={current === 0 ? '输入关键词搜索' : '搜索数据集'}
                onChange={(e) => {
                  current === 0
                    ? setUserInput(e.target.value)
                    : setDataSetInput(e.target.value);
                  handleSearchClick(e.target.value);
                }}
              />
            </div>
            <div className={styles['permissionContent']}>
              {current === 0 &&
                (!userDataLoading ? (
                  <Radio.Group
                    onChange={(e) => handleUserChange(e)}
                    value={permissionUserDataSelected.id}
                  >
                    <Space direction="vertical">
                      {permissionUserData.map((item) => {
                        radioDataMap[item.id] = item;
                        return (
                          <Radio key={item.id} value={item.id}>
                            {item.userName}
                          </Radio>
                        );
                      })}
                    </Space>
                  </Radio.Group>
                ) : (
                  <div className={styles['permissionContent-spin']}>
                    <Spin />
                  </div>
                ))}
              {current === 1 &&
                (!dataSetLoading ? (
                  <Tree
                    checkable
                    defaultCheckedKeys={updatePermissionSelected}
                    checkedKeys={permissionDataSetSelected}
                    height={600}
                    onCheck={(e, node) => handleDataSetChange(e, node)}
                    treeData={permissionDataSet}
                    fieldNames={{
                      title: 'name',
                      key: 'id',
                      children: 'children',
                    }}
                    blockNode
                  />
                ) : (
                  <div className={styles['permissionContent-spin']}>
                    <Spin />
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
      <div className={styles['userModal-footer']}>
        <Button
          style={{ margin: '0 8px' }}
          onClick={() => modalOpenChange(false)}
        >
          取消
        </Button>
        {current > 0 && (
          <Button
            disabled={userItem.id !== -1}
            style={{ margin: 0 }}
            onClick={() => prev()}
          >
            上一步
          </Button>
        )}
        {current < steps.length - 1 && (
          <Button type="primary" style={{ margin: 0 }} onClick={() => next()}>
            下一步
          </Button>
        )}
        {current === steps.length - 1 && (
          <Button
            type="primary"
            style={{ margin: '0 0 0 8px' }}
            onClick={() => handleUserModalOK()}
          >
            确定
          </Button>
        )}
      </div>
    </>
    // </Modal>
  );
};

export default AddUserModal;
