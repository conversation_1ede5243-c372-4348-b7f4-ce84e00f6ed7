import React, { useState, useRef, useEffect } from 'react';
import { Form, Button, Modal } from '@blmcp/ui';
import {
  BLMTemplatePage,
  TemplatePageSchema,
} from '@blmcp/peento-businessComponents';
import { message } from '@blmcp/ui';
import request from '@/utils/request';
import { blmAnalysisPageView } from '@/utils/eventTracking';
import {
  delDataSetsToUser,
  queryUserListForAuthor,
  queryDomainAndDatasetsInfo,
} from '../../api';
import { getSchema } from './schema';
import AddUserModal from './components/AddUserModal';
// @ts-expect-error
import styles from './index.module.less';

const Permission = (props) => {
  const [form] = Form.useForm();
  const permissionSearchValue = Form.useWatch('permissionSearchValue', form);

  const [userItem, setUserItem] = useState({ id: -1, userName: '' });
  const permissionRef = useRef(null);

  const [pageParams, setPageParams] = useState({
    page: 1,
    pageSize: 4,
    searchValue: permissionSearchValue,
  });
  // 弹窗状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 修改状态已选数据集
  const [updatePermissionSelected, setUpdatePermissionSelected] = useState([]);

  // 授权人数据
  const [permissionUserData, setPermissionUserData] = useState([]);
  // 数据集权限
  const [permissionDataSet, setPermissionDataSet] = useState([]);
  // 授权人数据loading
  const [userDataLoading, setUserDataLoading] = useState(false);
  // 数据集数据loading
  const [dataSetLoading, setDataSetLoading] = useState(false);

  // 点击删除报告
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: props?.indexs
        ? `您确认要重置该员工的数据集权限吗？重置之后，仅保留「${props?.indexs.join(
            '、',
          )}」数据集权限`
        : '您确认要重置该员工的数据集权限吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        delDataSetsToUser({ id: id }).then((res) => {
          if (res && res.code === 1) {
            message.success('重置成功');
            // 删除数据集之后重新调用列表接口
            // @ts-expect-error
            permissionRef?.current?.refreshTableList({}, false);
          }
        });
      },
    });
  };

  // 添加用户权限
  const addUserPermission = () => {
    // 清空用户ID
    setUserItem({ id: -1, userName: '' });
    // 清空数据集id
    setUpdatePermissionSelected([]);
    // 打开修改人员权限弹窗
    setIsModalOpen(true);
  };

  // 修改用户权限
  const updatePermission = (
    userId: number,
    userName: string,
    updatePermission: [],
  ) => {
    // 将用户ID传递给权限弹窗
    setUserItem({ id: userId, userName: userName });
    // 打开修改人员权限弹窗
    setIsModalOpen(true);
    // 若是修改进入，且有已选数据集，则对数据集做一个回填操作
    let updatePermissionNew: any[] = [];
    if (updatePermission?.length) {
      updatePermission.map((item) =>
        updatePermissionNew.push((item as any)?.tableId),
      );
    }
    setUpdatePermissionSelected(updatePermissionNew as any);
  };

  useEffect(() => {
    if (isModalOpen) {
      setDataSetLoading(true);

      if (userItem.id === -1) {
        // 获取授权人数据
        setUserDataLoading(true);
        queryUserListForAuthor({ q: '', pageNum: 1, pageSize: 100 })
          .then((res) => {
            setUserDataLoading(false);
            if (res && res.data) {
              setPermissionUserData(res.data.items ?? []);
            }
          })
          .catch(() => {
            setUserDataLoading(false);
          });
      }
      // 获取数据集数据
      queryDomainAndDatasetsInfo({ q: '' })
        .then((res) => {
          setDataSetLoading(false);
          if (res && res.data) {
            setPermissionDataSet(res.data ?? []);
          }
        })
        .catch(() => {
          setDataSetLoading(false);
        });
    }
  }, [isModalOpen]);
  useEffect(() => {
    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000348',
      eventId: 'e_leopard_cp_pv_00001108',
    });
  }, []);

  return (
    <div className={styles['permissionList']}>
      <BLMTemplatePage
        request={request}
        ref={permissionRef}
        schema={
          getSchema({ updatePermission, handleDelete }) as TemplatePageSchema
        }
        tableActionsRight={
          <Button type="primary" onClick={() => addUserPermission()}>
            添加人员权限
          </Button>
        }
        height={props.tableHeight || 'calc(100vh - 66px)'}
        // @ts-expect-error
        onBeforeFetch={(res, updateColumns) => {
          // console.log(res, updateColumns, 'jhhhhhh');
          return {
            pageNum: res.pageNum,
            pageSize: res.pageSize,
            q: res.q,
          };
        }}
      />

      {isModalOpen && (
        <Modal
          title={userItem.id !== -1 ? '修改人员权限' : '添加人员权限'}
          open={isModalOpen}
          footer={null}
          onCancel={() => setIsModalOpen(false)}
          closable={true}
          wrapClassName={styles['permissionModal']}
          maskClosable={false}
        >
          <AddUserModal
            modalOpenChange={(e: boolean) => setIsModalOpen(e)}
            modalOpen={isModalOpen}
            userItem={userItem}
            permissionRef={permissionRef}
            updatePermissionSelected={updatePermissionSelected}
            permissionUserDataInit={permissionUserData}
            permissionDataSetInit={permissionDataSet}
            userDataLoading={userDataLoading}
            dataSetLoading={dataSetLoading}
          />
        </Modal>
      )}
    </div>
  );
};

export default Permission;
