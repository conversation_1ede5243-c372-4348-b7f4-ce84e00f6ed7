import { Button } from '@blmcp/ui';
import { isLegoDev } from '@/utils/common';
// @ts-expect-error
import styles from './index.module.less';

export const getSchema = ({ updatePermission, handleDelete }) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'qbi-legoPermission',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'q',
        placeholder: '搜索姓名',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chart-manager/queryUserAndDataSetsList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '姓名/账号',
          dataIndex: 'userName',
          align: 'left',
          width: 150,
        },
        {
          title: '可创建表的数据集',
          dataIndex: 'dataSetList',
          align: 'left',
          width: 768,
          render: (text: any, record: any) => {
            if (record.dataSetList) {
              let dataSetArr = [] as any;
              record.dataSetList.map((items: any) => {
                return dataSetArr.push(items.name);
              });
              return <div>{dataSetArr.join('、')}</div>;
            } else {
              return <div>--</div>;
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 155,
          fixed: 'right',
          render: (
            text: any,
            record: {
              status: number;
              userId: any;
              userName: string;
              dataSetList: [];
            },
          ) => (
            <div className={styles['operation']}>
              <Button
                style={{ margin: '0 10px 0 0' }}
                type="link"
                onClick={() =>
                  updatePermission(
                    record.userId,
                    record.userName,
                    record.dataSetList,
                  )
                }
              >
                修改
              </Button>
              <Button
                style={{ margin: 0 }}
                type="link"
                onClick={() => handleDelete(record.userId)}
              >
                重置权限
              </Button>
            </div>
          ),
        },
      ],
    },
  };
  return schema;
};
