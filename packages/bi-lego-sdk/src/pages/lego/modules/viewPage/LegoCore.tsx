// share-url-hooks-disable-file
// @ts-nocheck
import { useEffect, useMemo, useState, useRef } from 'react';
import { buildComponents } from '@alilc/lowcode-utils';
import ReactRenderer from '@alilc/lowcode-react-renderer';
// import { injectComponents } from '@alilc/lowcode-plugin-inject';
// import { Loading } from '@alifd/next';
import { Button, Modal, message, Empty, Tooltip } from '@blmcp/ui';
import { useSize } from 'ahooks';
import { throttle, debounce } from 'lodash-es';
import { IPublicTypeRootSchema } from '@alilc/lowcode-types';
import { isLegoDev, getVariable } from '@/utils/common';
import UserTransfer from '@/pages/lego/modules/pageList/components/AddShareModal';
import { copyTemplateClick } from '@/pages/lego/utils/common';
// import VisibleFilter from '@/pages/lego/utils/visibleFilter';
import {
  reportBusinessMonitor,
  sceneActionStartMonitor,
  sceneActionEndMonitor,
  blmMicrofs,
} from '@/utils/eventTracking';
import { transformServerToSchema, registerResize, walkTree } from '../../utils';
import isMobile from '../../utils/isMobile';
import { getTextyWidthAndHeight } from '../../utils/css';
import { addShares, exchangeOwner, copyTemplate } from '../../api/index';
import { ComponentValue } from '../../hooks/useComponent';

import './index.less';
import '../editPage/info.css';
import { FDCell } from './layout/FDCell';
import { FDRow } from './layout/FDRow';
import './layout/style.scss';
import AddAiaToSchem from './AddAiaToSchem';
import { AssetLoader } from './AssetLoader';
import eventSdk from '@/tool/event';
import { routerBase } from '@/utils/common';
import { store as reportStore } from '@/tool/report';
// import ComponentEmpty from '@/pages/lego/components/ComponentEmpty';

declare global {
  interface Window {
    $baseRouter: any;
    $baseSelfReport: any;
    htmlStartTime: any;
    $routerBeforeEachTime: any;
  }
}

interface LoaderConfig {
  await: boolean;
  priority: number;
  urls: string[];
  package: string;
  library: string;
}

interface ViewProps {
  data: any; // 页面数据
  edit?: boolean; // 编辑按钮显示
  share?: boolean; // 分享按钮显示
  assets?: boolean; // 是否需要加载资源
  walkerComponents?: (
    item: IPublicTypeRootSchema,
    parent: IPublicTypeRootSchema,
    index: number,
    parents: IPublicTypeRootSchema[],
  ) => void; // 组件渲染
  libraryAssetCallback: (list: LoaderConfig[]) => void;
  aiAttribution?: boolean;
  handleSchema?: (schema: any) => void;
  titleSlot?: React.ReactNode;
  uuid: string;
  publishStatus: number;
  freezeFilterRegion?: boolean;
}

const ViewPage = (props: ViewProps) => {
  const {
    data,
    assets = false,
    edit = true,
    share = true,
    aiAttribution = true,
    libraryAssetCallback,
    publishStatus,
    freezeFilterRegion = true,
  } = props;
  const [reportSchema, setReportSchema] = useState({});
  const [isEmpty, setEmpty] = useState(false);
  const [actionList, setActionList] = useState<number[]>([]);
  const BoxRef = useRef<null | HTMLElement>(null);
  const size = useSize(BoxRef);
  const sizeTime = useRef<any>(null);
  const reportId = data?.data?.reportId || '';

  useEffect(
    debounce(() => {
      BoxRef.current?.classList.add('legoALLOverflowHidden');
      registerResize();
      clearTimeout(sizeTime.current);
      sizeTime.current = setTimeout(() => {
        BoxRef.current?.classList.remove('legoALLOverflowHidden');
      }, 300);
    }, 100),
    [size?.width],
  );

  // 分享&编辑弹窗
  const [modalView, setModalView] = useState({
    visible: false,
    type: '',
    reportId: '',
  });
  // 穿梭框内选中的值
  const [selectedKeys, setSelectedKeys] = useState<any>({});

  // 弹窗确认
  const handleOk = () => {
    if (
      (!actionList.includes(1) && selectedKeys?.targetKeys?.length) ||
      (actionList.includes(1) &&
        (selectedKeys?.originShareList?.length ||
          selectedKeys?.targetKeys?.length))
    ) {
      const searchParamsShare = {
        reportId: modalView.reportId,
        shareList: selectedKeys?.targetKeys ?? [],
        originShareList: selectedKeys?.originShareList,
      };
      const searchParamsTransfer = {
        reportId: modalView.reportId,
        transferId: selectedKeys?.targetKeys,
      };
      const queryApi = modalView.type === 'share' ? addShares : exchangeOwner;
      const searchParams =
        modalView.type === 'share' ? searchParamsShare : searchParamsTransfer;
      queryApi(searchParams).then((res) => {
        if (res && res?.code === 1) {
          message.success('分享成功');
        }
      });
      setModalView({ ...modalView, visible: false, type: '' });
    } else {
      message.error('请选择人员');
    }
  };

  // 关闭报告弹窗
  const handleCancel = () => {
    setModalView({ ...modalView, visible: false, type: '' });
  };

  // const reportId = new URLSearchParams(location.search.slice(1)).get(
  //   'reportId',
  // );
  const [reportName, setReportName] = useState('');

  // 是否为模板
  const [isTemplate, setTemplateState] = useState();

  // 测试
  useEffect(() => {
    (async () => {
      if (data) {
        eventSdk.dispatch('init', {
          reportId: data.data?.pageMark || data.data?.reportId,
        });

        const beforeSchemaTime =
          Date.now() - (window.$routerBeforeEachTime || window.htmlStartTime);
        // 转换权限list
        const _actionList = (
          typeof data.data?.actionList === 'string'
            ? data.data?.actionList.split(',')
            : data.data?.actionList || []
        ).map((v: string) => +v);
        setActionList(_actionList);

        // 报告名称
        setReportName(data?.data?.reportName);
        // 报告状态
        setTemplateState(data?.data?.ownType);

        blmMicrofs({
          legoPageMontTime: window.$legoPageMontTime,
          beforeSchemaTime,
          reportId: data?.data?.pageMark || data?.data?.reportId,
        });
        eventSdk.dispatch('loaded');

        // 查看判断
        if (
          data.code === 401 ||
          data.code === 173403 ||
          (data?.data?.actionList && !_actionList.includes(5))
        ) {
          message.error('暂无该报告查看权限，请联系管理员授权');
          if (window.$baseRouter) {
            window.$baseRouter.push(`/${routerBase()}/403`);
          }
          return () => {};
        } else if (data.code !== 1) {
          message.error(data.tips || data.msg);
          setEmpty(true);
          return () => {};
        }

        // 找不到 schema 处理
        if (!data?.data?.schema) {
          // message.error('请先发布后再查看');
          setEmpty(true);
          return () => {};
        }

        // 数据粒度影响筛选器
        // VisibleFilter.set(
        //   data.data.minAuthLevel,
        //   props.uuid,
        //   JSON.parse(data?.data?.schema),
        // );

        try {
          const {
            componentsMap: componentsMapArray,
            componentsTree,
            packages,
          } = await transformServerToSchema(
            data.data,
            function (_schema) {
              aiAttribution && AddAiaToSchem(_schema);
              if (props.walkerComponents) {
                walkTree(_schema.componentsTree, props.walkerComponents);
              }
              return _schema;
            },
            props.uuid,
          );

          if (props.handleSchema) {
            props.handleSchema({
              componentsMap: componentsMapArray,
              componentsTree,
              packages,
            });
          }

          const componentsMap: any = {};
          componentsMapArray.forEach((component: any) => {
            componentsMap[component.componentName] = component;
          });
          const pageSchema = componentsTree[0];

          const libraryMap: any = {};
          const libraryAsset: any = [];

          if (libraryAssetCallback) {
            libraryAssetCallback(packages);
          }

          packages?.forEach(
            ({
              package: _package,
              library,
              urls,
              renderUrls,
              await: _await,
              priority,
            }: any) => {
              libraryMap[_package] = library;
              if (_await || priority) {
                libraryAsset.push({
                  await: true,
                  url: renderUrls || urls,
                  priority,
                });
              } else if (renderUrls) {
                libraryAsset.push(renderUrls);
              } else if (urls) {
                libraryAsset.push(urls);
              }
            },
          );

          if (assets) {
            // sceneActionStartMonitor({
            //   sceneId: 'legoBI-loadAssets-warn',
            //   uniqueId: 0,
            //   maxTime: 6000 * 2,
            // });
            const assetLoader = new AssetLoader();

            // 资源包处理， 依赖复用
            // for (let i = libraryAsset.length - 1; i >= 0; i--) {
            //   const item = libraryAsset[i];
            //   const urls =
            //     Object.prototype.toString.call(item) === '[object Object]'
            //       ? item.url
            //       : item;
            //   if (dependencyCheck(urls)) {
            //     libraryAsset.splice(i, 1);
            //   }
            // }
            // console.log('libraryAsset', libraryAsset);
            await assetLoader.load(libraryAsset);
            // sceneActionEndMonitor({
            //   sceneId: 'legoBI-loadAssets-warn',
            //   uniqueId: 0,
            //   maxTime: 6000 * 2,
            // });
          }
          // @ts-expect-error
          const components = buildComponents(libraryMap, componentsMap);
          if (isMobile()) {
            // 处理布局兼容
            components.FDRow = FDRow;
            components.FDCell = FDCell;
          }
          setReportSchema({
            schema: pageSchema,
            components,
          });
        } catch (error) {
          reportBusinessMonitor(
            'legoBI-schema-error',
            { error, type: 'view' },
            'error',
          );
        }
      }
    })();
  }, [data]);

  useEffect(() => {
    // 哈勃下，添加高度 100% 类，达到高度撑开
    if (isLegoDev()) {
      document
        .getElementById('_overScrollWrap')
        ?.classList.add('hubble-qbi-height100');
    }
    return function () {
      setEmpty(false);
      // 哈勃下，添加高度 100% 类，达到高度撑开

      if (isLegoDev()) {
        setTimeout(() => {
          document
            .getElementById('_overScrollWrap')
            ?.classList.remove('hubble-qbi-height100');
        });
      }
    };
  }, [data]);

  const { schema, components } = reportSchema as any;
  const ComponentRendering = useMemo(() => {
    if (!data) return <></>;
    if (isEmpty) {
      return (
        <Empty
          imageStyle={{ marginTop: '80px' }}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂未创建任何内容"
        />
      );
    }
    return (
      <ReactRenderer
        className="lowcode-plugin-sample-preview-content"
        schema={schema}
        components={components}
        // faultComponent={ComponentEmpty}
        // notFoundComponent={ComponentEmpty}
      />
    );
  }, [schema, components, isEmpty, data]);
  let isTittleNeedToolTip = false;
  if (
    getTextyWidthAndHeight(
      reportName,
      {
        'font-size': isMobile() ? '18px' : '15px',
        'font-weight': 500,
        'word-break': 'break-all',
      },
      'span',
    ).width > (isMobile() ? document.body.clientWidth - 20 : 300)
  ) {
    isTittleNeedToolTip = true;
  }

  return (
    <div
      className={
        'lowcode-plugin-sample-preview' +
        ` legoReport${props.uuid}` +
        (isLegoDev() ? ' isHubble' : '') +
        (isMobile() ? ' mobile' : '')
      }
      style={{ height: freezeFilterRegion ? '100%' : 'auto' }}
      ref={BoxRef}
    >
      <div className="lowcode-plugin-sample-header">
        <h1>
          {isTittleNeedToolTip ? (
            <Tooltip title={reportName}>{reportName}</Tooltip>
          ) : (
            reportName
          )}
        </h1>
        {/* <span style={{ flex: 1 }}></span> */}
        <div>
          {props.titleSlot}
          {isLegoDev() && !isMobile() && (
            <Button
              onClick={() => {
                copyTemplateClick(
                  { id: reportId },
                  publishStatus,
                  isTemplate ? '模板' : '报告',
                );
              }}
            >
              复制{isTemplate ? '模板' : '报告'}
            </Button>
          )}
          {!isLegoDev() &&
            share &&
            !isMobile() &&
            !(isTemplate || !actionList.includes(2) || publishStatus === 0) && (
              <Button
                onClick={() => {
                  setModalView({
                    visible: true,
                    type: 'share',
                    reportId: reportId,
                  });

                  eventSdk.dispatch('templateShare', {
                    reportId: reportId,
                    reportType: 'view',
                  });
                }}
              >
                分享
              </Button>
            )}
          {!isMobile() && actionList.includes(1) && edit && (
            <Button
              style={{ marginLeft: '10px' }}
              type="primary"
              onClick={throttle(
                () => {
                  // 哈勃下不能调用，有专门的复制按钮
                  if (isTemplate && !isLegoDev()) {
                    // TODO copy函数调用
                    copyTemplate(reportId as string).then((res) => {
                      if (res?.code === 1) {
                        eventSdk.dispatch('templateCopy', {
                          reportId: reportId,
                          reportName,
                          copyId: res?.data?.reportId,
                        });
                        setTimeout(() => {
                          const href = `/${routerBase()}/legoBI/edit?reportId=${
                            res?.data?.reportId
                          }&overallLayoutShow=false`;
                          location.href = href;
                        });
                      }
                    });
                  } else {
                    eventSdk.dispatch('templateEdit', {
                      reportId: reportId,
                    });
                    setTimeout(() => {
                      const href = `/${routerBase()}/legoBI/edit?reportId=${reportId}&overallLayoutShow=false`;
                      location.href = href;
                    });
                  }

                  // if (window.$baseRouter) {
                  //   window.$baseRouter.push(href);
                  // } else {
                  //   location.href = href;
                  // }
                },
                2000,
                { trailing: false },
              )}
            >
              编辑
            </Button>
          )}
        </div>
      </div>
      <div
        className="lowcode-plugin-sample-box"
        style={{
          background: isEmpty ? '#fff' : 'transparent',
          overflow: freezeFilterRegion ? 'auto' : 'initial',
        }}
      >
        {ComponentRendering}
      </div>
      {modalView.visible && (
        <Modal
          title={modalView.type === 'share' ? '分享报告' : '转让报告'}
          open={modalView.visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={modalView.type === 'share' ? 720 : 560}
          style={{ maxHeight: '70vh' }}
          closable={true}
          maskClosable={false}
        >
          <UserTransfer
            modalView={modalView}
            onModalOk={(e: any) => setSelectedKeys(e)}
            tabsType={actionList.includes(1) ? 1 : 2}
          />
        </Modal>
      )}
    </div>
  );
};

export default ViewPage;
