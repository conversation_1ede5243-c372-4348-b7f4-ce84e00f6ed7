import React, { forwardRef } from 'react';
import { wrapReactClass } from '@alilc/lowcode-utils';

export const FDRow = forwardRef(({ children, className, isPartialContainerFilter }, ref) => {
  const isLegoDefaultRow = className && className?.indexOf('legoDefaultRow') !== -1 || isPartialContainerFilter;

  const filterChildren = children?.filter((f) => f.props?.style?.display !== 'none') || [];
  const childrenLength  = filterChildren.length;
  let content = children;
  if (isLegoDefaultRow) {
    content = (
      <div className={`lego-default-row-wrapper ${childrenLength > 4 ? 'show-expand' : 'hide-expand'}`}  style={{display: childrenLength === 0 ? 'none' : 'block'}}>
        <div className='lego-default-row-filter-wrapper' style={{display: childrenLength > 1 ? 'grid' : 'none'}}>{children?.slice(0, -1)}</div>
        {children?.[children?.length - 1]}
      </div>
    )
  }
  return (
    <div className={`${className || ''} m-fd-row mobile`} ref={ref}>
      {content}
    </div>
  )
})

export default wrapReactClass(FDRow)
