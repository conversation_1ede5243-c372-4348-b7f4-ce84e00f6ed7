import LegoRenderer from './LegoRender';

export default function (props) {
  const reportId: string =
    new URLSearchParams(location.search.slice(1)).get('reportId') || '';

  return (
    <LegoRenderer
      reportId={reportId}
      edit={true}
      share={true}
      handleSchema={props.handleSchema}
      titleSlot={props.titleSlot}
      customAgreement={props.customAgreement}
    ></LegoRenderer>
  );
}
