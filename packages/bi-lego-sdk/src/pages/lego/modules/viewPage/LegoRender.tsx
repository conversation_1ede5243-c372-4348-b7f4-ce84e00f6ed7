import { Spin } from '@blmcp/ui';
import React, { useEffect, useState } from 'react';
import { IPublicTypeRootSchema } from '@alilc/lowcode-types';
import { getPageStructure } from '@/pages/lego/api';
import LegoCore from './LegoCore';
import { getCurrentPageStatus } from '@/pages/lego/utils';
import configSdk from '@/tool/config';
import { isLegoDev, getVariable } from '@/utils/common';
import { useLegoReport, store as reportStore } from '@/tool/report';
import { store as componentStore } from '../../hooks/useComponent';
import queryCenter from '@/pages/lego/libraryMaterials/module/Query';
import relationCenter from '@/pages/lego/libraryMaterials/module/RelationCenter';
import LinkageCenter from '@/pages/lego/libraryMaterials/module/Linkage';
import { clearContext } from '@/utils/store';

const filterNameMap: any = {
  CityFilter: 'adcode',
  CapacityCompanyFilter: 'car_team_id',
  DateFilterGlobal: 't_date',
};

const newFilterNameMap: any = {
  NewCityFilter: 'adcode',
  NewCarTeamFilter: 'car_team_id',
  DateFilterGlobal: 't_date',
  NewFleetFilter: 'fleet_id',
};

declare global {
  interface Window {
    legoRequest: any;
    antd: any;
    dayjs: any;
    PropTypes: any;
    ReactDom: any;
  }
}

interface ViewProps {
  reportKey?: string;
  reportId?: string;
  edit?: boolean;
  share?: boolean;
  filterProps?: {
    [key: string]: {
      defaultValue?: number[] | string[];
      disabled?: boolean;
      hidden?: boolean;
      clearable?: boolean;
      options?: { label: string; value: any }[];
    };
  };
  walkerComponents?: (
    item: IPublicTypeRootSchema,
    parent: IPublicTypeRootSchema,
    index: number,
    parents: IPublicTypeRootSchema[],
  ) => void; // 组件渲染
  dependentAssets?:
    | string[]
    | {
        package?: string;
        library?: string;
        urls: string[];
        version?: string;
        await?: boolean;
        priority?: number;
      }[];
  aiAttribution?: boolean;
  handleSchema?: (schema: any) => void;
  titleSlot?: React.ReactNode;
  customAgreement?: {
    agreementName: string;
    agreementLink: string;
  };
}

function clearAll(reportId, type) {
  // 销毁时候才清除报告的状态（初始依赖绑定在清除前面，会引发问题）
  if (type === 'unmount') {
    reportStore.delete(reportId);
  }

  componentStore.conditionDelete('uuid', reportId); // 状态
  LinkageCenter(reportId)?.clear(); // 关联
  queryCenter(reportId)?.clear(); // 查询参数
  relationCenter(reportId)?.clear(); // 事件
  getVariable('__lego_showTipsSet__')?.[reportId]?.clear?.();

  // 报告参数
  const filterContext = getVariable('__lego__bi_filterContext__') || {};
  if (filterContext[reportId]) {
    clearContext(filterContext[reportId]);
    delete filterContext[reportId];
  }
}

export default function (props: ViewProps) {
  const {
    reportKey,
    reportId,
    edit = false,
    share = false,
    filterProps = {},
    dependentAssets = [],
    aiAttribution = true,
  } = props;
  const uuid = (reportKey as string) || (reportId as string);
  const reportOperateConfig = configSdk.get('sdkConfig.reportOperateConfig');
  const publishStatus = getCurrentPageStatus();

  const [data, setData] = useState<any>();
  const [pageMark, setPageMark] = useState('');
  const [reportMeta] = useLegoReport(uuid);

  useEffect(() => {
    clearAll(uuid, 'mount');
    setData(null);
    setPageMark('');

    getPageStructure({
      reportId,
      pageMark: reportKey,
      publishStatus,
    })
      .then((res: any) => {
        setPageMark(res?.data?.pageMark);
        setData(res as any);
      })
      .catch((res) => {
        setData(res as any);
      });

    return function () {
      clearAll(uuid, 'unmount');
    };
  }, [reportKey, reportId]);

  useEffect(() => {
    if (props.customAgreement) {
      reportStore.merge(uuid, {
        customAgreement: props.customAgreement,
      });
    }
  }, [props.customAgreement]);

  if (!data) {
    return <Spin size="large" className="lego-page-loading" />;
  }

  return (
    <LegoCore
      uuid={uuid}
      key={reportMeta.sensitiveState ? 1 : 0}
      publishStatus={publishStatus}
      assets={true}
      libraryAssetCallback={(packages: any[]) => {
        packages.unshift(...dependentAssets);
      }}
      {...props}
      data={data}
      edit={
        isLegoDev() ||
        (edit &&
          reportOperateConfig.includes('edit') &&
          data?.data?.editable !== 0)
      }
      share={share && reportOperateConfig.includes('share')}
      aiAttribution={aiAttribution}
      walkerComponents={(item: any, parent: any, ...arg) => {
        if (item.componentName.includes('Filter')) {
          const key =
            filterNameMap[item.componentName] ||
            newFilterNameMap[item.componentName] ||
            item.props?.dataSetConfig?.dimensionInfo?.[0]?.key ||
            item.props?.dataSetConfig?.measureInfo?.[0]?.key;
          const setting = filterProps[key];
          if (setting) {
            const { hidden, clearable, ...other } = setting;
            item.props = {
              ...(item.props || {}),
              ...other,
            };
            componentStore.merge(item.id, {
              uuid,
              hidden: !!hidden,
              noRender: !!clearable,
            });
          }
        }

        // TODO 页面逻辑， 营销隐藏编辑器后可以去除
        // if (
        //   pageMark?.includes?.('financeBusinessAnalysis') &&
        //   item.componentName === 'CapacityCompanyFilter'
        // ) {
        //   parent.props.style.display = 'none';
        // }
        // END TODO

        props?.walkerComponents?.(item, parent, ...arg);
      }}
      handleSchema={props.handleSchema}
      titleSlot={props.titleSlot}
    ></LegoCore>
  );
}
