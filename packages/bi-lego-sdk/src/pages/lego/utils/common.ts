/**
 *  存放乐高公共业务方法
 */
// @ts-nocheck
import { Modal } from '@blmcp/ui';
import { throttle } from 'lodash-es';
import { copyTemplate } from '../api/hubble';
import { routerBase } from '@/utils/common';

// 复制模版
export const copyTemplateClick = throttle(
  (record: any, source: number, str = '报告') => {
    Modal.confirm({
      title: '确认操作',
      content: `即将复制${str}副本并打开新窗口，是否继续？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        copyTemplate({ reportId: record.id, source }).then((res) => {
          if (res?.code === 1) {
            window.open(
              `/${routerBase()}/legoBI/edit?reportId=${res?.data?.reportId}`,
              '_blank',
            );
          }
        });
      },
      onCancel: () => {},
    });
  },
  1000,
  { trailing: false },
);
