import {
  getCurrentPageId,
  isDesign,
  getProjectSchema,
  walkTree,
  getNodeById,
} from '@/pages/lego/utils';
import { store as reportStore } from '@/tool/report';
import { store as componentStore } from '@/pages/lego/hooks/useComponent';
import linkageCenterExp from '../libraryMaterials/module/Linkage';

const filterNumMap: { [key: string]: number } = {
  adcode: 1,
  car_team_id: 2,
  fleet_id: 3,
};

interface Props {
  [key: string]: {
    key: string;
    visible: boolean;
  };
}

class VisibleFilter {
  // @ts-expect-error
  hideNum: number;
  constructor() {}
  set(
    _level: number,
    reportUid: string = getCurrentPageId() as string,
    _schema?: any,
  ) {
    const level = _level || 4;
    let cityId, carTeamId, fleetId;

    const schema =
      _schema?.componentsTree || getProjectSchema()?.componentsTree || [];
    walkTree(schema, (item: any) => {
      if (
        item.componentName === 'NewCityFilter' ||
        item.componentName === 'DjCityFilter' ||
        item.componentName === 'CityFilter'
      ) {
        cityId = item.id;
      } else if (
        item.componentName === 'NewCarTeamFilter' ||
        item.componentName === 'DjFleetFilter' ||
        item.componentName === 'CapacityCompanyFilter'
      ) {
        carTeamId = item.id;
      } else if (item.componentName === 'NewFleetFilter') {
        fleetId = item.id;
      }
    });

    if (isDesign()) {
      if (cityId) {
        getNodeById(cityId).parent.setVisible(level > 1);
      }
      if (carTeamId) {
        getNodeById(carTeamId).parent.setVisible(level > 2);
      }
      if (fleetId) {
        getNodeById(fleetId).parent.setVisible(level > 3);
      }
    }

    reportStore.merge(reportUid, {
      legoMinAuthLevel: level,
    });
    cityId &&
      componentStore.merge(cityId, {
        uuid: reportUid,
        // hidden: level <= 1,
        noRender: level <= 1,
      });
    carTeamId &&
      componentStore.merge(carTeamId, {
        uuid: reportUid,
        // hidden: level <= 2,
        noRender: level <= 2,
      });
    fleetId &&
      componentStore.merge(fleetId, {
        uuid: reportUid,
        // hidden: level <= 3,
        noRender: level <= 3,
      });

    // 设置在window 上，方便组件内部使用
    // @ts-expect-error
    window.legoMinAuthLevel = level;

    const linkageCenter = linkageCenterExp(reportUid);
    // 设置组件数据源
    linkageCenter.notify(
      'setCarTeamFilterKind',
      level > 3 ? 'link' : 'authOpen',
    );
    linkageCenter.notify('setCityFilterKind', level > 2 ? 'link' : 'authOpen');

    // if (!reportUid) {
    //   reportUid = getCurrentPageId() as string;
    //   const wind = getDesignWindow()?.document.querySelector(`.lce-page`);
    //   if (wind) {
    //     // @ts-expect-error
    //     wind!.parentNode!.setAttribute('id', 'legoReport' + reportUid);
    //   }
    // }

    // this.run(this.toProps(level), level, reportUid);
  }
}

export default new VisibleFilter();
