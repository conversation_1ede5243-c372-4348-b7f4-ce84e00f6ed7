import { setVariable } from '@/utils/common';
import { store } from '../hooks/useComponent';

interface keyValue {
  [key: string]: any;
}

interface GetOption {
  expire?: boolean;
  await?: boolean;
  weakMatch?: boolean;
}

export default class Cache {
  relationshipMap: keyValue;
  cacheMap: keyValue;
  awaitMap: keyValue;
  constructor() {
    this.cacheMap = {};
    this.relationshipMap = {};
    this.awaitMap = {};
  }

  concat(keys: string[]) {
    return keys.join('_');
  }

  createKey(keys: string[]) {
    // 创建前先删除老数据， 防止获取异步数据出现bug
    this.deleteByIndexId(keys[0]);
    const key = this.concat(keys);
    this.relationshipMap[keys[0]] = keys;
    return key;
  }

  add(keys: string[], data: any) {
    const key = this.createKey(keys);
    this.cacheMap[key] = {
      time: Date.now(),
      data,
    };
    // 如果等待数据有当前key， 需要通知get方法
    if (this.awaitMap[keys[0]]?.state === 'pending') {
      this.awaitMap[keys[0]].resolve(data);
      delete this.awaitMap[keys[0]];
    }
  }

  setRelationship(keys: string[]) {
    this.relationshipMap[keys[0]] = keys;
  }

  get(keys: string[], option?: GetOption): any {
    const { expire = true, weakMatch = false } = option || {};
    const key = this.concat(keys);
    const data = this._getCacheMap(key, weakMatch);

    if (data) {
      // 过期时间为false 或者 true 要验证过期时间 才返回数据
      if ((expire && Date.now() - data.time < 1000 * 60 * 60) || !expire) {
        return data.data;
      }
    } else if (option?.await) {
      // 没取到数据可等待获取数据
      return this._createPromise(keys[0]).promise;
    }
  }

  delete(keys: string[]) {
    const key = this.concat(keys);
    delete this.relationshipMap[keys[0]];
    delete this.awaitMap[key];
    return delete this.cacheMap[key];
  }

  deleteByIndexId(id: string) {
    const key = this.relationshipMap[id] || [];
    return this.delete(key);
  }

  getDataByIndexId(id: string, ...arg: any) {
    const key = this.relationshipMap[id] || [id];
    return this.get(key, ...arg);
  }

  clear(type?: number | number[]) {
    // 有 type 则按组件类型进行删除
    if (type !== undefined) {
      const types = typeof type === 'number' ? [type] : type;
      Object.keys(this.relationshipMap).forEach((id) => {
        if (types.includes(store.get(id)?.componentType)) {
          this.deleteByIndexId(id);
        }
      });
    } else {
      this.cacheMap = {};
      this.relationshipMap = {};
      this.awaitMap = {};
    }
  }

  _createPromise(key: string) {
    this.awaitMap[key] = {};
    const promise = new Promise((resolve, reject) => {
      this.awaitMap[key].state = 'pending';
      // 等待数据获取完成，resolve
      this.awaitMap[key].resolve = (arg: any) => {
        this.awaitMap[key].state = 'resolved';
        resolve(arg);
      };
      // 等待数据获取失败，reject
      this.awaitMap[key].reject = (arg: any) => {
        this.awaitMap[key].state = 'rejected';
        reject(arg);
      };
    });
    this.awaitMap[key].promise = promise;
    return this.awaitMap[key];
  }

  _getCacheMap(key: string, vague: boolean = false) {
    // 完全匹配不到才进行模糊匹配
    if (this.cacheMap[key]) {
      return this.cacheMap[key];
    }

    if (vague) {
      for (let k in this.cacheMap) {
        if (k.startsWith(key)) {
          return this.cacheMap[k];
        }
      }
    }

    return this.cacheMap[key];
  }
}

export const globalCache = setVariable('__lego__bi_globalCache', new Cache());
