export const isNull = (value: any) => Object.prototype.toString.call(value) === '[object Null]'
export const isUndefined = (value: any) => Object.prototype.toString.call(value) === '[object Undefined]'
export const isArray = (value: any) => Object.prototype.toString.call(value) === '[object Array]'
// 判断数据是否是一个有效值，不能是null也不能是undefined
export const isValueValid = (value: any) => !isNull(value) && !isUndefined(value);
