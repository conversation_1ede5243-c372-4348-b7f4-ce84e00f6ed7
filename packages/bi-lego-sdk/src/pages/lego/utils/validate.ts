export const validateText = (text: string, type: 1 | 2 = 1) => {
  const regH1 = '^[\u4e00-\u9fa5A-Za-z0-9_（）()]+$'; // 中英文、数字、下划线、中英文括号
  const regH2 = '^[\u4e00-\u9fa5A-Za-z]+$';
  let regHg = new RegExp(type === 1 ? regH1 : regH2);
  return regHg.test(text);
};

export const isValidName = (text: string) => validateText(text);

export const isValidNewFieldName = (text: string) => validateText(text, 2);
