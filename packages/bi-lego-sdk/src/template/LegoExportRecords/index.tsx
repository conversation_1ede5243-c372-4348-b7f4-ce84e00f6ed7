import React, { forwardRef } from 'react';
import Wrapper from '../Wrapper';
import ExportRecords, {
  ExportRecordsProps,
  ExportRecordsRef,
} from '@/pages/lego/modules/exportRecords/index';

const WrappedExportRecords = Wrapper(ExportRecords);

const LegoExportRecords = forwardRef<ExportRecordsRef, ExportRecordsProps>(
  (props, ref) => {
    return <WrappedExportRecords {...props} ref={ref} />;
  },
);

export default LegoExportRecords;
export {
  ExportRecordsRef,
  ExportRecordsProps,
} from '@/pages/lego/modules/exportRecords/index';
