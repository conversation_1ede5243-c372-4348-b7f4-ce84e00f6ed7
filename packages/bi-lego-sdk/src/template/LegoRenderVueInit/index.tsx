import Wrapper from '../Wrapper';
import LegoRender from '@/pages/lego/modules/viewPage/LegoRender';
import { ReactInVue } from 'vuera';
import * as BlmUtils from 'blm-utils';
import PropTypes from 'prop-types';

!window.PropTypes && (window.PropTypes = PropTypes);

// @ts-expect-error
!window.BlmUtils && (window.BlmUtils = BlmUtils);

const Component = function (props) {
  return (
    <LegoRender
      dependentAssets={[
        {
          package: 'antd',
          library: 'antd',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/antd-umdV5.16.0-440b813236.css',
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/antd-umdV1.11.10-13c788ee66.js',
          ],
        },
        {
          package: 'antdIcons',
          library: 'antdIcons',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/prerelease/cp/js/antdIcons-umdV5.5.1-05b7122c56.js',
          ],
        },
        {
          package: 'blmcpUi',
          library: 'blmcpUi',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/blmcpUi-umdV1.1.25-5f604abc13.css',
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmcpUi-umdV1.1.25-469848c040.js',
          ],
        },
        {
          package: 'blmRequest',
          library: 'blmRequest',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmRequest-umdV0.0.21-rc.0-cf8f44cce4.js',
          ],
        },
        {
          package: 'blmMobileLib',
          library: 'blmMobileLib',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/prerelease/cp/js/blmMobileLib-umdV1.1.1-rc.1-540f381ad9.js',
          ],
        },
        {
          package: 'blmBusinessComponents',
          library: 'blmBusinessComponents',
          urls: [
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/prerelease/cp/css/blmBusinessComponents-umdV0.0.95-rc.3-518a0fed2b.css',
            'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/prerelease/cp/js/blmBusinessComponents-umdV0.0.95-rc.3-26452da873.js',
          ],
        },
      ]}
      {...props}
    />
  );
};
export default ReactInVue(Wrapper(Component));
