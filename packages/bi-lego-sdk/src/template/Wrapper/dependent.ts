import dayjs from 'dayjs';
import React from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import * as lodash from 'lodash-es';
import * as antd from 'antd';
import * as blmcpUi from '@blmcp/ui';
import * as blmBusinessComponents from '@blmcp/peento-businessComponents';
import * as mobileLib from '@blmcp/ui-mobile';

import localeData from './dayjs_locale';
localeData(undefined, dayjs, dayjs);

!window.dayjs && (window.dayjs = dayjs);
!window.lodash && (window.lodash = lodash);
!window.React && (window.React = React);
!window.ReactDOM && (window.ReactDOM = window.ReactDom || ReactDOM);
!window.PropTypes && (window.PropTypes = PropTypes);
!window.antd && (window.antd = antd);
!window.blmcpUi && (window.blmcpUi = blmcpUi);
!window.blmBusinessComponents &&
  (window.blmBusinessComponents = blmBusinessComponents);
!window.mobileLib && (window.mobileLib = mobileLib);
