{"compilerOptions": {"allowJs": true, "baseUrl": ".", "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": false, "jsx": "preserve", "lib": ["DOM", "ESNext"], "moduleResolution": "node", "paths": {"@/*": ["./src/*"], "@/assets": ["./src/assets"]}, "noImplicitAny": false, "resolveJsonModule": true, "rootDir": "src", "skipLibCheck": true, "strict": true}, "exclude": ["**/node_modules"], "include": ["src", "window.d.ts"]}