const PageScanPlugin = require('router-scan-bigbang/plugin/pageScanPlugin')
const FunScanPlugin = require('router-scan-bigbang/plugin/funScanPlugin')
const RouterScanPlugin = require('router-scan-bigbang/plugin/routerScanPlugin')
const InterfaceScanPlugin = require('router-scan-bigbang/plugin/interfaceScanPlugin')
const virtualRouters = require('./routes/virtualRouters.js')
const path = require('path')
const execSync = require('child_process').execSync
const BRANCH_NAME = execSync('git rev-parse --abbrev-ref HEAD')
  .toString()
  .replace(/\s+/, '');
const VERSION = BRANCH_NAME.split('/')[1];
const PROJECTNAME = 'qbi'
module.exports = {
  outPath: `dist/${PROJECTNAME}/${VERSION}/`,
  platform: 'leopard-web-qbi',
  entry: './routes/index.ts',
  routerCur: path.resolve('src/pages'),
  routerNameKey: 'name',
  routerPathKey: 'path',
  routerRkKey: 'pageKey',
  exclude: ['/src/api/lego/api/hubble.ts'],
  plugins: [
    new PageScanPlugin(),
    new FunScanPlugin(),
    new RouterScanPlugin(),
    new InterfaceScanPlugin()
  ],
  otherRouteDependent: {
    'legoReportEditBI': ['./src/pages/lego/libraryMaterials/index.tsx'],
    'legoReportViewBI': ['./src/pages/lego/libraryMaterials/index.tsx'],
  },
  virtualRouters: virtualRouters
}
