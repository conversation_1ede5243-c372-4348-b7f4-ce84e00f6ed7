import React, { useRef } from 'react';
import { LegoExportRecords } from 'src';
import { Button, Tag } from '@blmcp/ui';
import { ExportRecordsRef } from 'src/pages/lego/modules/exportRecords';

export const 导出记录 = () => {
  const exportRecordsPageRef = useRef<ExportRecordsRef>(null);
  const handleClick = () => {
    console.log('exportRecordsPage.current:', exportRecordsPageRef);
    exportRecordsPageRef?.current?.refreshTableList({}, false);
  };
  return (
    <>
      <LegoExportRecords
        ref={exportRecordsPageRef}
        btnSlot={(records: any) => (
          <Button onClick={handleClick}>{records.elementTitle}</Button>
        )}
      />
    </>
  );
};

export default {
  title: '导出记录',
};
