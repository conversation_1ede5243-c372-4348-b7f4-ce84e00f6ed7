import type { StorybookConfig } from '@modern-js/storybook';

const config: StorybookConfig = {
  stories: ['../stories/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: ['@storybook/addon-essentials'],
  framework: {
    name: '@modern-js/storybook',
    options: {
      bundler: 'webpack',
    },
  },
  webpackFinal: async (config) => {
    // 忽略特定的依赖
    config.externals = config.externals || {};
    config.externals['react'] = 'var window.React';
    config.externals['react-dom'] = 'var window.ReactDOM';
    config.externals['prop-types'] = 'PropTypes';
    config.externals['lodash'] = 'var window._';
    config.externals['@alilc/lowcode-engine'] = 'var window.AliLowCodeEngine';
    config.externals['@alilc/lowcode-editor-skeleton'] =
      'var window.AliLowCodeEngine.common.skeletonCabin';
    return config;
  },
};

export default config;
